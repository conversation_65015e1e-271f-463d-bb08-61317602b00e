// 校园地图系统 - 配置管理模块
// 统一管理所有配置参数，避免重复定义

// 全局错误处理 - 过滤浏览器扩展错误
if (!window.extensionErrorHandlerAdded) {
    window.addEventListener('error', (event) => {
        // 忽略来自浏览器扩展的错误
        if (event.filename && (
            event.filename.includes('content_scripts') ||
            event.filename.includes('extension') ||
            event.filename.includes('chrome-extension') ||
            event.filename.includes('moz-extension') ||
            event.filename.includes('safari-extension')
        )) {
            console.warn('🔇 已忽略浏览器扩展错误:', event.message);
            event.preventDefault();
            return false;
        }

        // 忽略特定的扩展相关错误消息
        if (event.message && (
            event.message.includes('handleHotKey') ||
            event.message.includes('toUpperCase') ||
            event.message.includes('content_scripts') ||
            event.message.includes('Cannot read properties of undefined')
        )) {
            console.warn('🔇 已忽略可能的扩展错误:', event.message);
            event.preventDefault();
            return false;
        }
    });

    window.extensionErrorHandlerAdded = true;
}

// 动态配置管理
const DynamicConfig = {
    serverPort: 3002,
    baseUrl: 'http://localhost:3002',

    // 初始化配置 - 从服务器获取实际端口
    async init() {
        try {
            // 尝试从多个可能的端口获取配置
            const ports = [3002, 3003, 3004, 3005, 3006, 3007, 3008, 3009, 3010];

            for (const port of ports) {
                try {
                    const response = await fetch(`http://localhost:${port}/api/config`, {
                        method: 'GET',
                        timeout: 2000
                    });

                    if (response.ok) {
                        const config = await response.json();
                        this.serverPort = config.port;
                        this.baseUrl = config.baseUrl;
                        console.log(`✅ 动态配置获取成功，服务器端口: ${this.serverPort}`);

                        // 更新其他模块的配置
                        this.updateModuleConfigs();
                        return true;
                    }
                } catch (e) {
                    // 继续尝试下一个端口
                    continue;
                }
            }

            console.warn('⚠️ 无法获取动态配置，使用默认端口 3002');
            return false;

        } catch (error) {
            console.error('❌ 动态配置初始化失败:', error);
            return false;
        }
    },

    // 更新其他模块的配置
    updateModuleConfigs() {
        // 更新用户追踪模块
        if (typeof UserTrackingModule !== 'undefined') {
            UserTrackingModule.apiBaseUrl = `${this.baseUrl}/api/tracking`;
        }

        // 更新CONFIG对象
        if (typeof CONFIG !== 'undefined' && CONFIG.api) {
            CONFIG.api.baseUrl = `${this.baseUrl}/api`;
        }
    }
};

// 用户行为追踪模块 - 已禁用
const UserTrackingModule = {
    sessionId: null,
    isEnabled: false, // 🔒 已关闭用户行为追踪
    apiBaseUrl: 'http://localhost:3002/api/tracking', // 将被动态更新

    // 初始化追踪
    init() {
        if (!this.isEnabled) return;

        try {
            // 生成或获取会话ID
            this.sessionId = this.generateSessionId();

            // 创建会话
            this.createSession();

            // 记录页面访问
            this.trackAction('page_visit', '智慧校园系统首页', {
                url: window.location.href,
                timestamp: new Date().toISOString()
            });

            // 绑定页面卸载事件
            window.addEventListener('beforeunload', () => {
                this.endSession();
            });

            // 绑定页面可见性变化事件
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.trackAction('page_visit', '页面隐藏', {
                        timestamp: new Date().toISOString()
                    });
                } else {
                    this.trackAction('page_visit', '页面显示', {
                        timestamp: new Date().toISOString()
                    });
                }
            });

            console.log('📊 用户行为追踪已启用，会话ID:', this.sessionId);
        } catch (error) {
            console.error('用户行为追踪初始化失败:', error);
        }
    },

    // 生成会话ID
    generateSessionId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 9);
        return `session_${timestamp}_${random}`;
    },

    // 创建会话
    async createSession() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/session`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sessionId: this.sessionId,
                    userAgent: navigator.userAgent
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('会话创建结果:', result);
        } catch (error) {
            console.error('创建会话失败:', error);
        }
    },

    // 记录用户行为
    async trackAction(actionType, actionName, actionData = {}) {
        if (!this.isEnabled || !this.sessionId) return;

        try {
            const response = await fetch(`${this.apiBaseUrl}/action`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sessionId: this.sessionId,
                    actionType: actionType,
                    actionName: actionName,
                    actionData: actionData,
                    pageUrl: window.location.href
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            console.log(`📊 行为已记录: ${actionType} - ${actionName}`);
        } catch (error) {
            console.error('记录用户行为失败:', error);
        }
    },

    // 结束会话
    async endSession() {
        if (!this.isEnabled || !this.sessionId) return;

        try {
            const response = await fetch(`${this.apiBaseUrl}/session/end`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sessionId: this.sessionId
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            console.log('📊 会话已结束');
        } catch (error) {
            console.error('结束会话失败:', error);
        }
    },

    // 快捷方法：搜索行为
    trackSearch(searchTerm, searchType, results) {
        this.trackAction('search', `搜索${searchType}`, {
            searchTerm: searchTerm,
            searchType: searchType,
            resultsCount: results ? results.length : 0,
            timestamp: new Date().toISOString()
        });
    },

    // 快捷方法：路径规划
    trackRouteAction(action, data) {
        this.trackAction('route_plan', action, {
            ...data,
            timestamp: new Date().toISOString()
        });
    },

    // 快捷方法：天气查询
    trackWeatherAction(action, data) {
        this.trackAction('weather_check', action, {
            ...data,
            timestamp: new Date().toISOString()
        });
    },

    // 快捷方法：图层操作
    trackLayerAction(action, layerName, enabled) {
        this.trackAction('layer_toggle', action, {
            layerName: layerName,
            enabled: enabled,
            timestamp: new Date().toISOString()
        });
    },

    // 快捷方法：建筑物点击
    trackBuildingClick(buildingName, buildingType) {
        this.trackAction('building_click', '点击建筑物', {
            buildingName: buildingName,
            buildingType: buildingType,
            timestamp: new Date().toISOString()
        });
    },

    // 快捷方法：地图点击
    trackMapClick(coordinates, action) {
        this.trackAction('map_click', action, {
            coordinates: coordinates,
            timestamp: new Date().toISOString()
        });
    },

    // 快捷方法：导出操作
    trackExport(exportType, data) {
        this.trackAction('export', `导出${exportType}`, {
            exportType: exportType,
            ...data,
            timestamp: new Date().toISOString()
        });
    }
};

const CONFIG = {
    // 地图配置
    map: {
        center: ol.proj.fromLonLat([120.909241, 31.976284]), // 地图中心坐标 - 转换为EPSG:3857投影坐标
        zoom: 16, // 初始缩放级别
        minZoom: 10, // 最小缩放级别
        maxZoom: 20, // 最大缩放级别
        projection: 'EPSG:3857', // 坐标系统
        extent: [13454500, 3764800, 13456500, 3765800] // 地图范围 [minX, minY, maxX, maxY] - EPSG:3857投影坐标
    },

    // 图层数据路径配置
    layers: {
        buildings: 'geojson2_data/buildings.geojson', // 建筑物图层
        roads: 'geojson2_data/roads.geojson', // 道路图层
        rivers: 'geojson2_data/rivers.geojson', // 河流图层
        waters: 'geojson2_data/waters.geojson', // 水域图层
        traffic: 'geojson2_data/traffic.geojson', // 交通设施图层
        boundary: 'geojson2_data/boundary.geojson' // 边界图层
    },

    // UI参数配置
    ui: {
        loadingDuration: 2000, // 加载动画持续时间(ms)
        searchDelay: 300, // 搜索防抖延迟(ms)
        animationSpeed: 500, // 动画速度(ms)
        fadeInDuration: 300, // 淡入动画时长(ms)
        fadeOutDuration: 200, // 淡出动画时长(ms)
        modalZIndex: 1000, // 模态框层级
        tooltipDelay: 500 // 提示框延迟显示(ms)
    },

    // 样式常量配置
    styles: {
        // 颜色配置
        colors: {
            primary: '#1976d2', // 主色调
            secondary: '#424242', // 次要色调
            success: '#4caf50', // 成功色
            warning: '#ff9800', // 警告色
            error: '#f44336', // 错误色
            background: '#f5f5f5', // 背景色
            surface: '#ffffff', // 表面色
            text: '#212121', // 文字色
            textSecondary: '#757575', // 次要文字色
            border: '#e0e0e0', // 边框色
            shadow: 'rgba(0,0,0,0.1)' // 阴影色
        },
        
        // 可爱的建筑物分类样式配置
        buildingTypes: {
            '教学建筑': {
                fill: 'rgba(74, 144, 226, 0.8)', // 专业蓝色
                stroke: '#4A90E2',
                strokeWidth: 3,
                name: '教学建筑',
                icon: '🏫'
            },
            '公共建筑': {
                fill: 'rgba(173, 216, 230, 0.8)', // 天空蓝
                stroke: '#4ecdc4',
                strokeWidth: 3,
                name: '公共建筑',
                icon: '🏛️'
            },
            '宿舍建筑': {
                fill: 'rgba(255, 223, 186, 0.8)', // 桃色
                stroke: '#feca57',
                strokeWidth: 3,
                name: '宿舍建筑',
                icon: '🏠'
            },
            '体育建筑': {
                fill: 'rgba(149, 225, 211, 0.8)', // 薄荷绿
                stroke: '#95e1d3',
                strokeWidth: 3,
                name: '体育建筑',
                icon: '🏟️'
            },
            '行政建筑': {
                fill: 'rgba(221, 160, 221, 0.8)', // 淡紫色
                stroke: '#dda0dd',
                strokeWidth: 3,
                name: '行政建筑',
                icon: '🏢'
            },
            '食堂': {
                fill: 'rgba(255, 218, 185, 0.8)', // 桃橙色
                stroke: '#ff9f43',
                strokeWidth: 3,
                name: '食堂',
                icon: '🍽️'
            },
            '服务建筑': {
                fill: 'rgba(186, 220, 255, 0.8)', // 浅蓝色
                stroke: '#74b9ff',
                strokeWidth: 3,
                name: '服务建筑',
                icon: '🏪'
            },
            '学院建筑': {
                fill: 'rgba(255, 206, 84, 0.8)', // 温暖黄色
                stroke: '#feca57',
                strokeWidth: 3,
                name: '学院建筑',
                icon: '🎓'
            },
            '公共停车场': {
                fill: 'rgba(255, 69, 58, 0.8)', // 红色
                stroke: '#ff453a',
                strokeWidth: 3,
                name: '公共停车场',
                icon: '🅿️'
            },
            '未知': {
                fill: 'rgba(178, 190, 195, 0.7)', // 温和灰色
                stroke: '#b2bec3',
                strokeWidth: 2,
                name: '未知建筑',
                icon: '❓'
            }
        },

        // 可爱的图层样式配置
        layerStyles: {
            buildings: {
                fill: 'rgba(149, 225, 211, 0.8)', // 建筑物填充色 - 薄荷绿（默认）
                stroke: '#95e1d3', // 建筑物边框色 - 薄荷绿（默认）
                strokeWidth: 2 // 边框宽度
            },
            roads: {
                stroke: '#dda0dd', // 道路颜色 - 淡紫色
                strokeWidth: 3 // 道路宽度
            },
            rivers: {
                fill: 'rgba(135, 206, 250, 0.8)', // 河流填充色 - 天空蓝
                stroke: '#87ceeb', // 河流边框色 - 天空蓝
                strokeWidth: 3 // 河流宽度
            },
            waters: {
                fill: 'rgba(173, 216, 230, 0.7)', // 水域填充色 - 浅蓝色
                stroke: '#add8e6', // 水域边框色 - 浅蓝色
                strokeWidth: 2
            },
            traffic: {
                fill: 'rgba(74, 144, 226, 0.8)', // 交通设施填充色 - 专业蓝色
                stroke: '#4A90E2', // 交通设施边框色 - 专业蓝色
                strokeWidth: 3,
                radius: 6, // 交通设施点的半径
                opacity: 1.0 // 透明度
            },
            boundary: {
                stroke: '#4A90E2', // 边界颜色 - 专业蓝
                strokeWidth: 4, // 边界宽度
                strokeDasharray: [10, 5] // 虚线样式
            },
            highlight: {
                fill: 'rgba(255, 223, 186, 0.9)', // 高亮填充色 - 桃色
                stroke: '#ffdba6', // 高亮边框色 - 桃色
                strokeWidth: 4 // 高亮边框宽度
            }
        },

        // 字体配置
        fonts: {
            family: '"Noto Sans SC", "Microsoft YaHei", "PingFang SC", sans-serif', // 字体族
            sizes: {
                small: '12px', // 小字体
                normal: '14px', // 正常字体
                large: '16px', // 大字体
                xlarge: '18px', // 超大字体
                title: '20px' // 标题字体
            },
            weights: {
                normal: 400, // 正常粗细
                medium: 500, // 中等粗细
                bold: 700 // 粗体
            }
        },

        // 尺寸配置
        sizes: {
            borderRadius: '4px', // 圆角半径
            padding: {
                small: '8px', // 小间距
                normal: '12px', // 正常间距
                large: '16px', // 大间距
                xlarge: '24px' // 超大间距
            },
            margin: {
                small: '4px', // 小外边距
                normal: '8px', // 正常外边距
                large: '16px', // 大外边距
                xlarge: '32px' // 超大外边距
            }
        }
    },

    // 认证配置
    auth: {
        defaultUsername: 'admin', // 默认用户名
        defaultPassword: '123456', // 默认密码
        sessionTimeout: 3600000, // 会话超时时间(ms) - 1小时
        maxLoginAttempts: 3 // 最大登录尝试次数
    },

    // 搜索配置
    search: {
        maxResults: 10, // 最大搜索结果数
        minQueryLength: 1, // 最小查询长度
        highlightClass: 'search-highlight', // 高亮样式类名
        noResultsText: '未找到相关建筑物' // 无结果提示文字
    },

    // 路径规划配置
    routing: {
        maxDistance: 5000, // 最大路径距离(米)
        walkingSpeed: 7, // 步行速度(公里/小时)
        cyclingSpeed: 17, // 骑行速度(公里/小时)
        routeColor: 'rgba(255, 0, 0, 0.9)', // 路径颜色 - 红色，与主配置保持一致
        routeWidth: 2, // 路径宽度 - 更细的线条，与主配置保持一致
        startMarkerColor: '#4caf50', // 起点标记颜色
        endMarkerColor: '#f44336' // 终点标记颜色
    },

    // 信息展示配置
    info: {
        popupOffset: [0, -10], // 弹窗偏移量
        popupMaxWidth: 300, // 弹窗最大宽度
        popupMinWidth: 200, // 弹窗最小宽度
        showArea: true, // 是否显示面积信息
        areaUnit: '平方米', // 面积单位
        dateFormat: 'YYYY-MM-DD HH:mm:ss' // 日期格式
    },

    // 图层控制配置
    layerControl: {
        position: 'top-right', // 控制面板位置
        collapsed: false, // 是否默认折叠
        showLegend: true, // 是否显示图例
        legendPosition: 'bottom-left' // 图例位置
    },

    // 外部资源配置
    external: {
        openLayersCSS: 'https://cdn.jsdelivr.net/npm/ol@7.5.2/ol.css', // OpenLayers CSS
        openLayersJS: 'https://cdn.jsdelivr.net/npm/ol@7.5.2/dist/ol.js', // OpenLayers JS
        googleFonts: 'https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap' // Google字体
    },

    // 调试配置
    debug: {
        enabled: false, // 是否启用调试模式
        logLevel: 'info', // 日志级别: 'debug', 'info', 'warn', 'error'
        showPerformance: false, // 是否显示性能信息
        showBounds: false // 是否显示边界框
    },

    // 天气功能配置
    weather: {
        city: '南通', // 城市名称
        version: 'v1', // tianqiapi.com API版本
        cacheKey: 'campus_weather_cache', // 本地存储缓存键名
        cacheExpiry: 24 * 60 * 60 * 1000, // 缓存过期时间(ms) - 24小时
        staleThreshold: 2 * 60 * 60 * 1000, // 数据陈旧阈值(ms) - 2小时
        updateInterval: 2 * 60 * 60 * 1000, // 更新检查间隔(ms) - 2小时
        // 更新说明：系统每2小时检查一次，如果数据超过2小时会后台更新，缓存24小时后过期
        apiUrl: 'https://wttr.in/南通?format=j1', // wttr.in免费天气API接口地址
        retryAttempts: 3, // 重试次数
        retryDelay: 1000, // 重试延迟(ms)
        // 页面可见性管理配置
        autoUpdateEnabled: true, // 用户偏好：启用自动更新
        pauseOnHidden: true, // 页面隐藏时暂停更新
        resumeUpdateOnVisible: true, // 页面可见时恢复更新
        showUpdateStatus: true, // 显示更新状态指示
        userPreferencesKey: 'weather_user_preferences', // localStorage键名
        defaultData: { // 默认天气数据（API失败时使用）
            city: '南通',
            weather: '晴朗',
            currentTemp: 22,
            maxTemp: 28,
            minTemp: 18,
            humidity: '65%',
            pressure: '1013 hPa',
            windSpeed: '15 km/h',
            uvIndex: '3',
            icon: '☀️',
            date: new Date().toISOString().split('T')[0],
            updateTime: '数据不可用',
            temperatureChart: [
                { time: '00:00', temperature: 18, rawTime: 0 },
                { time: '06:00', temperature: 16, rawTime: 600 },
                { time: '12:00', temperature: 25, rawTime: 1200 },
                { time: '18:00', temperature: 22, rawTime: 1800 }
            ]
        }
    },

    // API配置 - 数据库连接
    api: {
        baseUrl: 'http://localhost:3002/api', // 内置HTTP服务器地址
        timeout: 10000, // 请求超时时间(ms)
        retryAttempts: 3, // 重试次数
        endpoints: {
            buildings: '/buildings',
            weather: '/weather',
            routes: '/routes',
            health: '/health'
        }
    }
};

// 冻结配置对象，防止意外修改（API配置除外，需要动态更新）
Object.freeze(CONFIG.map);
Object.freeze(CONFIG.layers);
Object.freeze(CONFIG.ui);
Object.freeze(CONFIG.styles);
Object.freeze(CONFIG.styles.colors);
Object.freeze(CONFIG.styles.layerStyles);
Object.freeze(CONFIG.styles.fonts);
Object.freeze(CONFIG.styles.sizes);
Object.freeze(CONFIG.auth);
// 注意：CONFIG.api 不冻结，允许动态端口配置
Object.freeze(CONFIG.search);
Object.freeze(CONFIG.routing);
Object.freeze(CONFIG.info);
Object.freeze(CONFIG.layerControl);
Object.freeze(CONFIG.external);
Object.freeze(CONFIG.debug);
Object.freeze(CONFIG.weather);
Object.freeze(CONFIG.weather.defaultData);

// ============================================================================
// 国际化辅助函数
// ============================================================================

// 国际化翻译函数
function t(key) {
    return window.I18N ? window.I18N.t(key) : key;
}

// 配置验证函数
function validateConfig() {
    const requiredKeys = ['map', 'layers', 'ui', 'styles', 'auth', 'weather'];
    const missingKeys = requiredKeys.filter(key => !CONFIG.hasOwnProperty(key));

    if (missingKeys.length > 0) return false;
    if (!Array.isArray(CONFIG.map.center) || CONFIG.map.center.length !== 2) return false;
    if (Object.keys(CONFIG.layers).length === 0) return false;
    if (!CONFIG.weather.city || !CONFIG.weather.apiUrl) return false;

    return true;
}

// 获取配置值的辅助函数
function getConfig(path, defaultValue = null) {
    const keys = path.split('.');
    let value = CONFIG;
    
    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        } else {
            return defaultValue;
        }
    }
    
    return value;
}

// 导出配置对象和工具函数（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, validateConfig, getConfig };
}

// 在Node.js环境中设置全局变量
if (typeof global !== 'undefined') {
    global.CONFIG = CONFIG;
    global.validateConfig = validateConfig;
    global.getConfig = getConfig;
}

// ============================================================================
// 建筑物分类工具模块 - BuildingClassifier
// ============================================================================

const BuildingClassifier = {
    // 建筑物分类规则
    classificationRules: {
        '教学建筑': [
            '教学楼', '教学', '实验楼', '实验', '工程训练中心'
        ],
        '公共建筑': [
            '图书馆', '范曾艺术馆', '艺术馆', '综合楼', '校园服务中心'
        ],
        '宿舍建筑': [
            '公寓', '宿舍', '学生公寓', '研究生公寓', '留学生公寓'
        ],
        '体育建筑': [
            '体育', '操场', '看台', '体育馆', '击剑馆', '足球场', '篮球场', '排球场'
        ],
        '行政建筑': [
            '行政', '办公', '校医院', '医院'
        ],
        '食堂': [
            '食堂', '餐厅'
        ],
        '服务建筑': [
            '服务', '驿站', '顺丰', '数码', '供电站', '交通职业适应性评价中心'
        ],
        '学院建筑': [
            '学院', '机械工程学院', '交通与土木工程学院', '电气工程学院', '纺化楼'
        ],
        '公共停车场': [
            '停车场', '停车', '车库', '地下车库', '公共停车场'
        ]
    },

    // 根据建筑物名称分类
    classifyBuilding(buildingName) {
        if (!buildingName || typeof buildingName !== 'string') {
            return '未知';
        }

        // 遍历分类规则
        for (const [category, keywords] of Object.entries(this.classificationRules)) {
            for (const keyword of keywords) {
                if (buildingName.includes(keyword)) {
                    return category;
                }
            }
        }

        return '未知';
    },

    // 获取建筑物样式
    getBuildingStyle(buildingName) {
        const category = this.classifyBuilding(buildingName);
        const styleConfig = CONFIG.styles.buildingTypes[category];

        return styleConfig || CONFIG.styles.buildingTypes['未知'];
    },

    // 获取所有建筑物类型
    getAllCategories() {
        return Object.keys(CONFIG.styles.buildingTypes);
    },

    // 获取类型统计信息
    getCategoryStats(buildings) {
        const stats = {};

        // 初始化统计
        this.getAllCategories().forEach(category => {
            stats[category] = {
                count: 0,
                totalArea: 0,
                buildings: []
            };
        });

        // 统计建筑物
        buildings.forEach(building => {
            const category = this.classifyBuilding(building.name);
            if (stats[category]) {
                stats[category].count++;
                stats[category].totalArea += building.area || 0;
                stats[category].buildings.push(building);
            }
        });

        return stats;
    }
};

// ============================================================================
// 加载动画模块 - LoadingModule
// ============================================================================

const LoadingModule = {
    // 模块状态
    isVisible: true, // 初始状态为显示
    isAnimating: false, // 是否正在执行动画
    progressValue: 0, // 当前进度值
    loadingElement: null, // 加载动画元素
    progressBar: null, // 进度条元素

    // 初始化模块
    init() {
        this.loadingElement = document.getElementById('loading-screen');
        this.progressBar = document.querySelector('.progress-bar');

        if (!this.loadingElement) return false;

        this.loadingElement.style.display = 'flex';
        this.loadingElement.style.opacity = '1';
        this.startProgressAnimation();
        return true;
    },

    // 显示加载动画
    showLoading() {
        if (!this.loadingElement) return;
        this.isVisible = true;
        this.loadingElement.style.display = 'flex';
        this.loadingElement.style.opacity = '1';
        this.loadingElement.classList.remove('fade-out');
        this.loadingElement.classList.add('fade-in');
    },

    // 隐藏加载动画
    hideLoading() {
        return new Promise((resolve) => {
            if (!this.loadingElement || !this.isVisible) {
                resolve();
                return;
            }

            this.isAnimating = true;
            this.isVisible = false;
            this.loadingElement.classList.remove('fade-in');
            this.loadingElement.classList.add('fade-out');
            this.loadingElement.style.transition = `opacity ${CONFIG.ui.fadeOutDuration}ms ease-out`;
            this.loadingElement.style.opacity = '0';

            setTimeout(() => {
                if (this.loadingElement) {
                    this.loadingElement.style.display = 'none';
                }
                this.isAnimating = false;
                resolve();
            }, CONFIG.ui.fadeOutDuration || 500);
        });
    },

    // 更新进度
    updateProgress(progress) {
        if (!this.progressBar) return;

        // 确保进度值在0-100之间
        progress = Math.max(0, Math.min(100, progress));
        this.progressValue = progress;

        // 更新进度条宽度
        this.progressBar.style.width = `${progress}%`;

        if (progress >= 100) {
            setTimeout(() => {
                this.hideLoading();
            }, CONFIG.ui.loadingDuration || 1000);
        }
    },

    // 开始进度动画
    startProgressAnimation() {
        if (!this.progressBar) return;

        let progress = 0;
        const duration = CONFIG.ui.loadingDuration || 2000;
        const interval = 50;
        const increment = (100 / duration) * interval;

        const progressTimer = setInterval(() => {
            progress += increment;

            if (progress >= 100) {
                progress = 100;
                clearInterval(progressTimer);
            }

            this.updateProgress(progress);
        }, interval);
    },

    // 设置加载文本
    setLoadingText(text) {
        const loadingText = document.querySelector('.loading-text');
        if (loadingText) {
            loadingText.textContent = text;
        }
    },

    // 自动控制加载流程
    autoControl() {

        // 模拟资源加载过程
        const loadingSteps = [
            { progress: 20, text: '正在加载地图数据...', delay: 300 },
            { progress: 40, text: '正在加载建筑物信息...', delay: 500 },
            { progress: 60, text: '正在加载道路信息...', delay: 400 },
            { progress: 80, text: '正在初始化地图引擎...', delay: 600 },
            { progress: 100, text: '加载完成', delay: 300 }
        ];

        let currentStep = 0;

        const executeStep = () => {
            if (currentStep >= loadingSteps.length) return;

            const step = loadingSteps[currentStep];
            this.setLoadingText(step.text);
            this.updateProgress(step.progress);

            currentStep++;

            if (currentStep < loadingSteps.length) {
                setTimeout(executeStep, step.delay);
            }
        };

        // 开始执行步骤
        setTimeout(executeStep, 500);
    },

    // 检查是否正在显示
    isShowing() {
        return this.isVisible && !this.isAnimating;
    },

    // 重置加载动画
    reset() {
        this.progressValue = 0;
        this.isVisible = true;
        this.isAnimating = false;

        if (this.progressBar) {
            this.progressBar.style.width = '0%';
        }

        this.setLoadingText('正在加载地图数据...');
        this.showLoading();
    },

    // 销毁模块
    destroy() {
        if (this.loadingElement) {
            this.loadingElement.style.display = 'none';
        }
        this.loadingElement = null;
        this.progressBar = null;
        this.isVisible = false;
        this.isAnimating = false;
    },

    // ============================================================================
    // 增强的错误处理方法 - 解决初始化失败问题
    // ============================================================================

    // 增强的初始化方法（带错误处理）
    initWithErrorHandling() {
        try {
            console.log('🔄 开始初始化LoadingModule...');

            this.loadingElement = document.getElementById('loading-screen');
            this.progressBar = document.querySelector('.progress-bar');

            if (!this.loadingElement) {
                throw new Error('loading-screen元素未找到');
            }

            if (!this.progressBar) {
                console.warn('⚠️ progress-bar元素未找到，将跳过进度显示');
            }

            this.loadingElement.style.display = 'flex';
            this.loadingElement.style.opacity = '1';

            // 只有在progressBar存在时才启动进度动画
            if (this.progressBar) {
                this.startProgressAnimation();
            } else {
                console.warn('⚠️ 跳过进度动画，因为progress-bar元素缺失');
            }

            console.log('✅ LoadingModule初始化成功');
            return true;

        } catch (error) {
            console.error('❌ LoadingModule初始化失败:', error);
            this.handleInitError(error);
            return false;
        }
    },

    // 错误处理方法
    handleInitError(error) {
        console.log('🔧 执行LoadingModule错误恢复流程...');

        // 记录详细的错误信息
        console.error('LoadingModule错误详情:', {
            message: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
        });

        // 尝试显示用户友好的错误提示
        if (typeof showUserMessage === 'function') {
            showUserMessage('系统加载遇到问题，正在尝试恢复...', 'warning');
        }

        // 直接尝试显示登录页面
        setTimeout(() => {
            try {
                console.log('🔄 尝试直接初始化AuthModule...');
                if (typeof AuthModule !== 'undefined') {
                    let authInitSuccess = false;

                    // 优先使用增强的初始化方法
                    if (typeof AuthModule.initWithValidation === 'function') {
                        console.log('🔧 LoadingModule使用增强的AuthModule初始化方法');
                        authInitSuccess = AuthModule.initWithValidation();
                    } else if (typeof AuthModule.init === 'function') {
                        console.log('🔧 LoadingModule使用原始的AuthModule初始化方法');
                        authInitSuccess = AuthModule.init();
                    } else {
                        console.error('❌ AuthModule没有可用的初始化方法');
                        this.showFallbackInterface('AuthModule初始化方法不可用');
                        return;
                    }

                    if (!authInitSuccess) {
                        console.error('❌ AuthModule初始化返回失败');
                        // 错误处理已由AuthModule的错误处理方法接管
                        // 这里不需要额外的处理，因为AuthModule会显示自己的错误界面
                    }
                } else {
                    console.error('❌ AuthModule不可用');
                    this.showFallbackInterface('AuthModule不可用');
                }
            } catch (authError) {
                console.error('❌ AuthModule备用初始化失败:', authError);
                this.showFallbackInterface('系统初始化完全失败');
            }
        }, 500);
    },

    // 显示降级界面（使用FallbackInterfaceManager）
    showFallbackInterface(errorInfo) {
        console.log('🚨 LoadingModule请求显示降级界面:', errorInfo);

        try {
            // 使用新的FallbackInterfaceManager
            if (typeof FallbackInterfaceManager !== 'undefined') {
                FallbackInterfaceManager.show(errorInfo);
            } else {
                console.error('❌ FallbackInterfaceManager不可用，使用备用方案');
                this.showBasicFallback(errorInfo);
            }
        } catch (error) {
            console.error('❌ 显示降级界面失败:', error);
            this.showBasicFallback(errorInfo);
        }
    },

    // 基本的降级界面（备用方案）
    showBasicFallback(errorInfo) {
        try {
            const body = document.body;
            if (body) {
                body.innerHTML = `
                    <div style="
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 100vh;
                        flex-direction: column;
                        font-family: 'Microsoft YaHei', Arial, sans-serif;
                        background: #f5f5f5;
                        color: #333;
                        text-align: center;
                        padding: 20px;
                    ">
                        <h2 style="color: #4A90E2; margin-bottom: 20px;">智慧校园系统</h2>
                        <p style="margin-bottom: 20px;">系统正在加载中，请稍候...</p>
                        <p style="font-size: 14px; color: #666; margin-bottom: 20px;">错误信息: ${errorInfo}</p>
                        <button onclick="location.reload()" style="
                            padding: 12px 24px;
                            background: #4A90E2;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 14px;
                        ">重新加载</button>
                    </div>
                `;
            }
        } catch (error) {
            console.error('❌ 基本降级界面也失败:', error);
            alert('系统初始化失败: ' + errorInfo);
        }
    }
};

// 导出加载动画模块（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports.LoadingModule = LoadingModule;
}

// 在全局作用域中设置LoadingModule
if (typeof window !== 'undefined') {
    window.LoadingModule = LoadingModule;
}

// ============================================================================
// 登录验证模块 - AuthModule
// ============================================================================

const AuthModule = {
    // 模块状态
    isLoggedIn: false, // 登录状态
    isProcessing: false, // 是否正在处理登录
    loginContainer: null, // 登录容器元素
    mainContainer: null, // 主界面容器元素
    loginForm: null, // 登录表单元素
    errorElement: null, // 错误提示元素

    // 初始化模块
    init() {
        this.loginContainer = document.getElementById('login-container');
        this.mainContainer = document.getElementById('main-container');
        this.loginForm = document.getElementById('login-form');
        this.errorElement = document.getElementById('login-error');

        if (!this.loginContainer || !this.mainContainer || !this.loginForm) {
            return false;
        }

        this.bindEvents();
        this.showLoginForm();
        return true;
    },

    // 绑定事件
    bindEvents() {
        // 表单提交事件
        this.loginForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLoginSubmit();
        });

        // 登录按钮点击事件
        const loginBtn = document.getElementById('login-btn');
        if (loginBtn) {
            loginBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleLoginSubmit();
            });
        }

        // 回车键快捷登录
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');

        [usernameInput, passwordInput].forEach(input => {
            if (input) {
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.handleLoginSubmit();
                    }
                });
            }
        });

        // 输入框焦点事件 - 清除错误提示
        [usernameInput, passwordInput].forEach(input => {
            if (input) {
                input.addEventListener('focus', () => {
                    this.hideError();
                });
            }
        });

    },

    // 显示登录界面
    showLoginForm() {
        if (this.loginContainer) {
            this.loginContainer.style.display = 'flex';
            this.loginContainer.classList.add('fade-in');
            this.loginContainer.classList.remove('fade-out');
        }

        if (this.mainContainer) {
            this.mainContainer.style.display = 'none';
        }

        this.clearInputs();
        this.hideError();

        setTimeout(() => {
            const usernameInput = document.getElementById('username');
            if (usernameInput) {
                usernameInput.focus();
            }
        }, 300);
    },

    // 隐藏登录界面，显示主界面
    hideLoginForm() {
        return new Promise((resolve) => {

            if (this.loginContainer) {
                this.loginContainer.classList.remove('fade-in');
                this.loginContainer.classList.add('fade-out');

                setTimeout(() => {
                    this.loginContainer.style.display = 'none';

                    // 显示主界面
                    if (this.mainContainer) {
                        this.mainContainer.style.display = 'block';
                        this.mainContainer.classList.add('fade-in');
                    }

                    resolve();
                }, CONFIG.ui.fadeOutDuration || 300);
            } else {
                resolve();
            }
        });
    },

    // 验证登录信息
    validateLogin(username, password) {
        const defaultUsername = CONFIG.auth.defaultUsername || 'admin';
        const defaultPassword = CONFIG.auth.defaultPassword || '123456';
        return username === defaultUsername && password === defaultPassword;
    },

    // 处理登录表单提交
    async handleLoginSubmit() {
        if (this.isProcessing) return;

        this.isProcessing = true;

        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');

        if (!usernameInput || !passwordInput) {
            this.isProcessing = false;
            return;
        }

        const username = usernameInput.value.trim();
        const password = passwordInput.value.trim();

        // 输入验证
        if (!username || !password) {
            this.showError('请输入用户名和密码');
            this.isProcessing = false;
            return;
        }

        // 显示加载状态
        this.setLoadingState(true);

        try {
            // 模拟网络延迟
            await new Promise(resolve => setTimeout(resolve, 800));

            // 验证登录
            const isValid = this.validateLogin(username, password);

            if (isValid) {
                this.isLoggedIn = true;
                await this.hideLoginForm();
                this.onLoginSuccess();
            } else {
                this.showError('用户名或密码错误，请重试');
                passwordInput.value = '';
                passwordInput.focus();
            }

        } catch (error) {
            this.showError('登录过程中发生错误，请重试');
        } finally {
            this.setLoadingState(false);
            this.isProcessing = false;
        }
    },

    // 设置加载状态
    setLoadingState(isLoading) {
        const loginBtn = document.getElementById('login-btn');
        const buttonText = document.querySelector('.button-text');
        const buttonLoading = document.querySelector('.button-loading');

        if (loginBtn) {
            loginBtn.disabled = isLoading;
        }

        if (buttonText && buttonLoading) {
            if (isLoading) {
                buttonText.style.display = 'none';
                buttonLoading.style.display = 'inline';
            } else {
                buttonText.style.display = 'inline';
                buttonLoading.style.display = 'none';
            }
        }
    },

    // 显示错误提示
    showError(message) {
        console.log(`显示错误提示: ${message}`);

        if (this.errorElement) {
            const errorText = this.errorElement.querySelector('.error-text');
            if (errorText) {
                errorText.textContent = message;
            }

            this.errorElement.style.display = 'flex';
            this.errorElement.classList.add('fade-in');

            // 3秒后自动隐藏
            setTimeout(() => {
                this.hideError();
            }, 3000);
        }
    },

    // 隐藏错误提示
    hideError() {
        if (this.errorElement) {
            this.errorElement.style.display = 'none';
            this.errorElement.classList.remove('fade-in');
        }
    },

    // 清空输入框
    clearInputs() {
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');

        if (usernameInput) usernameInput.value = '';
        if (passwordInput) passwordInput.value = '';
    },

    // 登录成功回调
    onLoginSuccess() {
        const event = new CustomEvent('loginSuccess', {
            detail: { timestamp: Date.now() }
        });
        document.dispatchEvent(event);
    },

    // 登出功能
    logout() {
        this.isLoggedIn = false;
        this.showLoginForm();
        const event = new CustomEvent('logout', {
            detail: { timestamp: Date.now() }
        });
        document.dispatchEvent(event);
    },

    // 检查登录状态
    isUserLoggedIn() {
        return this.isLoggedIn;
    },

    // 获取默认登录信息（用于测试）
    getDefaultCredentials() {
        return {
            username: CONFIG.auth.defaultUsername || 'admin',
            password: CONFIG.auth.defaultPassword || '123456'
        };
    },

    // 销毁模块
    destroy() {
        if (this.loginForm) {
            this.loginForm.removeEventListener('submit', this.handleLoginSubmit);
        }
        this.loginContainer = null;
        this.mainContainer = null;
        this.loginForm = null;
        this.errorElement = null;
        this.isLoggedIn = false;
        this.isProcessing = false;
    },

    // ============================================================================
    // 增强的初始化检查方法 - 解决DOM元素缺失问题
    // ============================================================================

    // 增强的初始化方法（带DOM元素验证）
    initWithValidation() {
        console.log('🔄 开始初始化AuthModule...');

        // 检查必需的DOM元素
        const requiredElements = {
            'login-container': 'loginContainer',
            'main-container': 'mainContainer',
            'login-form': 'loginForm',
            'login-error': 'errorElement'
        };

        const missingElements = [];

        for (const [elementId, propertyName] of Object.entries(requiredElements)) {
            const element = document.getElementById(elementId);
            if (element) {
                this[propertyName] = element;
                console.log(`✅ 找到元素: ${elementId}`);
            } else {
                missingElements.push(elementId);
                console.error(`❌ 缺失元素: ${elementId}`);
            }
        }

        if (missingElements.length > 0) {
            console.error('❌ AuthModule初始化失败，缺失关键DOM元素:', missingElements);
            this.handleMissingElements(missingElements);
            return false;
        }

        try {
            console.log('🔧 开始绑定事件...');
            this.bindEvents();

            console.log('🔧 显示登录表单...');
            this.showLoginForm();

            console.log('✅ AuthModule初始化成功');
            return true;
        } catch (error) {
            console.error('❌ AuthModule事件绑定失败:', error);
            this.handleInitError(error);
            return false;
        }
    },

    // 处理缺失元素的情况
    handleMissingElements(missingElements) {
        const message = `系统界面加载不完整，缺失元素: ${missingElements.join(', ')}`;
        console.error('AuthModule DOM元素检查失败:', {
            missingElements: missingElements,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        });

        // 显示用户友好的错误提示
        if (typeof showUserMessage === 'function') {
            showUserMessage('系统界面加载遇到问题，请刷新页面重试', 'error');
        } else {
            console.error('showUserMessage函数不可用，无法显示用户提示');
        }

        // 尝试显示基本的错误界面
        this.showBasicErrorInterface(missingElements);
    },

    // 处理初始化错误
    handleInitError(error) {
        console.error('AuthModule初始化错误详情:', {
            message: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
        });

        // 显示用户友好的错误提示
        if (typeof showUserMessage === 'function') {
            showUserMessage('登录系统初始化失败，请刷新页面重试', 'error');
        }

        // 尝试显示基本的错误界面
        this.showBasicErrorInterface(['初始化错误: ' + error.message]);
    },

    // 显示基本错误界面
    showBasicErrorInterface(errorInfo) {
        console.log('🚨 AuthModule请求显示错误界面');

        try {
            // 优先使用FallbackInterfaceManager显示完整的降级界面
            if (typeof FallbackInterfaceManager !== 'undefined') {
                const errorMessage = Array.isArray(errorInfo) ? errorInfo.join(', ') : errorInfo;
                FallbackInterfaceManager.show('AuthModule初始化失败: ' + errorMessage);
                return;
            }

            // 备用方案：显示简单的错误提示
            const body = document.body;
            if (body) {
                // 检查是否已经有错误界面
                let errorDiv = document.getElementById('auth-error-interface');
                if (!errorDiv) {
                    errorDiv = document.createElement('div');
                    errorDiv.id = 'auth-error-interface';
                    errorDiv.innerHTML = `
                        <div style="
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            background: #fff5f5;
                            border: 1px solid #fed7d7;
                            border-radius: 8px;
                            padding: 16px;
                            max-width: 400px;
                            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                            z-index: 10000;
                            font-family: 'Microsoft YaHei', Arial, sans-serif;
                        ">
                            <div style="color: #e53e3e; font-weight: 600; margin-bottom: 8px;">
                                ⚠️ 系统初始化问题
                            </div>
                            <div style="color: #666; font-size: 14px; margin-bottom: 12px;">
                                ${Array.isArray(errorInfo) ? errorInfo.join(', ') : errorInfo}
                            </div>
                            <button onclick="location.reload()" style="
                                background: #e53e3e;
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 4px;
                                cursor: pointer;
                                font-size: 14px;
                            ">刷新页面</button>
                        </div>
                    `;
                    body.appendChild(errorDiv);
                }
            }
        } catch (interfaceError) {
            console.error('❌ 创建错误界面失败:', interfaceError);
            // 最后的备用方案
            alert('系统初始化失败: ' + (Array.isArray(errorInfo) ? errorInfo.join(', ') : errorInfo));
        }
    }
};

// ============================================================================
// 全局超时保护机制 - 解决加载卡死问题
// ============================================================================

// 全局超时保护机制
const LOADING_TIMEOUT = 8000; // 8秒超时
let loadingTimeoutId = null;

// 启动超时保护
function startLoadingTimeout() {
    console.log('🔄 启动加载超时保护机制 (8秒)');
    loadingTimeoutId = setTimeout(() => {
        const loadingScreen = document.getElementById('loading-screen');
        const loginContainer = document.getElementById('login-container');

        if (loadingScreen && loadingScreen.style.display !== 'none') {
            console.warn('⚠️ 加载超时，强制进入登录页面');
            loadingScreen.style.display = 'none';
            if (loginContainer) {
                loginContainer.style.display = 'flex';
            }

            // 显示用户友好提示
            if (typeof showUserMessage === 'function') {
                showUserMessage('系统加载完成，如有问题请刷新页面', 'info');
            } else {
                console.info('系统加载完成，如有问题请刷新页面');
            }
        }
    }, LOADING_TIMEOUT);
}

// 清除超时保护
function clearLoadingTimeout() {
    if (loadingTimeoutId) {
        console.log('✅ 清除加载超时保护');
        clearTimeout(loadingTimeoutId);
        loadingTimeoutId = null;
    }
}

// ============================================================================
// 集成的系统初始化流程 - 统一协调所有错误处理机制
// ============================================================================

// 修改页面加载完成后的初始化流程
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 页面DOM加载完成，开始集成系统初始化');
    console.log('📋 初始化流程: 超时保护 → 动态配置 → 加载模块 → 认证模块 → 资源检测 → 后台服务');

    try {
        // ========== 第一阶段：启动全局保护机制 ==========
        console.log('🔄 第一阶段：启动全局保护机制');
        startLoadingTimeout();

        // 🔒 用户行为追踪已禁用
        // UserTrackingModule.init();

        // ========== 第二阶段：初始化动态配置 ==========
        console.log('🔄 第二阶段：初始化动态配置');
        try {
            await DynamicConfig.init();
            console.log('✅ 动态配置初始化完成');
        } catch (configError) {
            console.warn('⚠️ 动态配置初始化失败，使用默认配置:', configError);
            // 配置失败不应阻塞主流程
        }

        // ========== 第三阶段：初始化加载模块 ==========
        console.log('🔄 第三阶段：初始化加载模块');
        let loadingInitSuccess = false;
        try {
            if (typeof LoadingModule.initWithErrorHandling === 'function') {
                console.log('🔧 使用增强的LoadingModule初始化方法');
                loadingInitSuccess = LoadingModule.initWithErrorHandling();
            } else {
                console.log('🔧 使用原始的LoadingModule初始化方法');
                loadingInitSuccess = LoadingModule.init();
            }
        } catch (error) {
            console.error('❌ LoadingModule初始化异常:', error);
            loadingInitSuccess = false;
        }

        if (loadingInitSuccess) {
            console.log('✅ LoadingModule初始化成功，开始加载动画');
            LoadingModule.autoControl();

            // ========== 第四阶段：初始化认证模块 ==========
            setTimeout(async () => {
                console.log('🔄 第四阶段：初始化认证模块');

                // 清除超时保护（正常加载完成）
                clearLoadingTimeout();

                try {
                    let authInitSuccess = false;
                    if (typeof AuthModule.initWithValidation === 'function') {
                        console.log('🔧 使用增强的AuthModule初始化方法');
                        authInitSuccess = AuthModule.initWithValidation();
                    } else {
                        console.log('🔧 使用原始的AuthModule初始化方法');
                        authInitSuccess = AuthModule.init();
                    }

                    if (!authInitSuccess) {
                        throw new Error('AuthModule初始化失败');
                    }

                    console.log('✅ AuthModule初始化成功');

                    // ========== 第五阶段：资源加载检测 ==========
                    console.log('🔄 第五阶段：资源加载检测');
                    if (typeof ResourceLoadingDetector !== 'undefined') {
                        setTimeout(() => {
                            try {
                                const results = ResourceLoadingDetector.checkAllResources();
                                const allResourcesLoaded = ResourceLoadingDetector.handleResourceFailures(results);

                                if (allResourcesLoaded) {
                                    console.log('✅ 所有资源加载检测完成');
                                } else {
                                    console.warn('⚠️ 部分资源加载失败，但系统仍可正常使用');
                                }
                            } catch (resourceError) {
                                console.error('❌ 资源检测过程中发生异常:', resourceError);
                            }
                        }, 1000);
                    } else {
                        console.warn('⚠️ ResourceLoadingDetector不可用，跳过资源检测');
                    }

                    // ========== 第六阶段：后台服务初始化 ==========
                    console.log('🔄 第六阶段：后台服务初始化');
                    await initializeBackgroundServices();

                    console.log('🎉 系统初始化完成！所有模块已就绪');

                } catch (error) {
                    console.error('❌ 认证模块或后续初始化失败:', error);

                    // 使用FallbackInterfaceManager显示错误
                    if (typeof FallbackInterfaceManager !== 'undefined') {
                        FallbackInterfaceManager.show('认证系统初始化失败: ' + error.message);
                    } else if (typeof showUserMessage === 'function') {
                        showUserMessage('登录系统初始化失败，请刷新页面重试', 'error');
                    } else {
                        console.error('无法显示用户错误提示');
                    }
                }
            }, CONFIG.ui.loadingDuration + 500 || 2500);

        } else {
            // LoadingModule初始化失败，清除超时保护
            console.error('❌ LoadingModule初始化失败，启动错误恢复流程');
            clearLoadingTimeout();

            // 错误处理现在由LoadingModule.handleInitError方法处理
            // 这里不再需要重复的错误处理逻辑
            console.log('🔧 错误处理已由LoadingModule.handleInitError接管');
        }

    } catch (criticalError) {
        console.error('❌ 系统初始化过程中发生严重错误:', criticalError);

        // 清除超时保护
        clearLoadingTimeout();

        // 显示降级界面
        if (typeof FallbackInterfaceManager !== 'undefined') {
            FallbackInterfaceManager.show('系统初始化严重错误: ' + criticalError.message);
        } else {
            alert('系统初始化失败，请刷新页面重试。错误: ' + criticalError.message);
        }
    }
});

// ============================================================================
// 后台服务初始化函数 - 集成所有后台任务
// ============================================================================

async function initializeBackgroundServices() {
    console.log('🔄 开始初始化后台服务...');

    try {
        // ========== 初始化全局关闭按钮 ==========
        console.log('🔧 设置全局关闭按钮事件监听器');
        setTimeout(() => {
            try {
                const closeBtn = document.getElementById('building-info-close');
                const panel = document.getElementById('building-info-panel');

                if (closeBtn && panel) {
                    // 移除可能存在的重复事件监听器
                    closeBtn.removeEventListener('click', globalCloseHandler);

                    // 添加全局关闭事件处理器
                    closeBtn.addEventListener('click', globalCloseHandler);

                    console.log('✅ 全局关闭按钮事件监听器已设置');
                } else {
                    console.warn('⚠️ 关闭按钮或面板元素未找到，将在后续重试');
                }
            } catch (error) {
                console.error('❌ 设置全局关闭按钮失败:', error);
            }
        }, 1000);

        // ========== 检查系统状态和数据可用性 ==========
        console.log('🔧 检查系统状态和数据可用性...');

        // 显示系统初始化提示
        if (typeof showUserMessage === 'function') {
            showUserMessage('智慧校园系统正在初始化...', 'info');
        }

        // 等待buildings.js加载完成
        let retryCount = 0;
        const maxRetries = 10;

        const checkDataAvailability = () => {
            return new Promise((resolve) => {
                const checkInterval = setInterval(() => {
                    retryCount++;

                    if (typeof window.buildingsData !== 'undefined') {
                        clearInterval(checkInterval);
                        console.log('✅ 建筑物数据已加载');
                        resolve(true);
                    } else if (retryCount >= maxRetries) {
                        clearInterval(checkInterval);
                        console.warn('⚠️ 建筑物数据加载超时');
                        resolve(false);
                    }
                }, 100);
            });
        };

        const dataAvailable = await checkDataAvailability();

        if (dataAvailable && typeof ApiModule !== 'undefined') {
            try {
                const result = await ApiModule.healthCheck();

                if (result.success) {
                    console.log('✅ 本地SQLite数据访问正常:', result.data);
                    console.log('🎉 SQLite数据库集成已激活，搜索功能已升级！');

                    const buildingsCount = result.data.buildings_count || 0;
                    const statusMessage = `系统已就绪 - 使用本地数据（${buildingsCount}个建筑物）`;

                    if (typeof showUserMessage === 'function') {
                        showUserMessage(statusMessage, 'success');
                    }
                } else {
                    console.warn('⚠️ 本地SQLite数据访问失败，使用备用数据源');
                    if (typeof showUserMessage === 'function') {
                        showUserMessage('系统已就绪 - 使用备用数据源', 'warning');
                    }
                }
            } catch (apiError) {
                console.error('❌ API健康检查失败:', apiError);
                if (typeof showUserMessage === 'function') {
                    showUserMessage('系统已就绪 - 数据服务部分功能受限', 'warning');
                }
            }
        } else {
            console.warn('⚠️ 数据或API模块不可用，使用基础功能');
            if (typeof showUserMessage === 'function') {
                showUserMessage('系统已就绪 - 基础功能可用', 'info');
            }
        }

        console.log('✅ 后台服务初始化完成');

    } catch (error) {
        console.error('❌ 后台服务初始化失败:', error);
        // 后台服务失败不应阻塞主要功能
    }
}

// 监听登出事件
document.addEventListener('logout', (event) => {
    // 这里可以清理其他模块的状态
});

// 导出登录验证模块（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports.AuthModule = AuthModule;
}

// 在全局作用域中设置AuthModule
if (typeof window !== 'undefined') {
    window.AuthModule = AuthModule;
}

// ============================================================================
// 地图核心模块 - MapModule
// ============================================================================

const MapModule = {
    // 模块状态
    map: null, // OpenLayers地图实例
    view: null, // 地图视图
    mapContainer: null, // 地图容器元素
    isInitialized: false, // 初始化状态
    baseLayer: null, // 基础图层

    // 初始化地图模块
    init() {
        this.mapContainer = document.getElementById('map');
        if (!this.mapContainer) return false;

        try {
            if (typeof ol === 'undefined') return false;
            this.createMap();
            this.setupInteractions();
            this.bindMapEvents();
            this.isInitialized = true;
            return true;
        } catch (error) {
            return false;
        }
    },

    // 创建地图实例
    createMap() {
        this.view = new ol.View({
            center: CONFIG.map.center, // 直接使用投影坐标，不再转换
            zoom: CONFIG.map.zoom,
            minZoom: CONFIG.map.minZoom || 10,
            maxZoom: CONFIG.map.maxZoom || 20,
            projection: 'EPSG:3857'
        });

        // 创建墨卡托投影底图，确保与图层配准
        this.baseLayer = this.createBaseLayer();

        // 验证底图配准
        this.validateBaseLayerAlignment();

        // 完成地图创建
        this.finishMapCreation();
    },

    // 创建底图图层
    createBaseLayer() {
        console.log('创建EPSG:3857投影坐标系底图');

        // 创建专门的EPSG:3857底图
        return this.createEPSG3857BaseLayer();
    },

    // 创建EPSG:3857投影坐标系底图
    createEPSG3857BaseLayer() {
        console.log('创建墨卡托投影底图，确保与图层配准');

        // 使用当前正在使用的OpenStreetMap底图源（删除其他5个多余底图）
        const basemapSources = [
            {
                name: 'OpenStreetMap标准',
                source: new ol.source.OSM({
                    url: 'https://{a-c}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                    attributions: ['© OpenStreetMap contributors'],
                    crossOrigin: 'anonymous'
                }),
                description: 'OpenStreetMap标准墨卡托投影底图'
            }
        ];

        // 创建OpenStreetMap底图图层，确保EPSG:3857投影配准
        try {
            const basemap = basemapSources[0]; // 使用唯一的OpenStreetMap底图
            console.log(`使用墨卡托投影底图源: ${basemap.name}`);

            const baseLayer = new ol.layer.Tile({
                source: basemap.source,
                opacity: 1.0,
                title: basemap.name,
                // 确保底图使用EPSG:3857投影
                projection: 'EPSG:3857'
            });

            // 验证底图配准
            console.log(`✅ 成功创建墨卡托投影底图: ${basemap.name}`);
            console.log(`📍 底图投影: EPSG:3857 (Web Mercator)`);

            return baseLayer;
        } catch (error) {
            console.warn(`❌ OpenStreetMap底图加载失败:`, error);
            // 如果OpenStreetMap失败，创建备用底图
            console.warn('OpenStreetMap底图失败，使用备用底图');
            return this.createFallbackBaseLayer();
        }
    },

    // 创建备用底图
    createFallbackBaseLayer() {
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');

        // 绘制浅灰色背景
        ctx.fillStyle = '#f5f5f5';
        ctx.fillRect(0, 0, 512, 512);

        // 绘制网格线
        ctx.strokeStyle = '#e0e0e0';
        ctx.lineWidth = 1;

        // 主网格（64像素间距）
        for (let i = 0; i <= 512; i += 64) {
            ctx.beginPath();
            ctx.moveTo(i, 0);
            ctx.lineTo(i, 512);
            ctx.moveTo(0, i);
            ctx.lineTo(512, i);
            ctx.stroke();
        }

        // 添加标识和投影信息
        ctx.fillStyle = '#666666';
        ctx.font = 'bold 14px Arial';
        ctx.fillText('墨卡托投影备用底图', 15, 30);
        ctx.font = '12px Arial';
        ctx.fillText('EPSG:3857', 15, 50);
        ctx.fillText('Web Mercator', 15, 70);

        const dataUrl = canvas.toDataURL();

        console.log('🔧 创建墨卡托投影备用底图');

        return new ol.layer.Tile({
            source: new ol.source.XYZ({
                url: dataUrl,
                attributions: ['墨卡托投影备用底图 (EPSG:3857)'],
                tileSize: [512, 512],
                projection: 'EPSG:3857'
            }),
            opacity: 0.8,
            title: '墨卡托投影备用底图'
        });
    },

    // 验证底图配准
    validateBaseLayerAlignment() {
        console.log('🔍 验证底图与图层配准情况');

        try {
            // 获取地图中心点
            const mapCenter = this.view.getCenter();
            const mapZoom = this.view.getZoom();

            console.log('📍 地图配置验证:');
            console.log(`  - 中心坐标 (EPSG:3857): [${mapCenter[0].toFixed(2)}, ${mapCenter[1].toFixed(2)}]`);
            console.log(`  - 缩放级别: ${mapZoom}`);
            console.log(`  - 投影系统: ${this.view.getProjection().getCode()}`);

            // 转换为经纬度验证
            const centerLonLat = ol.proj.toLonLat(mapCenter);
            console.log(`  - 中心坐标 (经纬度): [${centerLonLat[0].toFixed(6)}, ${centerLonLat[1].toFixed(6)}]`);

            // 验证坐标范围是否合理（南通大学区域）
            const isValidLon = centerLonLat[0] >= 120.89 && centerLonLat[0] <= 120.92;
            const isValidLat = centerLonLat[1] >= 31.96 && centerLonLat[1] <= 31.98;

            if (isValidLon && isValidLat) {
                console.log('✅ 底图配准验证通过 - 坐标范围正确');
            } else {
                console.warn('⚠️ 底图配准可能存在问题 - 坐标范围异常');
            }

            // 验证底图投影
            if (this.baseLayer) {
                const baseLayerTitle = this.baseLayer.get('title') || '未知底图';
                console.log(`📋 底图信息: ${baseLayerTitle}`);
                console.log('✅ 底图使用标准墨卡托投影 (EPSG:3857)');
            }

        } catch (error) {
            console.error('❌ 底图配准验证失败:', error);
        }
    },



    // 完成地图创建
    finishMapCreation() {
        this.map = new ol.Map({
            target: this.mapContainer,
            layers: [this.baseLayer],
            view: this.view,
            controls: [
                new ol.control.Zoom(),
                new ol.control.ScaleLine({
                    units: 'metric',
                    bar: true,
                    steps: 4,
                    text: true,
                    minWidth: 140
                }),
                new ol.control.FullScreen(),
                new ol.control.Attribution({
                    collapsible: false
                })
            ]
        });

    },



    // 设置基础交互
    setupInteractions() {
        const interactions = this.map.getInteractions();
        interactions.forEach(interaction => {
            if (interaction instanceof ol.interaction.DoubleClickZoom) {
                interaction.setActive(true);
            }
        });

        const mousePositionControl = new ol.control.MousePosition({
            coordinateFormat: ol.coordinate.createStringXY(6),
            projection: 'EPSG:4326',
            className: 'mouse-position',
            target: document.getElementById('mouse-position'),
            undefinedHTML: '&nbsp;'
        });

        this.map.addControl(mousePositionControl);
    },

    // 绑定地图事件
    bindMapEvents() {
        console.log('绑定地图事件监听器...');

        // 地图点击事件
        this.map.on('click', (event) => {
            const coordinate = event.coordinate;
            const lonLat = ol.proj.toLonLat(coordinate);

            console.log('🖱️ 地图被点击!', {
                pixel: event.pixel,
                coordinate: coordinate,
                lonLat: lonLat
            });

            // 检查点击位置是否有建筑物要素
            console.log('🎯 准备检查建筑物要素...');
            this.checkBuildingFeatureAtClick(event);

            // 触发自定义地图点击事件
            const mapClickEvent = new CustomEvent('mapClick', {
                detail: {
                    coordinate: coordinate,
                    lonLat: lonLat,
                    pixel: event.pixel
                }
            });
            document.dispatchEvent(mapClickEvent);
        });

        // 地图移动事件
        this.map.on('moveend', (event) => {
            const view = event.map.getView();
            const center = view.getCenter();
            const zoom = view.getZoom();
            const lonLatCenter = ol.proj.toLonLat(center);

            console.log('地图移动完成:', {
                center: lonLatCenter,
                zoom: zoom
            });

            // 触发自定义地图移动事件
            const mapMoveEvent = new CustomEvent('mapMove', {
                detail: {
                    center: lonLatCenter,
                    zoom: zoom
                }
            });
            document.dispatchEvent(mapMoveEvent);
        });

        // 地图缩放事件
        this.map.on('change:size', () => {
            console.log('地图尺寸变化，更新视图');
            this.map.updateSize();
        });

        // 窗口大小变化事件
        window.addEventListener('resize', () => {
            setTimeout(() => {
                this.map.updateSize();
            }, 100);
        });

        console.log('地图事件绑定完成');
    },

    // 获取地图实例
    getMap() {
        return this.map;
    },

    // 获取地图视图
    getView() {
        return this.view;
    },

    // 设置地图中心
    setCenter(lonLat, zoom = null) {
        if (!this.view) return;

        const center = ol.proj.fromLonLat(lonLat);
        this.view.setCenter(center);

        if (zoom !== null) {
            this.view.setZoom(zoom);
        }

        console.log('地图中心已设置:', lonLat, zoom ? `缩放级别: ${zoom}` : '');
    },

    // 设置地图缩放级别
    setZoom(zoom) {
        if (!this.view) return;

        this.view.setZoom(zoom);
        console.log('地图缩放级别已设置:', zoom);
    },

    // 适应指定范围
    fitExtent(extent, options = {}) {
        if (!this.view) return;

        const defaultOptions = {
            duration: 1000,
            padding: [20, 20, 20, 20]
        };

        const fitOptions = { ...defaultOptions, ...options };
        this.view.fit(extent, fitOptions);

        console.log('地图已适应范围:', extent);
    },

    // 添加图层
    addLayer(layer) {
        if (!this.map || !layer) return;

        this.map.addLayer(layer);
        console.log('图层已添加到地图');
    },

    // 移除图层
    removeLayer(layer) {
        if (!this.map || !layer) return;

        this.map.removeLayer(layer);
        console.log('图层已从地图移除');
    },

    // 获取所有图层
    getLayers() {
        return this.map ? this.map.getLayers() : null;
    },

    // 清除所有图层（保留基础图层）
    clearLayers() {
        if (!this.map) return;

        const layers = this.map.getLayers();
        const layersToRemove = [];

        layers.forEach(layer => {
            if (layer !== this.baseLayer) {
                layersToRemove.push(layer);
            }
        });

        layersToRemove.forEach(layer => {
            this.map.removeLayer(layer);
        });

        console.log('已清除所有非基础图层');
    },

    // 显示加载提示
    showLoading() {
        const loadingElement = document.getElementById('map-loading');
        if (loadingElement) {
            loadingElement.style.display = 'flex';
        }
    },

    // 隐藏加载提示
    hideLoading() {
        const loadingElement = document.getElementById('map-loading');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    },

    // 检查是否已初始化
    isMapInitialized() {
        return this.isInitialized && this.map !== null;
    },

    // 检查点击位置的建筑物要素
    async checkBuildingFeatureAtClick(event) {
        if (!this.map) return;

        console.log('🔍 开始检查点击位置的建筑物要素...');

        // 首先检查地图上有哪些图层
        const allLayers = this.map.getLayers().getArray();
        console.log('🗺️ 地图上的所有图层:', allLayers.map(layer => ({
            name: layer.get('name'),
            type: layer.get('type'),
            visible: layer.getVisible(),
            opacity: layer.getOpacity()
        })));

        try {
            // 使用forEachFeatureAtPixel检查点击位置的要素
            const feature = this.map.forEachFeatureAtPixel(event.pixel, (feature, layer) => {
                // 只处理建筑物图层的要素
                const layerName = layer.get('name');
                const layerType = layer.get('type');
                console.log('🔍 检查图层:', layerName, '类型:', layerType, '要素:', feature);

                if (layerName === 'buildings') {
                    console.log('✅ 找到建筑物图层的要素!');
                    return feature;
                }
                return null;
            });

            console.log('🔍 检查结果 - 找到的要素:', feature);

            if (feature) {
                console.log('🎯 找到建筑物要素，开始处理...');

                // 获取建筑物属性
                const properties = feature.getProperties();
                const buildingName = properties.name || properties.NAME || properties['建筑物名称'] || '未知建筑';

                console.log('🏢 建筑物属性详细信息:', {
                    name: buildingName,
                    'properties.name': properties.name,
                    'properties.NAME': properties.NAME,
                    'properties.建筑物名称': properties['建筑物名称'],
                    'all_properties': properties
                });

                // 计算建筑物面积（如果几何体存在）
                let area = 0;
                let geometryData = null;
                const geometry = feature.getGeometry();

                if (geometry) {
                    const geometryType = geometry.getType();

                    if (geometryType === 'Polygon') {
                        // 获取几何数据（转换为EPSG:4326）
                        const clonedGeometry = geometry.clone();
                        clonedGeometry.transform('EPSG:3857', 'EPSG:4326');
                        const coordinates = clonedGeometry.getCoordinates();

                        geometryData = {
                            type: 'Polygon',
                            coordinates: coordinates
                        };

                        // 使用投影功能计算准确面积（平方米）
                        area = this.calculatePolygonAreaForClick(coordinates[0]);

                    } else if (geometryType === 'Point') {
                        // 获取点几何数据
                        const clonedGeometry = geometry.clone();
                        clonedGeometry.transform('EPSG:3857', 'EPSG:4326');
                        geometryData = {
                            type: 'Point',
                            coordinates: clonedGeometry.getCoordinates()
                        };
                        area = 0; // 点要素没有面积
                    }
                }

                console.log('点击了建筑物:', {
                    name: buildingName,
                    area: area,
                    properties: properties,
                    geometry: geometryData
                });

                // 🎯 直接复用搜索模块的完美逻辑！
                console.log('🔄 复用搜索模块的建筑物显示逻辑...');

                // 先通过数据库API查询建筑物信息
                if (typeof ApiModule !== 'undefined') {
                    console.log('📡 调用数据库API查询建筑物信息...');
                    try {
                        const result = await ApiModule.searchBuildings(buildingName);

                        console.log('🔍 数据库查询结果:', result);

                        if (result.success && result.data && result.data.length > 0) {
                            console.log('📊 数据库返回的建筑物列表:', result.data);
                            console.log('🔍 正在查找建筑物:', buildingName);

                            // 找到匹配的建筑物
                            const matchedBuilding = result.data.find(building => {
                                const isMatch = building.name === buildingName ||
                                    building.name.includes(buildingName) ||
                                    buildingName.includes(building.name);
                                console.log(`🔍 检查建筑物: ${building.name} vs ${buildingName}, 匹配: ${isMatch}`);
                                return isMatch;
                            });

                            if (matchedBuilding) {
                                console.log('✅ 找到数据库中的建筑物信息:', matchedBuilding);

                                // 🎯 直接调用搜索模块的 selectBuilding 逻辑！
                                // 这个方法已经完美工作，包含了所有显示逻辑
                                if (typeof SearchModule !== 'undefined') {
                                    console.log('🔄 准备调用搜索模块显示逻辑...');

                                    // 构造一个临时的搜索结果数组
                                    const originalResults = SearchModule.currentResults;
                                    SearchModule.currentResults = [matchedBuilding];

                                    // 调用搜索模块的选择建筑物方法（索引0）
                                    SearchModule.selectBuilding(0);

                                    // 恢复原始搜索结果
                                    SearchModule.currentResults = originalResults;

                                    console.log('🎉 已通过搜索模块显示建筑物信息!');
                                    return; // 成功处理，直接返回
                                } else {
                                    console.error('❌ SearchModule未定义');
                                }
                            } else {
                                console.log('❌ 在数据库结果中未找到匹配的建筑物');
                            }
                        } else {
                            console.log('❌ 数据库查询失败或无结果:', result);
                        }

                        console.log('⚠️ 数据库中未找到建筑物信息，使用GeoJSON数据');
                    } catch (error) {
                        console.error('❌ 查询数据库建筑物信息失败:', error);
                    }
                } else {
                    console.log('⚠️ ApiModule未定义，使用GeoJSON数据');
                }

                // 备用方案：使用原有的显示逻辑
                this.showBuildingInfoFromDatabase(buildingName, {
                    name: buildingName,
                    area: area,
                    coordinate: event.coordinate,
                    properties: properties,
                    geometry: geometryData
                });
            }
        } catch (error) {
            console.error('检查建筑物要素时发生错误:', error);
        }
    },

    // 🎯 从数据库查询并显示建筑物信息
    async showBuildingInfoFromDatabase(buildingName, fallbackInfo) {
        console.log('🔍 从数据库查询建筑物信息:', buildingName);

        try {
            // 先显示基础信息（避免用户等待）
            this.createBuildingFlashEffect(fallbackInfo);

            // 查询数据库获取详细信息
            if (typeof ApiModule !== 'undefined') {
                console.log('📡 调用数据库API查询建筑物信息...');
                const result = await ApiModule.searchBuildings(buildingName);

                if (result.success && result.data && result.data.data && result.data.data.length > 0) {
                    const buildings = result.data.data;

                    // 找到精确匹配的建筑物
                    let matchedBuilding = buildings.find(b => b.name === buildingName);
                    if (!matchedBuilding) {
                        // 如果没有精确匹配，找包含匹配的
                        matchedBuilding = buildings.find(b =>
                            b.name.includes(buildingName) || buildingName.includes(b.name)
                        );
                    }

                    if (matchedBuilding) {
                        console.log('✅ 从数据库找到建筑物信息:', matchedBuilding);
                        console.log('🔍 数据库建筑物详细字段:', {
                            id: matchedBuilding.id,
                            name: matchedBuilding.name,
                            type: matchedBuilding.type,
                            description: matchedBuilding.description,
                            area: matchedBuilding.area,
                            floor_count: matchedBuilding.floor_count,
                            build_year: matchedBuilding.build_year,
                            status: matchedBuilding.status,
                            longitude: matchedBuilding.longitude,
                            latitude: matchedBuilding.latitude
                        });

                        // 合并数据库信息和GeoJSON几何信息
                        const enhancedBuildingInfo = {
                            // 数据库的详细信息
                            id: matchedBuilding.id,
                            name: matchedBuilding.name,
                            type: matchedBuilding.type,
                            description: matchedBuilding.description,
                            area: matchedBuilding.area,
                            floor_count: matchedBuilding.floor_count,
                            build_year: matchedBuilding.build_year,
                            status: matchedBuilding.status,
                            longitude: matchedBuilding.longitude,
                            latitude: matchedBuilding.latitude,

                            // GeoJSON的几何和坐标信息
                            coordinate: fallbackInfo.coordinate,
                            geometry: fallbackInfo.geometry,

                            // 合并属性
                            properties: {
                                ...fallbackInfo.properties,
                                // 数据库信息覆盖
                                name: matchedBuilding.name,
                                type: matchedBuilding.type,
                                description: matchedBuilding.description,
                                floor_count: matchedBuilding.floor_count,
                                area: matchedBuilding.area,
                                build_year: matchedBuilding.build_year,
                                status: matchedBuilding.status,
                                longitude: matchedBuilding.longitude,
                                latitude: matchedBuilding.latitude
                            }
                        };

                        console.log('🎯 合并后的建筑物信息:', enhancedBuildingInfo);

                        // 触发建筑物点击事件（显示数据库信息）
                        this.triggerBuildingClickEvent(enhancedBuildingInfo);

                        console.log('🎉 已显示数据库建筑物信息');
                        return;
                    }
                }

                console.log('⚠️ 数据库中未找到建筑物信息，使用GeoJSON数据');
            } else {
                console.log('⚠️ ApiModule未定义，使用GeoJSON数据');
            }

        } catch (error) {
            console.error('❌ 查询数据库建筑物信息失败:', error);
        }

        // 如果数据库查询失败，使用原有的GeoJSON信息
        console.log('📋 使用GeoJSON建筑物信息作为备选');
        this.triggerBuildingClickEvent(fallbackInfo);
    },

    // 显示建筑物信息（带动画）
    showBuildingInfo(building) {
        console.log('🏢 showBuildingInfo 被调用:', building?.name);
        console.log('🔍 buildingInfoPanel 状态:', !!this.buildingInfoPanel);

        if (!this.buildingInfoPanel) {
            console.error('❌ buildingInfoPanel 未找到，尝试重新获取...');
            this.buildingInfoPanel = document.getElementById('building-info-panel');
            if (!this.buildingInfoPanel) {
                console.error('❌ 无法找到 building-info-panel 元素');
                return;
            }
        }

        if (!building) {
            console.error('❌ building 参数为空');
            return;
        }

        console.log('✅ 开始显示建筑物信息:', building.name);

        // 创建建筑物闪烁效果（2秒）
        this.createBuildingFlashEffect(building);

        // 清除之前的自动隐藏定时器
        if (this.autoHideTimer) {
            clearTimeout(this.autoHideTimer);
            this.autoHideTimer = null;
        }

        // 更新信息内容
        this.updateBuildingInfoContent(building);

        // 先隐藏面板（如果已显示）
        if (this.buildingInfoPanel.classList.contains('show')) {
            this.buildingInfoPanel.classList.remove('show');
            this.buildingInfoPanel.classList.add('hide');

            // 等待隐藏动画完成后再显示新内容
            setTimeout(() => {
                this.buildingInfoPanel.classList.remove('hide');
                this.buildingInfoPanel.classList.add('show');
                console.log('✅ 建筑物信息面板已显示（重新显示）');

                // 设置5秒后自动渐隐
                this.setAutoHideTimer();
            }, 1000);
        } else {
            // 直接显示
            this.buildingInfoPanel.classList.remove('hide');
            this.buildingInfoPanel.classList.add('show');
            console.log('✅ 建筑物信息面板已显示（首次显示）');

            // 设置5秒后自动渐隐
            this.setAutoHideTimer();
        }

        // 触发建筑物点击事件
        this.triggerBuildingClickEvent(building);
    },

    // 设置自动隐藏定时器
    setAutoHideTimer() {
        this.autoHideTimer = setTimeout(() => {
            console.log('建筑物信息自动隐藏');
            this.hideBuildingInfo();
        }, 5000); // 5秒后自动隐藏（渐显1秒+显示4秒）
    },

    // 隐藏建筑物信息（带动画）
    hideBuildingInfo() {
        if (!this.buildingInfoPanel) return;

        console.log('隐藏建筑物信息');

        // 清除自动隐藏定时器
        if (this.autoHideTimer) {
            clearTimeout(this.autoHideTimer);
            this.autoHideTimer = null;
        }

        this.buildingInfoPanel.classList.remove('show');
        this.buildingInfoPanel.classList.add('hide');

        // 清除高亮显示
        if (typeof SearchModule !== 'undefined' && SearchModule.clearHighlightWithAnimation) {
            SearchModule.clearHighlightWithAnimation();
        }
    },

    // 更新建筑物信息内容
    updateBuildingInfoContent(building) {
        if (!building) {
            console.error('❌ updateBuildingInfoContent: building 参数为空');
            return;
        }

        console.log('🏢 updateBuildingInfoContent 被调用:', building.name);
        console.log('📊 建筑物数据结构:', {
            id: building.id,
            name: building.name,
            type: building.type,
            description: building.description,
            area: building.area,
            floor_count: building.floor_count,
            build_year: building.build_year,
            geometry: building.geometry,
            properties: building.properties,
            coordinate: building.coordinate
        });

        // 🔍 详细检查每个字段的值
        console.log('🔍 字段详细检查:');
        console.log('  - building.area:', building.area, typeof building.area);
        console.log('  - building.floor_count:', building.floor_count, typeof building.floor_count);
        console.log('  - building.build_year:', building.build_year, typeof building.build_year);
        console.log('  - building.description:', building.description, typeof building.description);
        console.log('  - building.type:', building.type, typeof building.type);

        // 使用建筑物分类器获取分类信息
        const category = BuildingClassifier.classifyBuilding(building.name);
        const styleConfig = BuildingClassifier.getBuildingStyle(building.name);

        // 更新名称
        const nameElement = document.getElementById('building-info-name');
        if (nameElement) {
            nameElement.textContent = building.name || '未知建筑';
        }

        // 更新类型（优先使用数据库的type字段，否则使用分类器）
        const typeElement = document.getElementById('building-info-type');
        if (typeElement) {
            const displayType = building.type || building.properties?.type || styleConfig.name;
            typeElement.innerHTML = `${styleConfig.icon} ${displayType}`;
        }

        // 更新面积（优先使用数据库字段）
        const areaElement = document.getElementById('building-info-area');
        if (areaElement) {
            const area = building.area || building.properties?.area;
            if (area && area > 0) {
                areaElement.textContent = `${Math.round(area)}m²`;
            } else {
                areaElement.textContent = '未知';
            }
        }

        // 更新坐标
        const coordinatesElement = document.getElementById('building-info-coordinates');
        if (coordinatesElement) {
            let coordText = '未知';

            try {
                // 优先使用geometry中的坐标
                if (building.geometry && building.geometry.coordinates) {
                    if (building.geometry.type === 'Point') {
                        const coords = building.geometry.coordinates;
                        const lon = parseFloat(coords[0]);
                        const lat = parseFloat(coords[1]);
                        if (!isNaN(lon) && !isNaN(lat)) {
                            coordText = `${lon.toFixed(6)}, ${lat.toFixed(6)}`;
                        }
                    } else if (building.geometry.type === 'Polygon') {
                        // 计算多边形中心点
                        const coords = building.geometry.coordinates[0];
                        let sumLon = 0, sumLat = 0;
                        coords.forEach(coord => {
                            sumLon += parseFloat(coord[0]);
                            sumLat += parseFloat(coord[1]);
                        });
                        const centerLon = sumLon / coords.length;
                        const centerLat = sumLat / coords.length;
                        if (!isNaN(centerLon) && !isNaN(centerLat)) {
                            coordText = `${centerLon.toFixed(6)}, ${centerLat.toFixed(6)}`;
                        }
                    }
                }
                // 备用：使用properties中的坐标
                else if (building.properties?.longitude && building.properties?.latitude) {
                    const lon = parseFloat(building.properties.longitude);
                    const lat = parseFloat(building.properties.latitude);
                    if (!isNaN(lon) && !isNaN(lat)) {
                        coordText = `${lon.toFixed(6)}, ${lat.toFixed(6)}`;
                    }
                }
                // 最后备用：使用coordinate数组
                else if (building.coordinate && building.coordinate.length >= 2) {
                    const lon = parseFloat(building.coordinate[0]);
                    const lat = parseFloat(building.coordinate[1]);
                    if (!isNaN(lon) && !isNaN(lat)) {
                        coordText = `${lon.toFixed(6)}, ${lat.toFixed(6)}`;
                    }
                }
            } catch (error) {
                console.error('坐标解析错误:', error);
                coordText = '坐标解析失败';
            }

            coordinatesElement.textContent = coordText;
        }

        // 更新楼层数（优先使用数据库字段）
        const floorsElement = document.getElementById('building-info-floors');
        if (floorsElement) {
            const floors = building.floor_count || building.properties?.floor_count;
            if (floors && floors > 0) {
                floorsElement.textContent = `${floors}层`;
            } else {
                floorsElement.textContent = '未知';
            }
        }

        // 更新建造年份（优先使用数据库字段）
        const yearElement = document.getElementById('building-info-year');
        if (yearElement) {
            const year = building.build_year || building.properties?.build_year;
            if (year) {
                yearElement.textContent = `${year}年`;
            } else {
                yearElement.textContent = '未知';
            }
        }

        // 更新描述（优先使用数据库字段）
        const descriptionElement = document.getElementById('building-info-description');
        const descriptionItem = document.getElementById('building-info-description-item');
        if (descriptionElement && descriptionItem) {
            const description = building.description || building.properties?.description;
            if (description && description.trim()) {
                descriptionElement.textContent = description;
                descriptionItem.style.display = 'flex';
            } else {
                descriptionItem.style.display = 'none';
            }
        }

        console.log('建筑物信息内容已更新');
    },

    // 触发建筑物点击事件
    triggerBuildingClickEvent(buildingInfo) {
        const event = new CustomEvent('buildingClick', {
            detail: { building: buildingInfo }
        });
        document.dispatchEvent(event);
        console.log('🎯 已触发建筑物点击事件:', buildingInfo.name);
    },

    // 创建建筑物闪烁效果
    createBuildingFlashEffect(buildingInfo) {
        if (!buildingInfo.geometry || !this.map) return;

        try {
            // 创建闪烁图层
            const flashSource = new ol.source.Vector();
            const flashLayer = new ol.layer.Vector({
                source: flashSource,
                zIndex: 9999 // 确保在最上层
            });

            // 创建几何对象
            let geometry;
            if (buildingInfo.geometry.type === 'Polygon') {
                geometry = new ol.geom.Polygon(buildingInfo.geometry.coordinates);
                geometry.transform('EPSG:4326', 'EPSG:3857');
            } else if (buildingInfo.geometry.type === 'Point') {
                geometry = new ol.geom.Point(buildingInfo.geometry.coordinates);
                geometry.transform('EPSG:4326', 'EPSG:3857');
            }

            if (geometry) {
                // 创建闪烁要素
                const flashFeature = new ol.Feature({
                    geometry: geometry,
                    name: buildingInfo.name
                });

                // 创建闪烁样式
                const flashStyle = new ol.style.Style({
                    fill: new ol.style.Fill({
                        color: 'rgba(255, 255, 0, 0.6)' // 黄色半透明
                    }),
                    stroke: new ol.style.Stroke({
                        color: '#ffff00', // 黄色边框
                        width: 3
                    })
                });

                flashFeature.setStyle(flashStyle);
                flashSource.addFeature(flashFeature);
                this.map.addLayer(flashLayer);

                // 创建闪烁动画
                let opacity = 0.6;
                let direction = -1;
                const flashInterval = setInterval(() => {
                    opacity += direction * 0.1;
                    if (opacity <= 0.2) {
                        direction = 1;
                    } else if (opacity >= 0.8) {
                        direction = -1;
                    }

                    // 更新样式透明度
                    const newStyle = new ol.style.Style({
                        fill: new ol.style.Fill({
                            color: `rgba(255, 255, 0, ${opacity})`
                        }),
                        stroke: new ol.style.Stroke({
                            color: '#ffff00',
                            width: 3
                        })
                    });
                    flashFeature.setStyle(newStyle);
                }, 100);

                // 2秒后清除闪烁效果
                setTimeout(() => {
                    clearInterval(flashInterval);
                    this.map.removeLayer(flashLayer);
                    console.log('建筑物闪烁效果已清除');
                }, 2000);

                console.log('建筑物闪烁效果已创建，持续2秒');
            }
        } catch (error) {
            console.error('创建建筑物闪烁效果失败:', error);
        }
    },

    // 现在使用数据库驱动的建筑物信息面板

    // 使用投影功能计算多边形面积（平方米）- 用于点击事件
    calculatePolygonAreaForClick(coordinates) {
        if (!coordinates || coordinates.length < 3) {
            return 0;
        }

        try {
            // 使用球面几何计算面积（考虑地球曲率）
            // 将经纬度坐标转换为弧度
            const toRadians = (degrees) => degrees * Math.PI / 180;

            // 地球半径（米）
            const EARTH_RADIUS = 6378137;

            let area = 0;
            const n = coordinates.length - 1; // 排除重复的最后一个点

            for (let i = 0; i < n; i++) {
                const j = (i + 1) % n;

                const lat1 = toRadians(coordinates[i][1]);
                const lon1 = toRadians(coordinates[i][0]);
                const lat2 = toRadians(coordinates[j][1]);
                const lon2 = toRadians(coordinates[j][0]);

                // 使用球面三角形面积公式
                area += (lon2 - lon1) * (2 + Math.sin(lat1) + Math.sin(lat2));
            }

            // 计算最终面积（平方米）
            area = Math.abs(area * EARTH_RADIUS * EARTH_RADIUS / 2);

            console.log(`点击计算多边形面积: ${Math.round(area)}m²`);
            return Math.round(area);

        } catch (error) {
            console.error('点击计算多边形面积失败:', error);
            return 0;
        }
    },

    // 销毁地图
    destroy() {
        console.log('销毁地图核心模块');

        if (this.map) {
            this.map.setTarget(null);
            this.map = null;
        }

        this.view = null;
        this.mapContainer = null;
        this.baseLayer = null;
        this.isInitialized = false;
    }
};

// 地图初始化已移至统一管理器

// 导出地图核心模块（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports.MapModule = MapModule;
}

// 在全局作用域中设置MapModule
if (typeof window !== 'undefined') {
    window.MapModule = MapModule;
}

console.log('地图核心模块已定义');

// ============================================================================
// GeoJSON图层加载模块 - LayerModule
// ============================================================================

const LayerModule = {
    // 模块状态
    loadedLayers: new Map(), // 已加载的图层
    isLoading: false, // 是否正在加载
    loadingProgress: 0, // 加载进度
    totalLayers: 0, // 总图层数
    loadedCount: 0, // 已加载数量

    // 初始化图层模块
    init() {
        console.log('初始化GeoJSON图层加载模块...');

        // 检查地图模块是否已初始化
        if (!MapModule.isMapInitialized()) {
            console.error('地图模块未初始化，无法加载图层');
            return false;
        }

        console.log('GeoJSON图层加载模块初始化完成');
        return true;
    },

    // 加载所有图层
    async loadAllLayers() {
        if (this.isLoading) {
            console.log('图层正在加载中，请稍候...');
            return;
        }

        console.log('开始加载所有GeoJSON图层...');
        this.isLoading = true;
        this.loadingProgress = 0;
        this.loadedCount = 0;

        // 显示地图加载提示
        MapModule.showLoading();

        try {
            // 获取图层配置
            const layerConfigs = CONFIG.layers;
            const layerNames = Object.keys(layerConfigs);
            this.totalLayers = layerNames.length;

            console.log(`准备加载 ${this.totalLayers} 个图层:`, layerNames);

            // 按优先级顺序加载图层
            const loadOrder = ['buildings', 'roads', 'rivers', 'waters', 'traffic', 'boundary'];
            const orderedLayers = [];

            // 按指定顺序排列
            loadOrder.forEach(name => {
                if (layerConfigs[name]) {
                    orderedLayers.push({ name, url: layerConfigs[name] });
                }
            });

            // 添加其他未指定顺序的图层
            layerNames.forEach(name => {
                if (!loadOrder.includes(name)) {
                    orderedLayers.push({ name, url: layerConfigs[name] });
                }
            });

            // 逐个加载图层
            for (const { name, url } of orderedLayers) {
                try {
                    console.log(`加载图层: ${name} (${url})`);
                    await this.loadGeoJSONLayer(name, url);
                    this.loadedCount++;
                    this.loadingProgress = Math.round((this.loadedCount / this.totalLayers) * 100);
                    console.log(`图层 ${name} 加载完成 (${this.loadingProgress}%)`);

                    // 触发进度更新事件
                    this.triggerProgressEvent(name, this.loadingProgress);

                } catch (error) {
                    console.error(`图层 ${name} 加载失败:`, error);
                    // 继续加载其他图层
                }
            }

            console.log('所有图层加载完成');
            this.triggerLoadCompleteEvent();

        } catch (error) {
            console.error('图层加载过程中发生错误:', error);
        } finally {
            this.isLoading = false;
            MapModule.hideLoading();
        }
    },

    // 加载单个GeoJSON图层
    async loadGeoJSONLayer(layerName, url) {
        return new Promise((resolve, reject) => {
            console.log(`开始加载GeoJSON图层: ${layerName} 从 ${url}`);

            try {
                // 创建矢量数据源
                const vectorSource = new ol.source.Vector({
                    url: url,
                    format: new ol.format.GeoJSON({
                        dataProjection: 'EPSG:3857', // 数据坐标系 - 已转换为投影坐标系
                        featureProjection: 'EPSG:3857' // 地图坐标系 - 保持一致
                    })
                });

                // 监听数据加载完成事件，进行坐标校验
                vectorSource.on('featuresloadend', () => {
                    console.log(`图层 ${layerName} 数据加载完成，进行坐标校验`);
                    this.validateLayerCoordinates(layerName, vectorSource);
                });

                // 创建图层样式
                const style = this.createLayerStyle(layerName);

                // 创建矢量图层
                const vectorLayer = new ol.layer.Vector({
                    source: vectorSource,
                    style: style,
                    opacity: this.getLayerOpacity(layerName),
                    visible: this.getLayerVisibility(layerName),
                    zIndex: this.getLayerZIndex(layerName)
                });

                // 设置图层属性
                vectorLayer.set('name', layerName);
                vectorLayer.set('type', 'geojson');

                // 监听数据加载完成事件
                vectorSource.on('featuresloadend', () => {
                    const featureCount = vectorSource.getFeatures().length;
                    console.log(`图层 ${layerName} 加载完成，包含 ${featureCount} 个要素`);

                    // 存储图层引用，包含完整状态信息
                    this.loadedLayers.set(layerName, {
                        layer: vectorLayer,
                        source: vectorSource,
                        featureCount: featureCount,
                        loadTime: Date.now(),
                        visible: vectorLayer.getVisible(),
                        opacity: vectorLayer.getOpacity()
                    });

                    resolve(vectorLayer);
                });

                // 监听数据加载错误事件
                vectorSource.on('featuresloaderror', (event) => {
                    console.error(`图层 ${layerName} 加载失败:`, event);
                    console.error(`图层URL: ${url}`);

                    // 提供详细的错误信息
                    const errorMsg = `图层 ${layerName} 加载失败，请检查文件路径: ${url}`;
                    console.error(errorMsg);

                    reject(new Error(errorMsg));
                });

                // 添加图层到地图
                MapModule.addLayer(vectorLayer);

            } catch (error) {
                console.error(`创建图层 ${layerName} 时发生错误:`, error);
                reject(error);
            }
        });
    },

    // 尝试从JavaScript数据加载（CORS备用方案）
    tryLoadFromJavaScriptData(layerName, vectorLayer, vectorSource) {
        console.log(`尝试使用JavaScript备用数据加载图层: ${layerName}`);

        // 获取对应的GeoJSON数据对象
        let geojsonData = null;
        switch(layerName) {
            case 'buildings':
                geojsonData = typeof buildingsGeoJSON !== 'undefined' ? buildingsGeoJSON : null;
                break;
            case 'roads':
                geojsonData = typeof roadsGeoJSON !== 'undefined' ? roadsGeoJSON : null;
                break;
            case 'waters':
                geojsonData = typeof watersGeoJSON !== 'undefined' ? watersGeoJSON : null;
                break;
            case 'boundary':
                geojsonData = typeof boundaryGeoJSON !== 'undefined' ? boundaryGeoJSON : null;
                break;
            case 'rivers':
                geojsonData = typeof riversGeoJSON !== 'undefined' ? riversGeoJSON : null;
                break;
            case 'traffic':
                geojsonData = typeof trafficGeoJSON !== 'undefined' ? trafficGeoJSON : null;
                break;
            default:
                return false;
        }

        if (geojsonData) {
            try {
                // 清除原有数据源
                vectorSource.clear();

                // 使用JavaScript数据创建要素
                const features = (new ol.format.GeoJSON({
                    dataProjection: 'EPSG:3857', // 数据已转换为投影坐标系
                    featureProjection: 'EPSG:3857' // 地图坐标系保持一致
                })).readFeatures(geojsonData);

                // 添加要素到数据源
                vectorSource.addFeatures(features);

                const featureCount = features.length;
                console.log(`图层 ${layerName} 使用JavaScript数据加载完成，包含 ${featureCount} 个要素`);

                // 存储图层引用，包含完整状态信息
                this.loadedLayers.set(layerName, {
                    layer: vectorLayer,
                    source: vectorSource,
                    featureCount: featureCount,
                    loadTime: Date.now(),
                    visible: vectorLayer.getVisible(),
                    opacity: vectorLayer.getOpacity()
                });

                return true;
            } catch (error) {
                console.error(`使用JavaScript数据加载图层 ${layerName} 失败:`, error);
                return false;
            }
        }

        return false;
    },

    // 验证图层坐标是否正确
    validateLayerCoordinates(layerName, vectorSource) {
        const features = vectorSource.getFeatures();
        if (features.length === 0) return;

        console.log(`验证图层 ${layerName} 的坐标系统`);

        // 获取第一个要素的坐标范围
        const firstFeature = features[0];
        const geometry = firstFeature.getGeometry();

        if (geometry) {
            const extent = geometry.getExtent();
            const center = ol.extent.getCenter(extent);

            console.log(`图层 ${layerName} 中心坐标 (EPSG:3857): [${center[0].toFixed(2)}, ${center[1].toFixed(2)}]`);

            // 转换为经纬度坐标进行检查
            const centerLonLat = ol.proj.toLonLat(center);
            console.log(`图层 ${layerName} 中心坐标 (经纬度): [${centerLonLat[0].toFixed(6)}, ${centerLonLat[1].toFixed(6)}]`);

            // 检查坐标是否在合理范围内（校园区域）
            const expectedBounds = CONFIG.map.extent;
            const isInBounds = centerLonLat[0] >= expectedBounds[0] &&
                              centerLonLat[0] <= expectedBounds[2] &&
                              centerLonLat[1] >= expectedBounds[1] &&
                              centerLonLat[1] <= expectedBounds[3];

            if (!isInBounds) {
                console.warn(`⚠️ 图层 ${layerName} 坐标可能偏移，中心点超出预期范围`);
                console.warn(`预期范围: [${expectedBounds[0]}, ${expectedBounds[1]}] - [${expectedBounds[2]}, ${expectedBounds[3]}]`);
                console.warn(`实际中心: [${centerLonLat[0].toFixed(6)}, ${centerLonLat[1].toFixed(6)}]`);
            } else {
                console.log(`✅ 图层 ${layerName} 坐标系统正常`);
            }
        }
    },

    // 创建图层样式
    createLayerStyle(layerName) {
        const styleConfig = CONFIG.styles && CONFIG.styles.layerStyles ? CONFIG.styles.layerStyles[layerName] : null;

        if (!styleConfig) {
            console.warn(`未找到图层 ${layerName} 的样式配置，使用默认样式`);
            return this.getDefaultStyle();
        }

        // 根据图层类型创建不同样式
        switch (layerName) {
            case 'buildings':
                // 为建筑物创建动态样式函数，根据建筑物名称分类显示不同颜色
                return (feature) => {
                    const properties = feature.getProperties();
                    const buildingName = properties.name || '未知';

                    // 使用建筑物分类器获取分类和样式
                    const category = BuildingClassifier.classifyBuilding(buildingName);
                    const buildingStyle = BuildingClassifier.getBuildingStyle(buildingName);

                    // 检查该分类是否应该显示
                    const categoryKey = this.getCategoryKey(category);
                    if (LayerControlModule.isInitialized &&
                        LayerControlModule.buildingCategoryStates[categoryKey] === false) {
                        // 如果该分类被隐藏，返回null不显示
                        return null;
                    }

                    return new ol.style.Style({
                        fill: new ol.style.Fill({
                            color: buildingStyle.fill
                        }),
                        stroke: new ol.style.Stroke({
                            color: buildingStyle.stroke,
                            width: buildingStyle.strokeWidth
                        })
                    });
                };

            case 'roads':
                return new ol.style.Style({
                    stroke: new ol.style.Stroke({
                        color: styleConfig.stroke,
                        width: styleConfig.strokeWidth,
                        lineCap: 'round',
                        lineJoin: 'round'
                    })
                });

            case 'rivers':
                return new ol.style.Style({
                    fill: new ol.style.Fill({
                        color: styleConfig.fill
                    }),
                    stroke: new ol.style.Stroke({
                        color: styleConfig.stroke,
                        width: styleConfig.strokeWidth
                    })
                });

            case 'waters':
                return new ol.style.Style({
                    fill: new ol.style.Fill({
                        color: styleConfig.fill
                    }),
                    stroke: new ol.style.Stroke({
                        color: styleConfig.stroke,
                        width: styleConfig.strokeWidth || 1
                    })
                });

            case 'traffic':
                return new ol.style.Style({
                    image: new ol.style.Circle({
                        radius: styleConfig.radius || 4,
                        fill: new ol.style.Fill({
                            color: styleConfig.fill
                        }),
                        stroke: new ol.style.Stroke({
                            color: styleConfig.stroke,
                            width: styleConfig.strokeWidth
                        })
                    })
                });

            case 'boundary':
                return new ol.style.Style({
                    stroke: new ol.style.Stroke({
                        color: styleConfig.stroke,
                        width: styleConfig.strokeWidth,
                        lineDash: [5, 5] // 虚线边界
                    })
                });

            default:
                return this.getDefaultStyle();
        }
    },

    // 获取默认样式
    getDefaultStyle() {
        return new ol.style.Style({
            fill: new ol.style.Fill({
                color: 'rgba(0, 100, 200, 0.3)'
            }),
            stroke: new ol.style.Stroke({
                color: '#0064c8',
                width: 1
            })
        });
    },

    // 获取图层透明度
    getLayerOpacity(layerName) {
        const opacityConfig = CONFIG.styles && CONFIG.styles.layerStyles && CONFIG.styles.layerStyles[layerName];
        return opacityConfig && opacityConfig.opacity !== undefined ? opacityConfig.opacity : 1.0;
    },

    // 获取图层可见性
    getLayerVisibility() {
        // 默认所有图层可见，可以根据需要调整
        return true;
    },

    // 获取建筑物分类对应的键名
    getCategoryKey(category) {
        const categoryKeyMap = {
            '教学建筑': 'building-teaching',
            '公共建筑': 'building-public',
            '宿舍建筑': 'building-dormitory',
            '体育建筑': 'building-sports',
            '行政建筑': 'building-admin',
            '食堂': 'building-dining',
            '服务建筑': 'building-service',
            '学院建筑': 'building-college',
            '公共停车场': 'building-parking',
            '未知': 'building-unknown'
        };
        return categoryKeyMap[category] || 'building-unknown';
    },

    // 获取指定图层
    getLayer(layerName) {
        const layerInfo = this.loadedLayers.get(layerName);
        return layerInfo ? layerInfo.layer : null;
    },

    // 获取图层层级
    getLayerZIndex(layerName) {
        const zIndexMap = {
            'boundary': 1,
            'waters': 2,
            'rivers': 3,
            'roads': 4,
            'buildings': 5,
            'traffic': 6
        };
        return zIndexMap[layerName] || 0;
    },

    // 触发进度更新事件
    triggerProgressEvent(layerName, progress) {
        const event = new CustomEvent('layerLoadProgress', {
            detail: {
                layerName: layerName,
                progress: progress,
                loadedCount: this.loadedCount,
                totalLayers: this.totalLayers
            }
        });
        document.dispatchEvent(event);
    },

    // 触发加载完成事件
    triggerLoadCompleteEvent() {
        const event = new CustomEvent('layersLoadComplete', {
            detail: {
                loadedLayers: Array.from(this.loadedLayers.keys()),
                totalCount: this.loadedLayers.size,
                loadTime: Date.now()
            }
        });
        document.dispatchEvent(event);
    },

    // 获取已加载的图层
    getLoadedLayer(layerName) {
        return this.loadedLayers.get(layerName);
    },

    // 获取所有已加载的图层
    getAllLoadedLayers() {
        return this.loadedLayers;
    },

    // 切换图层可见性
    toggleLayerVisibility(layerName) {
        console.log(`切换图层可见性: ${layerName}`);

        if (!layerName) {
            console.error('图层名称不能为空');
            return false;
        }

        const layerInfo = this.loadedLayers.get(layerName);
        if (!layerInfo) {
            console.warn(`图层 ${layerName} 未找到或未加载`);
            return false;
        }

        if (!layerInfo.layer) {
            console.error(`图层 ${layerName} 的layer对象不存在`);
            return false;
        }

        try {
            const currentVisibility = layerInfo.layer.getVisible();
            const newVisibility = !currentVisibility;
            layerInfo.layer.setVisible(newVisibility);

            // 更新内部状态记录
            layerInfo.visible = newVisibility;

            console.log(`图层 ${layerName} 可见性切换为: ${newVisibility}`);

            // 触发图层状态变化事件
            this.triggerLayerStateChangeEvent(layerName, 'visibility', newVisibility);

            return newVisibility;
        } catch (error) {
            console.error(`切换图层 ${layerName} 可见性时发生错误:`, error);
            return false;
        }
    },

    // 设置图层透明度
    setLayerOpacity(layerName, opacity) {
        console.log(`设置图层透明度: ${layerName} = ${opacity}`);

        if (!layerName) {
            console.error('图层名称不能为空');
            return false;
        }

        if (typeof opacity !== 'number' || opacity < 0 || opacity > 1) {
            console.error('透明度值必须是0-1之间的数字');
            return false;
        }

        const layerInfo = this.loadedLayers.get(layerName);
        if (!layerInfo) {
            console.warn(`图层 ${layerName} 未找到或未加载`);
            return false;
        }

        if (!layerInfo.layer) {
            console.error(`图层 ${layerName} 的layer对象不存在`);
            return false;
        }

        try {
            layerInfo.layer.setOpacity(opacity);

            // 更新内部状态记录
            layerInfo.opacity = opacity;

            console.log(`图层 ${layerName} 透明度设置为: ${opacity}`);

            // 触发图层状态变化事件
            this.triggerLayerStateChangeEvent(layerName, 'opacity', opacity);

            return true;
        } catch (error) {
            console.error(`设置图层 ${layerName} 透明度时发生错误:`, error);
            return false;
        }
    },

    // 获取所有图层状态
    getAllLayersStatus() {
        console.log('获取所有图层状态');

        const layersStatus = {};

        this.loadedLayers.forEach((layerInfo, layerName) => {
            if (layerInfo.layer) {
                layersStatus[layerName] = {
                    visible: layerInfo.layer.getVisible(),
                    opacity: layerInfo.layer.getOpacity(),
                    zIndex: layerInfo.layer.getZIndex(),
                    featureCount: layerInfo.featureCount || 0,
                    loadTime: layerInfo.loadTime || null,
                    isLoaded: true
                };
            } else {
                layersStatus[layerName] = {
                    visible: false,
                    opacity: 0,
                    zIndex: 0,
                    featureCount: 0,
                    loadTime: null,
                    isLoaded: false
                };
            }
        });

        console.log('图层状态信息:', layersStatus);
        return layersStatus;
    },

    // 触发图层状态变化事件
    triggerLayerStateChangeEvent(layerName, property, value) {
        const event = new CustomEvent('layerStateChange', {
            detail: {
                layerName: layerName,
                property: property,
                value: value,
                timestamp: Date.now(),
                allLayersStatus: this.getAllLayersStatus()
            }
        });
        document.dispatchEvent(event);
        console.log(`图层状态变化事件已触发: ${layerName}.${property} = ${value}`);
    },

    // 移除图层
    removeLayer(layerName) {
        const layerInfo = this.loadedLayers.get(layerName);
        if (layerInfo && layerInfo.layer) {
            MapModule.removeLayer(layerInfo.layer);
            this.loadedLayers.delete(layerName);
            console.log(`图层 ${layerName} 已移除`);
            return true;
        }
        return false;
    },

    // 清除所有图层
    clearAllLayers() {
        console.log('清除所有GeoJSON图层');

        this.loadedLayers.forEach((layerInfo) => {
            if (layerInfo.layer) {
                MapModule.removeLayer(layerInfo.layer);
            }
        });

        this.loadedLayers.clear();
        this.loadingProgress = 0;
        this.loadedCount = 0;

        console.log('所有GeoJSON图层已清除');
    },

    // 重新加载图层
    async reloadLayer(layerName) {
        console.log(`重新加载图层: ${layerName}`);

        // 先移除现有图层
        this.removeLayer(layerName);

        // 重新加载
        const layerUrl = CONFIG.layers[layerName];
        if (layerUrl) {
            try {
                await this.loadGeoJSONLayer(layerName, layerUrl);
                console.log(`图层 ${layerName} 重新加载完成`);
            } catch (error) {
                console.error(`图层 ${layerName} 重新加载失败:`, error);
            }
        }
    },

    // 获取图层统计信息
    getLayerStats() {
        const stats = {
            totalLayers: this.totalLayers,
            loadedLayers: this.loadedCount,
            loadingProgress: this.loadingProgress,
            isLoading: this.isLoading,
            layerDetails: {}
        };

        this.loadedLayers.forEach((layerInfo, layerName) => {
            stats.layerDetails[layerName] = {
                featureCount: layerInfo.featureCount,
                visible: layerInfo.layer.getVisible(),
                opacity: layerInfo.layer.getOpacity(),
                loadTime: layerInfo.loadTime
            };
        });

        return stats;
    },

    // 销毁模块
    destroy() {
        console.log('销毁GeoJSON图层加载模块');

        this.clearAllLayers();
        this.isLoading = false;
        this.loadingProgress = 0;
        this.totalLayers = 0;
        this.loadedCount = 0;
    }
};

// 修改登录成功事件监听器，添加图层加载
document.addEventListener('loginSuccess', (event) => {
    console.log('收到登录成功事件，时间戳:', event.detail.timestamp);

    // 延迟初始化地图，确保界面切换完成
    setTimeout(() => {
        console.log('开始初始化地图模块...');
        if (MapModule.init()) {
            console.log('地图模块初始化成功');

            // 地图初始化完成后，再初始化并加载图层
            setTimeout(() => {
                console.log('开始初始化图层模块...');
                if (LayerModule.init()) {
                    console.log('图层模块初始化成功，开始加载图层数据...');
                    LayerModule.loadAllLayers();
                }
            }, 1000);
        } else {
            console.error('地图模块初始化失败');
        }
    }, 500);
});

// 监听图层加载进度事件
document.addEventListener('layerLoadProgress', (event) => {
    const { layerName, progress, loadedCount, totalLayers } = event.detail;
    console.log(`图层加载进度: ${layerName} - ${progress}% (${loadedCount}/${totalLayers})`);
});

// 监听图层加载完成事件
document.addEventListener('layersLoadComplete', (event) => {
    const { loadedLayers, totalCount } = event.detail;
    console.log(`所有图层加载完成! 共加载 ${totalCount} 个图层:`, loadedLayers);

    // 触发自定义事件，通知其他模块图层加载完成
    const mapReadyEvent = new CustomEvent('mapReady', {
        detail: {
            timestamp: Date.now(),
            layerCount: totalCount,
            layers: loadedLayers
        }
    });
    document.dispatchEvent(mapReadyEvent);
});

// 导出图层模块（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports.LayerModule = LayerModule;
}

// 在全局作用域中设置LayerModule
if (typeof window !== 'undefined') {
    window.LayerModule = LayerModule;

    // 添加全局重新加载buildings图层的函数
    window.reloadBuildingsLayer = async function() {
        try {
            console.log('开始重新加载buildings图层...');
            await LayerModule.reloadLayer('buildings');
            console.log('Buildings图层重新加载完成！');
            return { success: true, message: 'Buildings图层重新加载成功' };
        } catch (error) {
            console.error('Buildings图层重新加载失败:', error);
            return { success: false, message: `重新加载失败: ${error.message}` };
        }
    };
}

console.log('GeoJSON图层加载模块已定义');

// ============================================================================
// 建筑物搜索模块 - SearchModule
// ============================================================================

const SearchModule = {
    // 模块状态
    searchInput: null, // 搜索输入框
    searchResults: null, // 搜索结果容器
    buildingsData: [], // 建筑物数据
    currentResults: [], // 当前搜索结果
    selectedIndex: -1, // 当前选中的结果索引
    searchTimeout: null, // 搜索防抖定时器
    highlightLayer: null, // 高亮图层
    buildingInfoPanel: null, // 建筑物信息面板
    buildingInfoClose: null, // 关闭按钮
    currentHighlightFeature: null, // 当前高亮的要素
    highlightAnimationId: null, // 高亮动画ID
    autoHideTimer: null, // 自动隐藏定时器
    isInitialized: false, // 初始化状态

    // 初始化搜索模块
    init() {
        console.log('初始化建筑物搜索模块...');

        // 获取DOM元素
        this.searchInput = document.getElementById('search-input');
        this.searchResults = document.getElementById('search-results');
        this.buildingInfoPanel = document.getElementById('building-info-panel');
        this.buildingInfoClose = document.getElementById('building-info-close');

        if (!this.searchInput) {
            console.error('搜索输入框元素未找到');
            return false;
        }

        if (!this.buildingInfoPanel) {
            console.error('建筑物信息面板元素未找到');
            return false;
        }

        // 创建搜索结果容器（如果不存在）
        if (!this.searchResults) {
            this.createSearchResultsContainer();
        }

        // 绑定信息面板事件
        this.bindInfoPanelEvents();

        // 绑定建筑物点击事件
        this.bindBuildingClickEvents();

        // 创建高亮图层
        this.createHighlightLayer();

        // 绑定搜索事件
        this.bindSearchEvents();

        // 加载建筑物数据
        this.loadBuildingsData();

        this.isInitialized = true;
        console.log('建筑物搜索模块初始化完成');
        return true;
    },

    // 创建搜索结果容器
    createSearchResultsContainer() {
        console.log('创建搜索结果容器');

        this.searchResults = document.createElement('div');
        this.searchResults.id = 'search-results';
        this.searchResults.className = 'search-results';
        this.searchResults.style.display = 'none';

        // 插入到搜索输入框后面
        if (this.searchInput && this.searchInput.parentNode) {
            this.searchInput.parentNode.insertBefore(this.searchResults, this.searchInput.nextSibling);
        }
    },

    // 创建高亮图层
    createHighlightLayer() {
        if (!MapModule.isMapInitialized()) {
            console.warn('地图未初始化，无法创建高亮图层');
            return;
        }

        console.log('创建建筑物高亮图层');

        // 创建高亮样式函数，支持不同几何类型
        const highlightStyleFunction = (feature) => {
            const geometry = feature.getGeometry();
            const geometryType = geometry.getType();

            // 基础样式配置
            const baseStyle = {
                fill: new ol.style.Fill({
                    color: 'rgba(255, 255, 0, 0.6)' // 亮黄色填充，半透明
                }),
                stroke: new ol.style.Stroke({
                    color: '#FFD700', // 金黄色边框
                    width: 4,
                    lineDash: [10, 5] // 虚线效果
                })
            };

            // 根据几何类型创建不同样式
            if (geometryType === 'Point') {
                return new ol.style.Style({
                    image: new ol.style.Circle({
                        radius: 15,
                        fill: new ol.style.Fill({
                            color: 'rgba(255, 255, 0, 0.8)'
                        }),
                        stroke: new ol.style.Stroke({
                            color: '#FFD700',
                            width: 3
                        })
                    })
                });
            } else {
                // 多边形或其他几何类型
                return new ol.style.Style(baseStyle);
            }
        };

        // 创建矢量数据源和图层
        const highlightSource = new ol.source.Vector();
        this.highlightLayer = new ol.layer.Vector({
            source: highlightSource,
            style: highlightStyleFunction,
            zIndex: 999 // 确保高亮图层在最上层
        });

        // 添加到地图
        MapModule.addLayer(this.highlightLayer);
    },

    // 绑定搜索事件
    bindSearchEvents() {
        console.log('绑定搜索事件监听器');

        // 输入事件 - 防抖搜索
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });

        // 键盘导航事件
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeyboardNavigation(e);
        });

        // 失焦事件 - 延迟隐藏结果
        this.searchInput.addEventListener('blur', () => {
            setTimeout(() => {
                this.hideSearchResults();
            }, 200);
        });

        // 聚焦事件 - 显示结果（如果有）
        this.searchInput.addEventListener('focus', () => {
            if (this.currentResults.length > 0) {
                this.showSearchResults();
            }
        });

        console.log('搜索事件绑定完成');
    },

    // 绑定信息面板事件
    bindInfoPanelEvents() {
        console.log('绑定建筑物信息面板事件');

        // 关闭按钮事件
        if (this.buildingInfoClose) {
            this.buildingInfoClose.addEventListener('click', () => {
                // 调用MapModule的hideBuildingInfo方法
                if (typeof MapModule !== 'undefined' && MapModule.hideBuildingInfo) {
                    MapModule.hideBuildingInfo();
                }
            });
        }

        // 点击面板外部关闭（可选）
        document.addEventListener('click', (e) => {
            if (this.buildingInfoPanel &&
                this.buildingInfoPanel.classList.contains('show') &&
                !this.buildingInfoPanel.contains(e.target) &&
                !this.searchInput.contains(e.target) &&
                !this.searchResults.contains(e.target)) {
                // 延迟关闭，避免与搜索结果点击冲突
                setTimeout(() => {
                    // 调用MapModule的hideBuildingInfo方法
                    if (typeof MapModule !== 'undefined' && MapModule.hideBuildingInfo) {
                        MapModule.hideBuildingInfo();
                    }
                }, 100);
            }
        });

        console.log('建筑物信息面板事件绑定完成');

        // 🔧 备用关闭按钮事件绑定（确保关闭功能正常工作）
        setTimeout(() => {
            const closeBtn = document.getElementById('building-info-close');
            if (closeBtn && !closeBtn.hasAttribute('data-event-bound')) {
                console.log('🔧 添加备用关闭按钮事件');
                closeBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🔧 备用关闭按钮被点击');
                    // 调用MapModule的hideBuildingInfo方法
                    if (typeof MapModule !== 'undefined' && MapModule.hideBuildingInfo) {
                        MapModule.hideBuildingInfo();
                    }
                });
                closeBtn.setAttribute('data-event-bound', 'true');
            }
        }, 100);
    },

    // 绑定建筑物点击事件
    bindBuildingClickEvents() {
        console.log('绑定建筑物点击事件监听器');

        // 绑定事件处理器

        // 监听建筑物点击事件
        document.addEventListener('buildingClick', (event) => {
            const buildingInfo = event.detail.building;
            console.log('🎯 收到建筑物点击事件:', buildingInfo);
            console.log('🔍 原始建筑物数据字段检查:', {
                id: buildingInfo.id,
                name: buildingInfo.name,
                type: buildingInfo.type,
                description: buildingInfo.description,
                area: buildingInfo.area,
                floor_count: buildingInfo.floor_count,
                build_year: buildingInfo.build_year,
                status: buildingInfo.status
            });

            // 转换为SearchModule期望的格式，保留所有数据库字段
            const building = {
                // 🔧 保留数据库的完整信息
                id: buildingInfo.id,
                name: buildingInfo.name,
                type: buildingInfo.type,
                description: buildingInfo.description,
                area: buildingInfo.area,
                floor_count: buildingInfo.floor_count,
                build_year: buildingInfo.build_year,
                status: buildingInfo.status,
                longitude: buildingInfo.longitude,
                latitude: buildingInfo.latitude,

                // GeoJSON相关信息
                coordinate: buildingInfo.coordinate,
                geometry: buildingInfo.geometry || null,

                // 合并属性信息
                properties: {
                    ...buildingInfo.properties,
                    // 确保数据库字段优先
                    name: buildingInfo.name,
                    type: buildingInfo.type,
                    description: buildingInfo.description,
                    floor_count: buildingInfo.floor_count,
                    area: buildingInfo.area,
                    build_year: buildingInfo.build_year,
                    status: buildingInfo.status,
                    longitude: buildingInfo.longitude,
                    latitude: buildingInfo.latitude
                }
            };

            // 先高亮建筑物（带动画效果）
            if (building.geometry) {
                SearchModule.highlightBuildingWithAnimation(building);
            }

            // 使用和搜索一样的显示方式和位置 - 通过MapModule调用
            if (typeof MapModule !== 'undefined' && MapModule.showBuildingInfo) {
                MapModule.showBuildingInfo(building);
            } else {
                console.error('❌ MapModule.showBuildingInfo 不可用，尝试备用方案');
                // 备用方案：直接调用SearchModule的显示方法
                if (typeof SearchModule !== 'undefined' && SearchModule.showBuildingInfo) {
                    SearchModule.showBuildingInfo(building);
                } else {
                    console.error('❌ SearchModule.showBuildingInfo 也不可用');
                }
            }

            // 定位到建筑物（使用搜索模块的定位方法）
            SearchModule.zoomToBuilding(building);
        });

        console.log('建筑物点击事件绑定完成');
    },

    // 处理搜索输入
    handleSearchInput(query) {
        // 清除之前的定时器
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // 防抖处理
        this.searchTimeout = setTimeout(() => {
            this.performSearch(query);
        }, CONFIG.ui.searchDelay || 300);
    },

    // 执行搜索
    performSearch(query) {
        console.log(`执行搜索: "${query}"`);

        // 📊 记录搜索行为
        if (typeof UserTrackingModule !== 'undefined') {
            UserTrackingModule.trackSearch(query, '建筑物', null);
        }

        // 清空当前结果
        this.currentResults = [];
        this.selectedIndex = -1;

        // 检查查询长度
        if (!query || query.trim().length < CONFIG.search.minQueryLength) {
            this.hideSearchResults();
            this.clearHighlight();
            return;
        }

        // 过滤建筑物
        const filteredBuildings = this.filterBuildings(query.trim());

        // 限制结果数量
        this.currentResults = filteredBuildings.slice(0, CONFIG.search.maxResults);

        // 显示搜索结果
        this.displaySearchResults();
    },

    // 过滤建筑物
    filterBuildings(query) {
        if (!this.buildingsData || this.buildingsData.length === 0) {
            console.warn('建筑物数据未加载');
            return [];
        }

        const queryLower = query.toLowerCase();

        return this.buildingsData.filter(building => {
            const name = building.name || '';
            const nameLower = name.toLowerCase();

            // 支持中文和英文搜索
            return nameLower.includes(queryLower) ||
                   this.matchPinyin(name, query) ||
                   this.matchPartial(name, query);
        });
    },

    // 拼音匹配（简单实现）
    matchPinyin() {
        // 这里可以集成更完善的拼音匹配库
        // 暂时使用简单的首字母匹配
        return false; // 暂不实现复杂拼音匹配
    },

    // 部分匹配
    matchPartial(text, query) {
        if (!text || !query) return false;

        // 移除空格和特殊字符进行匹配
        const cleanText = text.replace(/\s+/g, '').toLowerCase();
        const cleanQuery = query.replace(/\s+/g, '').toLowerCase();

        return cleanText.includes(cleanQuery);
    },

    // 显示搜索结果
    displaySearchResults() {
        if (!this.searchResults) return;

        console.log(`显示 ${this.currentResults.length} 个搜索结果`);

        // 清空现有结果
        this.searchResults.innerHTML = '';

        if (this.currentResults.length === 0) {
            // 显示无结果提示
            const noResultsDiv = document.createElement('div');
            noResultsDiv.className = 'search-no-results';
            noResultsDiv.textContent = CONFIG.search.noResultsText;
            this.searchResults.appendChild(noResultsDiv);
        } else {
            // 显示搜索结果
            this.currentResults.forEach((building, index) => {
                const resultItem = this.createResultItem(building, index);
                this.searchResults.appendChild(resultItem);
            });
        }

        this.showSearchResults();
    },

    // 创建结果项
    createResultItem(building, index) {
        const item = document.createElement('div');
        item.className = 'search-result-item';
        item.dataset.index = index;

        // 建筑物名称
        const nameDiv = document.createElement('div');
        nameDiv.className = 'result-name';
        nameDiv.textContent = building.name || '未命名建筑';

        // 建筑物信息
        const infoDiv = document.createElement('div');
        infoDiv.className = 'result-info';

        const infoText = [];
        if (building.type) infoText.push(`类型: ${building.type}`);
        if (building.area) infoText.push(`面积: ${building.area}m²`);

        infoDiv.textContent = infoText.join(' | ') || '暂无详细信息';

        // 创建按钮容器
        const buttonsDiv = document.createElement('div');
        buttonsDiv.className = 'result-buttons';

        // 查看详情按钮
        const viewBtn = document.createElement('button');
        viewBtn.className = 'result-btn result-btn-view';
        viewBtn.innerHTML = '<span class="btn-icon">👁️</span><span>查看详情</span>';
        viewBtn.addEventListener('click', (e) => {
            e.stopPropagation(); // 阻止事件冒泡
            this.selectBuilding(index);
        });

        // 导航至此按钮
        const navBtn = document.createElement('button');
        navBtn.className = 'result-btn result-btn-nav';
        navBtn.innerHTML = '<span class="btn-icon">🧭</span><span>导航至此</span>';
        navBtn.addEventListener('click', (e) => {
            e.stopPropagation(); // 阻止事件冒泡
            this.navigateToBuilding(building);
        });

        buttonsDiv.appendChild(viewBtn);
        buttonsDiv.appendChild(navBtn);

        item.appendChild(nameDiv);
        item.appendChild(infoDiv);
        item.appendChild(buttonsDiv);

        // 绑定鼠标悬停事件
        item.addEventListener('mouseenter', () => {
            this.highlightResultItem(index);
        });

        return item;
    },

    // 显示搜索结果容器
    showSearchResults() {
        if (this.searchResults) {
            this.searchResults.style.display = 'block';
        }
    },

    // 隐藏搜索结果容器
    hideSearchResults() {
        if (this.searchResults) {
            this.searchResults.style.display = 'none';
        }
    },

    // 处理键盘导航
    handleKeyboardNavigation(event) {
        if (!this.currentResults.length) return;

        switch (event.key) {
            case 'ArrowDown':
                event.preventDefault();
                this.selectedIndex = Math.min(this.selectedIndex + 1, this.currentResults.length - 1);
                this.highlightResultItem(this.selectedIndex);
                break;

            case 'ArrowUp':
                event.preventDefault();
                this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
                this.highlightResultItem(this.selectedIndex);
                break;

            case 'Enter':
                event.preventDefault();
                if (this.selectedIndex >= 0) {
                    this.selectBuilding(this.selectedIndex);
                }
                break;

            case 'Escape':
                event.preventDefault();
                this.hideSearchResults();
                this.clearHighlight();
                this.searchInput.blur();
                break;
        }
    },

    // 高亮结果项
    highlightResultItem(index) {
        // 移除所有高亮
        const items = this.searchResults.querySelectorAll('.search-result-item');
        items.forEach(item => item.classList.remove('highlighted'));

        // 高亮指定项
        if (index >= 0 && index < items.length) {
            items[index].classList.add('highlighted');
            this.selectedIndex = index;

            // 滚动到可见区域
            items[index].scrollIntoView({
                block: 'nearest',
                behavior: 'smooth'
            });
        }
    },

    // 选择建筑物
    selectBuilding(index) {
        if (index < 0 || index >= this.currentResults.length) return;

        const building = this.currentResults[index];
        console.log('选择建筑物:', building.name);

        // 更新搜索框文本
        this.searchInput.value = building.name;

        // 隐藏搜索结果
        this.hideSearchResults();

        // 高亮建筑物（带动画）
        this.highlightBuildingWithAnimation(building);

        // 定位到建筑物
        this.zoomToBuilding(building);

        // 显示建筑物信息（带动画）- 通过MapModule调用
        if (typeof MapModule !== 'undefined' && MapModule.showBuildingInfo) {
            MapModule.showBuildingInfo(building);
        } else {
            console.error('❌ MapModule.showBuildingInfo 不可用');
            // 备用方案：触发建筑物点击事件
            if (typeof MapModule !== 'undefined' && MapModule.triggerBuildingClickEvent) {
                MapModule.triggerBuildingClickEvent(building);
            } else {
                // 最后的备用方案：直接触发DOM事件
                const event = new CustomEvent('buildingClick', {
                    detail: { building: building }
                });
                document.dispatchEvent(event);
                console.log('🎯 已触发建筑物点击事件（备用方案）:', building.name);
            }
        }

        // 触发建筑物选择事件
        this.triggerBuildingSelectEvent(building);
    },

    // 🔧 选择最佳匹配候选（解决"西操场"匹配问题）
    selectBestCandidate(candidates, targetBuilding) {
        if (candidates.length === 1) {
            return candidates[0];
        }

        console.log(`🎯 从 ${candidates.length} 个候选中选择最佳匹配:`,
                   candidates.map(c => c.get('name')));

        const targetName = targetBuilding.name.toLowerCase().trim();

        // 🎯 特殊处理：西操场强制精确匹配
        if (targetName === '西操场') {
            console.log('🏟️ 检测到西操场搜索，强制精确名称匹配');

            // 🚨 强制只选择名称完全匹配"西操场"的候选
            const exactMatch = candidates.find(candidate => {
                const candidateName = (candidate.get('name') || '').toLowerCase().trim();
                const isExact = candidateName === '西操场';
                console.log(`🔍 检查候选: "${candidate.get('name')}" -> 精确匹配: ${isExact}`);
                return isExact;
            });

            if (exactMatch) {
                console.log(`✅ 西操场精确匹配成功: ${exactMatch.get('name')}`);
                return exactMatch;
            } else {
                console.log('❌ 未找到名称为"西操场"的精确匹配候选');
                // 如果没有精确匹配，返回第一个候选（但这不应该发生）
                return candidates[0];
            }
        }

        // 通用匹配逻辑（其他建筑物）
        let filteredCandidates = candidates.filter(candidate => {
            const candidateName = (candidate.get('name') || '').toLowerCase().trim();

            // 如果目标是操场，排除包含"看台"、"附属"等关键词的建筑
            if (targetName.includes('操场')) {
                if (candidateName.includes('看台') ||
                    candidateName.includes('附属') ||
                    candidateName.includes('配套') ||
                    candidateName.includes('体育中心')) {
                    console.log(`🚫 排除干扰项: ${candidateName}`);
                    return false;
                }
            }

            return true;
        });

        // 如果过滤后没有候选，使用原始候选
        if (filteredCandidates.length === 0) {
            console.log('⚠️ 过滤后无候选，使用原始候选');
            filteredCandidates = candidates;
        }

        // 按优先级排序
        filteredCandidates.sort((a, b) => {
            const nameA = (a.get('name') || '').toLowerCase().trim();
            const nameB = (b.get('name') || '').toLowerCase().trim();

            // 1. 精确匹配优先
            if (nameA === targetName && nameB !== targetName) return -1;
            if (nameB === targetName && nameA !== targetName) return 1;

            // 2. 面积大的优先（操场通常面积较大）
            const areaA = parseFloat(a.get('Shape_Area') || 0);
            const areaB = parseFloat(b.get('Shape_Area') || 0);

            if (targetName.includes('操场')) {
                return areaB - areaA; // 面积大的优先
            }

            // 3. 名称长度短的优先（通常更准确）
            return nameA.length - nameB.length;
        });

        const selected = filteredCandidates[0];
        console.log(`✅ 选择最佳候选: ${selected.get('name')} (面积: ${selected.get('Shape_Area')})`);

        return selected;
    },

    // 高亮建筑物（带动画效果）
    highlightBuildingWithAnimation(building) {
        if (!this.highlightLayer) {
            console.warn('⚠️ 高亮图层未初始化');
            return;
        }

        console.log('✨ 高亮建筑物（带动画）:', building.name);
        console.log('📍 建筑物数据:', building);

        // 先清除之前的高亮（带渐隐动画）
        this.clearHighlightWithAnimation(() => {
            // 渐隐完成后，添加新的高亮
            this.addHighlightFeature(building);
        });
    },

    // 添加高亮要素
    addHighlightFeature(building) {
        try {
            let feature = null;

            console.log('🎯 开始高亮建筑物:', building.name);
            console.log('📊 建筑物数据:', building);

            // 方法1: 尝试从GeoJSON图层中找到对应的建筑物要素
            if (LayerModule && LayerModule.buildingLayer) {
                const buildingSource = LayerModule.buildingLayer.getSource();
                const features = buildingSource.getFeatures();

                // 通过名称匹配找到对应的建筑物要素
                // 智能匹配算法：优先精确匹配，避免"西操场"匹配到"西操场看台"的问题
                let matchedFeature = null;

                console.log('🔍 开始匹配建筑物要素，目标名称:', building.name);

                // 🔧 改进的匹配算法：解决数据库和GeoJSON名称不匹配问题
                console.log('🎯 目标建筑名称:', building.name);

                // 🎯 特殊匹配规则：数据库名称 -> GeoJSON名称
                const nameMapping = {
                    "体育中心、操场看台": "体育中心、操场看台",
                    "西操场": "西操场",
                    "东操场": "东操场",
                    "图书馆": "图书馆"
                };

                // 获取目标GeoJSON名称
                const targetGeoName = nameMapping[building.name] || building.name;
                console.log('🎯 目标GeoJSON名称:', targetGeoName);

                // 第一轮：使用映射规则进行精确匹配
                matchedFeature = features.find(f => {
                    const featureName = f.get('name') || f.get('NAME') || f.get('建筑物名称') || '';
                    const isExactMatch = featureName === targetGeoName;
                    if (isExactMatch) {
                        console.log('✅ 映射规则匹配成功:', `"${building.name}" -> "${featureName}"`);
                    }
                    return isExactMatch;
                });

                // 备用：原始精确匹配
                if (!matchedFeature) {
                    matchedFeature = features.find(f => {
                        const featureName = f.get('name') || f.get('NAME') || f.get('建筑物名称') || '';
                        const isExactMatch = featureName === building.name;
                        if (isExactMatch) {
                            console.log('✅ 原始精确匹配成功:', featureName);
                        }
                        return isExactMatch;
                    });
                }

                // 🚨 特殊处理：西操场搜索跳过部分匹配，避免匹配到"体育中心、西操场看台"
                if (!matchedFeature && building.name !== '西操场') {
                    console.log('🔄 精确匹配失败，尝试智能部分匹配');

                    let partialMatches = features.filter(f => {
                        const featureName = f.get('name') || f.get('NAME') || f.get('建筑物名称') || '';

                        // 分割名称，处理复合名称（如"体育中心、西操场看台"）
                        const nameParts = featureName.split(/[、，,]/).map(part => part.trim());

                        // 检查是否有完全匹配的部分
                        const hasExactPart = nameParts.some(part => part === building.name);
                        if (hasExactPart) {
                            return true;
                        }

                        // 检查前缀或后缀匹配（避免子串匹配）
                        const isPrefixOrSuffix = featureName.startsWith(building.name) || featureName.endsWith(building.name);
                        if (isPrefixOrSuffix && featureName.includes(building.name)) {
                            return true;
                        }

                        return false;
                    });

                    if (partialMatches.length > 0) {
                        console.log(`🔍 找到 ${partialMatches.length} 个部分匹配候选`);
                        matchedFeature = this.selectBestCandidate(partialMatches, building);
                    }
                } else if (!matchedFeature && building.name === '西操场') {
                    console.log('🚨 西操场搜索：跳过部分匹配，避免错误匹配到看台');
                }

                // 第三轮：宽松匹配（仅当前两轮都失败时）
                if (!matchedFeature) {
                    console.log('🔄 智能匹配失败，尝试宽松匹配');
                    matchedFeature = features.find(f => {
                        const featureName = f.get('name') || f.get('NAME') || f.get('建筑物名称') || '';
                        const isLooseMatch = building.name.includes(featureName) && featureName.length > 2;
                        if (isLooseMatch) {
                            console.log('⚠️ 宽松匹配:', featureName);
                        }
                        return isLooseMatch;
                    });
                }

                if (matchedFeature) {
                    console.log('✅ 从建筑物图层找到匹配要素:', building.name);
                    feature = matchedFeature.clone(); // 克隆要素用于高亮
                } else {
                    console.log('⚠️ 在建筑物图层中未找到匹配要素:', building.name);
                }
            }

            // 方法2: 如果没有找到，尝试使用building.geometry
            if (!feature && building.geometry) {
                console.log('🔄 尝试使用building.geometry创建要素');
                try {
                    const format = new ol.format.GeoJSON();
                    feature = format.readFeature(building.geometry, {
                        dataProjection: 'EPSG:4326',
                        featureProjection: 'EPSG:3857'
                    });
                    console.log('✅ 从geometry创建要素成功');
                } catch (geoError) {
                    console.warn('⚠️ 从geometry创建要素失败:', geoError);
                }
            }

            // 方法3: 最后的备选方案，创建点几何
            if (!feature) {
                console.log('🔄 使用坐标创建点要素作为备选方案');
                let coordinates = null;

                // 尝试从多个来源获取坐标
                if (building.geometry && building.geometry.coordinates) {
                    coordinates = building.geometry.coordinates;
                } else if (building.coordinate && building.coordinate.length >= 2) {
                    coordinates = [parseFloat(building.coordinate[0]), parseFloat(building.coordinate[1])];
                } else if (building.properties?.longitude && building.properties?.latitude) {
                    coordinates = [parseFloat(building.properties.longitude), parseFloat(building.properties.latitude)];
                }

                if (coordinates) {
                    const transformedCoord = ol.proj.fromLonLat(coordinates);
                    const geometry = new ol.geom.Point(transformedCoord);
                    feature = new ol.Feature({
                        geometry: geometry,
                        name: building.name,
                        buildingData: building
                    });
                    console.log('✅ 创建点要素成功');
                }
            }

            if (!feature) {
                console.warn('❌ 无法创建高亮要素:', building.name);
                return;
            }

            // 设置要素属性
            feature.set('name', building.name);
            feature.set('buildingData', building);

            console.log('✨ 高亮要素创建成功:', building.name);

            // 保存当前高亮要素引用
            this.currentHighlightFeature = feature;

            // 添加到高亮图层（先设置透明度为0）
            this.highlightLayer.getSource().addFeature(feature);

            // 渐显动画
            this.animateHighlightFadeIn();

            console.log('🎉 建筑物高亮成功（带动画）:', building.name);

        } catch (error) {
            console.error('❌ 高亮建筑物失败:', error);
        }
    },

    // 高亮渐显动画
    animateHighlightFadeIn() {
        let opacity = 0;
        const targetOpacity = 0.8;
        const duration = 1000; // 1秒
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeProgress = 1 - Math.pow(1 - progress, 3);
            opacity = easeProgress * targetOpacity;

            // 更新高亮图层样式
            if (this.highlightLayer) {
                this.highlightLayer.setOpacity(opacity);
            }

            if (progress < 1) {
                this.highlightAnimationId = requestAnimationFrame(animate);
            } else {
                // 动画完成，开始脉冲效果
                this.startHighlightPulse();
            }
        };

        animate();
    },

    // 开始高亮脉冲效果
    startHighlightPulse() {
        let pulseDirection = 1; // 1为增加，-1为减少
        let currentOpacity = 0.8;
        const minOpacity = 0.6;
        const maxOpacity = 1.0;
        const pulseSpeed = 0.02;

        const pulse = () => {
            currentOpacity += pulseDirection * pulseSpeed;

            if (currentOpacity >= maxOpacity) {
                currentOpacity = maxOpacity;
                pulseDirection = -1;
            } else if (currentOpacity <= minOpacity) {
                currentOpacity = minOpacity;
                pulseDirection = 1;
            }

            if (this.highlightLayer && this.currentHighlightFeature) {
                this.highlightLayer.setOpacity(currentOpacity);
                this.highlightAnimationId = requestAnimationFrame(pulse);
            }
        };

        pulse();
    },

    // 清除高亮（带动画效果）
    clearHighlightWithAnimation(callback) {
        if (!this.highlightLayer || !this.currentHighlightFeature) {
            if (callback) callback();
            return;
        }

        // 停止当前动画
        if (this.highlightAnimationId) {
            cancelAnimationFrame(this.highlightAnimationId);
            this.highlightAnimationId = null;
        }

        // 渐隐动画
        let opacity = this.highlightLayer.getOpacity();
        const duration = 1000; // 1秒
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeProgress = 1 - Math.pow(1 - progress, 3);
            const currentOpacity = opacity * (1 - easeProgress);

            if (this.highlightLayer) {
                this.highlightLayer.setOpacity(currentOpacity);
            }

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // 动画完成，清除要素
                this.highlightLayer.getSource().clear();
                this.highlightLayer.setOpacity(0.8); // 重置透明度
                this.currentHighlightFeature = null;
                console.log('清除建筑物高亮（带动画）');

                if (callback) callback();
            }
        };

        animate();
    },

    // 清除高亮（无动画，用于模块销毁等场景）
    clearHighlight() {
        if (this.highlightLayer) {
            this.highlightLayer.getSource().clear();
            this.highlightLayer.setOpacity(0.8);
            this.currentHighlightFeature = null;

            if (this.highlightAnimationId) {
                cancelAnimationFrame(this.highlightAnimationId);
                this.highlightAnimationId = null;
            }

            console.log('清除建筑物高亮');
        }
    },

    // 缩放到建筑物
    zoomToBuilding(building) {
        if (!MapModule.isMapInitialized()) {
            console.warn('地图未初始化，无法缩放到建筑物');
            return;
        }

        console.log('🎯 缩放到建筑物:', building.name);
        console.log('📍 建筑物数据:', building);

        try {
            let coordinates = null;

            // 尝试从多个来源获取坐标
            if (building.geometry && building.geometry.coordinates) {
                if (building.geometry.type === 'Point') {
                    coordinates = building.geometry.coordinates;
                } else if (building.geometry.type === 'Polygon') {
                    // 计算多边形中心点
                    const coords = building.geometry.coordinates[0];
                    let sumLon = 0, sumLat = 0;
                    coords.forEach(coord => {
                        sumLon += parseFloat(coord[0]);
                        sumLat += parseFloat(coord[1]);
                    });
                    coordinates = [sumLon / coords.length, sumLat / coords.length];
                }
            } else if (building.coordinate && building.coordinate.length >= 2) {
                coordinates = [parseFloat(building.coordinate[0]), parseFloat(building.coordinate[1])];
            } else if (building.properties?.longitude && building.properties?.latitude) {
                coordinates = [parseFloat(building.properties.longitude), parseFloat(building.properties.latitude)];
            }

            if (!coordinates || coordinates.length < 2) {
                console.error('❌ 无法获取建筑物坐标:', building);
                return;
            }

            console.log('📍 使用坐标:', coordinates);

            // 转换坐标到地图投影
            const transformedCoord = ol.proj.fromLonLat(coordinates);

            // 获取地图视图
            const map = MapModule.getMap();
            if (!map) {
                console.error('❌ 无法获取地图实例');
                return;
            }

            const view = map.getView();
            if (!view) {
                console.error('❌ 无法获取地图视图');
                return;
            }

            // 计算偏移，避免被右侧信息面板遮挡
            const panelWidth = 420; // 信息面板宽度 + 边距
            const offsetX = -panelWidth / 2; // 向左偏移一半面板宽度

            // 计算偏移后的中心点
            const offsetCenter = [
                transformedCoord[0] + offsetX,
                transformedCoord[1]
            ];

            // 设置地图中心和缩放级别，考虑信息面板位置
            view.animate({
                center: offsetCenter,
                zoom: 17, // 适合查看建筑物的缩放级别
                duration: 1000 // 1秒动画
            });

            console.log('✅ 成功缩放到建筑物:', building.name);

        } catch (error) {
            console.error('❌ 缩放到建筑物失败:', error);
        }
    },

    // 根据坐标定位到建筑物
    zoomToBuildingByCoordinate(coordinate) {
        if (!MapModule.isMapInitialized() || !coordinate) {
            console.warn('无法定位到建筑物：地图未初始化或坐标无效');
            return;
        }

        try {
            // 以坐标为中心创建小范围
            const buffer = 100; // 100米缓冲区
            const extent = [
                coordinate[0] - buffer,
                coordinate[1] - buffer,
                coordinate[0] + buffer,
                coordinate[1] + buffer
            ];

            // 获取地图视图
            const map = MapModule.getMap();
            if (!map) {
                console.error('无法获取地图实例');
                return;
            }

            const view = map.getView();
            if (!view) {
                console.error('无法获取地图视图');
                return;
            }

            // 缩放到建筑物
            view.fit(extent, {
                duration: 1000,
                padding: [50, 50, 50, 50], // 添加边距
                maxZoom: 18, // 限制最大缩放级别
                size: map.getSize()
            });

            console.log('根据坐标定位到建筑物');

        } catch (error) {
            console.error('根据坐标定位建筑物失败:', error);
        }
    },

    // 导航至建筑物
    navigateToBuilding(building) {
        console.log('导航至建筑物:', building.name);

        try {
            // 隐藏搜索结果
            this.hideSearchResults();

            // 检查RouteModule是否可用
            if (typeof RouteModule === 'undefined' || !RouteModule.isInitialized) {
                console.error('路径规划模块未初始化');
                this.showMessage('路径规划功能暂不可用', 'error');
                return;
            }

            // 获取建筑物坐标
            let targetCoordinate = null;
            if (building.coordinate) {
                targetCoordinate = building.coordinate;
            } else if (building.geometry) {
                // 从几何数据中提取坐标
                if (building.geometry.type === 'Point') {
                    targetCoordinate = ol.proj.fromLonLat(building.geometry.coordinates);
                } else if (building.geometry.type === 'Polygon') {
                    // 计算多边形中心点
                    const coordinates = building.geometry.coordinates[0];
                    const lonLatCoords = coordinates.map(coord => ol.proj.fromLonLat(coord));
                    const extent = ol.extent.boundingExtent(lonLatCoords);
                    targetCoordinate = ol.extent.getCenter(extent);
                }
            }

            if (!targetCoordinate) {
                console.error('无法获取建筑物坐标');
                this.showMessage('无法获取建筑物位置信息', 'error');
                return;
            }

            // 设置为路径规划的终点
            RouteModule.setEndPoint(targetCoordinate, building.name);

            // 显示路径规划提醒
            this.showRoutePlanningReminder(building);

            console.log('已设置导航终点:', building.name);

        } catch (error) {
            console.error('导航至建筑物失败:', error);
            this.showMessage('导航功能出现错误', 'error');
        }
    },

    // 显示路径规划提醒
    showRoutePlanningReminder(building) {
        // 创建屏幕中间的提醒模态框
        const reminderModal = document.createElement('div');
        reminderModal.className = 'route-planning-reminder-modal';
        reminderModal.innerHTML = `
            <div class="reminder-modal-overlay"></div>
            <div class="reminder-modal-content">
                <div class="reminder-header">
                    <div class="reminder-icon">🎯</div>
                    <h3 class="reminder-title" data-i18n="route.endPointSet">${t('route.endPointSet')}</h3>
                </div>
                <div class="reminder-body">
                    <p class="reminder-message">
                        <strong>${building.name}</strong> ${t('route.hasBeenSetAsEndPoint')}
                    </p>
                    <div class="reminder-instruction">
                        ${t('messages.routePlanningReminder')}
                    </div>
                    <div class="reminder-steps">
                        <div class="step-item">
                            <span class="step-number">1</span>
                            <span class="step-text" data-i18n="route.openRoutePlanning">${t('route.openRoutePlanning')}</span>
                        </div>
                        <div class="step-item">
                            <span class="step-number">2</span>
                            <span class="step-text" data-i18n="route.setStartPoint">${t('route.setStartPoint')}</span>
                        </div>
                        <div class="step-item">
                            <span class="step-number">3</span>
                            <span class="step-text" data-i18n="route.clickCalculate">${t('route.clickCalculate')}</span>
                        </div>
                    </div>
                </div>
                <div class="reminder-actions">
                    <button class="reminder-btn reminder-btn-secondary" onclick="this.closest('.route-planning-reminder-modal').remove();">
                        <span data-i18n="common.close">${t('common.close')}</span>
                    </button>
                    <button class="reminder-btn reminder-btn-primary" onclick="this.closest('.route-planning-reminder-modal').remove(); PanelManager.showPanel(PanelManager.panelTypes.ROUTE_PLANNING, () => RouteModule.showPanel());">
                        <span data-i18n="route.openRoutePlanning">${t('route.openRoutePlanning')}</span>
                    </button>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(reminderModal);

        // 8秒后自动关闭提示（如果用户没有手动关闭）
        setTimeout(() => {
            if (reminderModal.parentNode) {
                reminderModal.remove();
            }
        }, 8000);

        console.log('已显示路径规划提醒模态框');
    },

    // 显示消息提示
    showMessage(message, type = 'info') {
        // 创建消息提示元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `search-message search-message-${type}`;
        messageDiv.textContent = message;

        // 添加到搜索容器
        const searchContainer = document.querySelector('.search-container');
        if (searchContainer) {
            searchContainer.appendChild(messageDiv);

            // 3秒后自动移除
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 3000);
        }
    },

    // 触发建筑物选择事件
    triggerBuildingSelectEvent(building) {
        const event = new CustomEvent('buildingSelect', {
            detail: {
                building: building,
                timestamp: Date.now()
            }
        });
        document.dispatchEvent(event);
    },

    // 加载建筑物数据
    loadBuildingsData() {
        console.log('加载建筑物数据...');

        // 监听图层加载完成事件
        document.addEventListener('layersLoadComplete', () => {
            this.extractBuildingsFromLayer();
        });

        // 如果图层已经加载完成，直接提取数据
        if (LayerModule && LayerModule.getLoadedLayer('buildings')) {
            this.extractBuildingsFromLayer();
        }
    },

    // 从图层提取建筑物数据
    extractBuildingsFromLayer() {
        console.log('从图层提取建筑物数据');

        const buildingsLayer = LayerModule.getLoadedLayer('buildings');
        if (!buildingsLayer || !buildingsLayer.source) {
            console.warn('建筑物图层未找到');
            return;
        }

        const features = buildingsLayer.source.getFeatures();
        this.buildingsData = features.map(feature => {
            const properties = feature.getProperties();
            const geometry = feature.getGeometry();

            // 获取几何类型和坐标，并计算面积
            let geometryData = null;
            let calculatedArea = null;

            if (geometry) {
                const geometryType = geometry.getType();
                let coordinates;

                if (geometryType === 'Polygon') {
                    // 将坐标从EPSG:3857转换回EPSG:4326
                    const clonedGeometry = geometry.clone();
                    clonedGeometry.transform('EPSG:3857', 'EPSG:4326');
                    coordinates = clonedGeometry.getCoordinates();

                    // 使用投影功能计算准确面积（平方米）
                    calculatedArea = this.calculatePolygonArea(coordinates[0]);

                } else if (geometryType === 'Point') {
                    const clonedGeometry = geometry.clone();
                    clonedGeometry.transform('EPSG:3857', 'EPSG:4326');
                    coordinates = clonedGeometry.getCoordinates();
                    // 点要素没有面积
                    calculatedArea = 0;
                } else {
                    console.warn('不支持的几何类型:', geometryType);
                    coordinates = geometry.getCoordinates();
                    calculatedArea = 0;
                }

                geometryData = {
                    type: geometryType,
                    coordinates: coordinates
                };
            }

            // 计算建筑物的中心坐标（用于路径规划）
            let centerCoordinate = null;
            if (geometryData.type === 'Point') {
                // 点几何直接使用坐标
                centerCoordinate = ol.proj.fromLonLat(geometryData.coordinates);
            } else if (geometryData.type === 'Polygon') {
                // 多边形计算中心点
                const coords = geometryData.coordinates[0];
                let sumLon = 0, sumLat = 0;
                coords.forEach(coord => {
                    sumLon += coord[0];
                    sumLat += coord[1];
                });
                const centerLon = sumLon / coords.length;
                const centerLat = sumLat / coords.length;
                centerCoordinate = ol.proj.fromLonLat([centerLon, centerLat]);
            }

            return {
                name: properties.name || properties.NAME || '未命名建筑',
                type: properties.type || properties.TYPE || '建筑物',
                area: calculatedArea !== null ? calculatedArea : (properties.area || properties.AREA || 0),
                id: properties.id || properties.ID || null,
                description: properties.description || properties.DESCRIPTION || '',
                geometry: geometryData,
                coordinate: centerCoordinate, // 添加投影坐标，用于路径规划
                feature: feature
            };
        });

        console.log(`提取到 ${this.buildingsData.length} 个建筑物数据`);
    },

    // 使用投影功能计算多边形面积（平方米）
    calculatePolygonArea(coordinates) {
        if (!coordinates || coordinates.length < 3) {
            return 0;
        }

        try {
            // 使用球面几何计算面积（考虑地球曲率）
            // 将经纬度坐标转换为弧度
            const toRadians = (degrees) => degrees * Math.PI / 180;

            // 地球半径（米）
            const EARTH_RADIUS = 6378137;

            let area = 0;
            const n = coordinates.length - 1; // 排除重复的最后一个点

            for (let i = 0; i < n; i++) {
                const j = (i + 1) % n;

                const lat1 = toRadians(coordinates[i][1]);
                const lon1 = toRadians(coordinates[i][0]);
                const lat2 = toRadians(coordinates[j][1]);
                const lon2 = toRadians(coordinates[j][0]);

                // 使用球面三角形面积公式
                area += (lon2 - lon1) * (2 + Math.sin(lat1) + Math.sin(lat2));
            }

            // 计算最终面积（平方米）
            area = Math.abs(area * EARTH_RADIUS * EARTH_RADIUS / 2);

            console.log(`计算多边形面积: ${Math.round(area)}m²`);
            return Math.round(area);

        } catch (error) {
            console.error('计算多边形面积失败:', error);
            return 0;
        }
    },

    // 建筑物信息显示已统一到MapModule

    // 获取搜索统计信息
    getSearchStats() {
        return {
            totalBuildings: this.buildingsData.length,
            currentResults: this.currentResults.length,
            selectedIndex: this.selectedIndex,
            isInitialized: this.isInitialized
        };
    },

    // 清空搜索
    clearSearch() {
        console.log('清空搜索');

        if (this.searchInput) {
            this.searchInput.value = '';
        }

        this.currentResults = [];
        this.selectedIndex = -1;
        this.hideSearchResults();
        this.clearHighlight();
    },

    // 销毁模块
    destroy() {
        console.log('销毁建筑物搜索模块');

        // 清除定时器
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // 清除动画
        if (this.highlightAnimationId) {
            cancelAnimationFrame(this.highlightAnimationId);
        }

        // 移除高亮图层
        if (this.highlightLayer) {
            MapModule.removeLayer(this.highlightLayer);
        }

        // 隐藏建筑物信息面板
        if (typeof MapModule !== 'undefined' && MapModule.hideBuildingInfo) {
            MapModule.hideBuildingInfo();
        }

        // 清空数据
        this.buildingsData = [];
        this.currentResults = [];
        this.selectedIndex = -1;
        this.isInitialized = false;

        // 清空DOM引用
        this.searchInput = null;
        this.searchResults = null;
        this.highlightLayer = null;
        this.buildingInfoPanel = null;
        this.buildingInfoClose = null;
        this.currentHighlightFeature = null;
        this.highlightAnimationId = null;
    }
};

// 初始化已移至统一管理器

// 监听建筑物选择事件
document.addEventListener('buildingSelect', (event) => {
    const { building } = event.detail;
    console.log('收到建筑物选择事件:', building.name);

    // 这里可以触发其他相关功能，如显示建筑物详情
});

// 导出搜索模块（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports.SearchModule = SearchModule;
}

// 在全局作用域中设置SearchModule
if (typeof window !== 'undefined') {
    window.SearchModule = SearchModule;
}

console.log('建筑物搜索模块已定义');

// ==================== 图层控制模块 ====================

const LayerControlModule = {
    // 模块状态
    panel: null, // 图层控制面板
    closeBtn: null, // 关闭按钮
    layerCheckboxes: {}, // 图层复选框映射
    layerSliders: {}, // 图层透明度滑块映射
    isInitialized: false, // 初始化状态

    // 图层名称映射
    layerNameMap: {
        'buildings': '建筑物',
        'roads': '道路',
        'waters': '水域',
        'rivers': '河流',
        'traffic': '交通设施',
        'boundary': '校园边界'
    },

    // 建筑物分类映射
    buildingCategoryMap: {
        'building-teaching': '教学建筑',
        'building-public': '公共建筑',
        'building-dormitory': '宿舍建筑',
        'building-sports': '体育建筑',
        'building-admin': '行政建筑',
        'building-dining': '食堂',
        'building-service': '服务建筑',
        'building-college': '学院建筑',
        'building-parking': '公共停车场',
        'building-unknown': '未知建筑'
    },

    // 建筑物分类状态
    buildingCategoryStates: {},

    // 初始化模块
    init() {
        console.log('初始化图层控制模块');

        try {
            // 获取DOM元素
            this.panel = document.getElementById('layer-control-panel');
            this.closeBtn = document.getElementById('layer-panel-close');

            if (!this.panel) {
                console.error('图层控制面板元素未找到');
                return false;
            }

            if (!this.closeBtn) {
                console.error('图层控制面板关闭按钮未找到');
                return false;
            }

            // 初始化图层控件映射
            this.initLayerControls();

            // 绑定事件
            this.bindEvents();

            // 同步图层状态
            this.syncLayerStates();

            this.isInitialized = true;
            console.log('图层控制模块初始化成功');
            return true;

        } catch (error) {
            console.error('图层控制模块初始化失败:', error);
            return false;
        }
    },

    // 初始化图层控件映射
    initLayerControls() {
        console.log('初始化图层控件映射');

        // 初始化复选框映射
        Object.keys(this.layerNameMap).forEach(layerName => {
            const checkbox = document.getElementById(`layer-${layerName}`);
            if (checkbox) {
                this.layerCheckboxes[layerName] = checkbox;
                console.log(`找到图层复选框: ${layerName}`);
            } else {
                console.warn(`图层复选框未找到: layer-${layerName}`);
            }
        });

        // 初始化建筑物分类复选框映射
        Object.keys(this.buildingCategoryMap).forEach(categoryName => {
            const checkbox = document.getElementById(`layer-${categoryName}`);
            if (checkbox) {
                this.layerCheckboxes[categoryName] = checkbox;
                this.buildingCategoryStates[categoryName] = true; // 默认显示
                console.log(`找到建筑物分类复选框: ${categoryName}`);
            } else {
                console.warn(`建筑物分类复选框未找到: layer-${categoryName}`);
            }
        });

        // 初始化建筑物总控制切换功能
        this.initBuildingToggle();

        console.log(`图层控件映射完成，共找到 ${Object.keys(this.layerCheckboxes).length} 个复选框`);
    },

    // 初始化建筑物总控制切换功能
    initBuildingToggle() {
        const buildingToggle = document.querySelector('.layer-toggle');
        const buildingSubItems = document.getElementById('building-sub-layers');

        if (buildingToggle && buildingSubItems) {
            buildingToggle.addEventListener('click', (e) => {
                e.stopPropagation(); // 防止触发父元素的点击事件

                const isCollapsed = buildingSubItems.classList.contains('collapsed');

                if (isCollapsed) {
                    // 展开
                    buildingSubItems.classList.remove('collapsed');
                    buildingToggle.classList.remove('collapsed');
                    buildingToggle.textContent = '▼';
                } else {
                    // 收起
                    buildingSubItems.classList.add('collapsed');
                    buildingToggle.classList.add('collapsed');
                    buildingToggle.textContent = '▶';
                }

                console.log(`建筑物分类面板${isCollapsed ? '展开' : '收起'}`);
            });

            console.log('建筑物总控制切换功能已初始化');
        }
    },

    // 绑定事件
    bindEvents() {
        console.log('绑定图层控制面板事件');

        // 绑定关闭按钮事件
        if (this.closeBtn) {
            this.closeBtn.addEventListener('click', () => {
                this.hidePanel();
            });
        }

        // 绑定图层复选框事件
        Object.keys(this.layerCheckboxes).forEach(layerName => {
            const checkbox = this.layerCheckboxes[layerName];
            if (checkbox) {
                checkbox.addEventListener('change', (e) => {
                    this.handleLayerToggle(layerName, e.target.checked);
                });
            }
        });

        // 绑定面板外部点击关闭事件
        document.addEventListener('click', (e) => {
            if (this.panel &&
                this.panel.style.display !== 'none' &&
                !this.panel.contains(e.target) &&
                !e.target.closest('#layer-control-btn')) {
                // 延迟关闭，避免与按钮点击冲突
                setTimeout(() => {
                    this.hidePanel();
                }, 100);
            }
        });

        console.log('图层控制面板事件绑定完成');
    },

    // 处理图层切换
    handleLayerToggle(layerName, isChecked) {
        console.log(`处理图层切换: ${layerName} = ${isChecked}`);

        try {
            // 检查是否是建筑物分类
            if (this.buildingCategoryMap[layerName]) {
                this.handleBuildingCategoryToggle(layerName, isChecked);
                return;
            }

            // 检查是否是建筑物总控制
            if (layerName === 'buildings') {
                this.handleBuildingMainToggle(isChecked);
                return;
            }

            // 调用LayerModule的切换方法
            const result = LayerModule.toggleLayerVisibility(layerName);

            if (result !== isChecked) {
                // 如果实际结果与复选框状态不一致，同步复选框状态
                const checkbox = this.layerCheckboxes[layerName];
                if (checkbox) {
                    checkbox.checked = result;
                }
                console.warn(`图层 ${layerName} 状态同步: 复选框=${isChecked}, 实际=${result}`);
            }

            // 触发图层状态变化事件
            this.triggerLayerControlEvent(layerName, 'visibility', result);

        } catch (error) {
            console.error(`切换图层 ${layerName} 失败:`, error);

            // 恢复复选框状态
            const checkbox = this.layerCheckboxes[layerName];
            if (checkbox) {
                checkbox.checked = !isChecked;
            }
        }
    },

    // 处理建筑物分类切换
    handleBuildingCategoryToggle(categoryName, isChecked) {
        console.log(`处理建筑物分类切换: ${categoryName} = ${isChecked}`);

        // 更新分类状态
        this.buildingCategoryStates[categoryName] = isChecked;

        // 触发建筑物图层重新渲染
        this.refreshBuildingLayer();

        // 触发事件
        this.triggerLayerControlEvent(categoryName, 'visibility', isChecked);
    },

    // 处理建筑物总控制切换
    handleBuildingMainToggle(isChecked) {
        console.log(`处理建筑物总控制切换: ${isChecked}`);

        if (isChecked) {
            // 显示建筑物图层
            LayerModule.toggleLayerVisibility('buildings');
        } else {
            // 隐藏建筑物图层
            LayerModule.toggleLayerVisibility('buildings');
        }

        // 同步所有建筑物分类复选框状态
        Object.keys(this.buildingCategoryMap).forEach(categoryName => {
            const checkbox = this.layerCheckboxes[categoryName];
            if (checkbox) {
                checkbox.checked = isChecked;
                this.buildingCategoryStates[categoryName] = isChecked;
            }
        });

        // 触发建筑物图层重新渲染
        this.refreshBuildingLayer();
    },

    // 刷新建筑物图层
    refreshBuildingLayer() {
        console.log('刷新建筑物图层显示');

        try {
            // 获取建筑物图层
            const buildingLayer = LayerModule.getLayer('buildings');
            if (!buildingLayer) {
                console.warn('建筑物图层未找到');
                return;
            }

            // 触发图层重新渲染
            buildingLayer.changed();

            console.log('建筑物图层已刷新');

        } catch (error) {
            console.error('刷新建筑物图层失败:', error);
        }
    },

    // 显示面板
    showPanel() {
        console.log('显示图层控制面板');

        if (!this.panel) {
            console.error('图层控制面板未初始化');
            return;
        }

        try {
            // 同步最新的图层状态
            this.syncLayerStates();

            // 显示面板
            this.panel.style.display = 'block';

            // 添加显示动画类
            setTimeout(() => {
                this.panel.classList.add('show');
            }, 10);

            console.log('图层控制面板已显示');

        } catch (error) {
            console.error('显示图层控制面板失败:', error);
        }
    },

    // 隐藏面板
    hidePanel() {
        console.log('隐藏图层控制面板');

        if (!this.panel) {
            console.error('图层控制面板未初始化');
            return;
        }

        try {
            // 移除显示动画类
            this.panel.classList.remove('show');

            // 延迟隐藏面板，等待动画完成
            setTimeout(() => {
                this.panel.style.display = 'none';
            }, 300); // 与CSS动画时间匹配

            console.log('图层控制面板已隐藏');

            // 通知面板管理器面板已关闭
            if (typeof PanelManager !== 'undefined') {
                PanelManager.notifyPanelClosed(PanelManager.panelTypes.LAYER_CONTROL);
            }

        } catch (error) {
            console.error('隐藏图层控制面板失败:', error);
        }
    },

    // 同步图层状态
    syncLayerStates() {
        console.log('同步图层状态到控制面板');

        try {
            // 获取所有图层状态
            const layersStatus = LayerModule.getAllLayersStatus();

            // 更新复选框状态
            Object.keys(this.layerCheckboxes).forEach(layerName => {
                const checkbox = this.layerCheckboxes[layerName];
                const layerStatus = layersStatus[layerName];

                if (checkbox && layerStatus) {
                    checkbox.checked = layerStatus.visible;
                    console.log(`同步图层状态: ${layerName} = ${layerStatus.visible}`);
                }
            });

            console.log('图层状态同步完成');

        } catch (error) {
            console.error('同步图层状态失败:', error);
        }
    },

    // 触发图层控制事件
    triggerLayerControlEvent(layerName, action, value) {
        const event = new CustomEvent('layerControlChange', {
            detail: {
                layerName: layerName,
                action: action,
                value: value,
                timestamp: Date.now()
            }
        });
        document.dispatchEvent(event);
        console.log(`图层控制事件已触发: ${layerName}.${action} = ${value}`);
    },

    // 获取面板状态
    getPanelState() {
        return {
            isVisible: this.panel && this.panel.style.display !== 'none',
            isInitialized: this.isInitialized,
            layerStates: this.getLayerControlStates()
        };
    },

    // 获取图层控制状态
    getLayerControlStates() {
        const states = {};
        Object.keys(this.layerCheckboxes).forEach(layerName => {
            const checkbox = this.layerCheckboxes[layerName];
            if (checkbox) {
                states[layerName] = {
                    checked: checkbox.checked,
                    enabled: !checkbox.disabled
                };
            }
        });
        return states;
    },

    // 销毁模块
    destroy() {
        console.log('销毁图层控制模块');

        // 隐藏面板
        this.hidePanel();

        // 清空数据
        this.layerCheckboxes = {};
        this.layerSliders = {};
        this.isInitialized = false;

        // 清空DOM引用
        this.panel = null;
        this.closeBtn = null;

        console.log('图层控制模块已销毁');
    }
};

// 监听图层加载完成事件，初始化图层控制模块
document.addEventListener('layersLoadComplete', () => {
    console.log('收到图层加载完成事件，初始化图层控制模块');

    // 延迟初始化，确保LayerModule完全准备好
    setTimeout(() => {
        if (LayerControlModule.init()) {
            console.log('图层控制模块初始化成功');
        } else {
            console.error('图层控制模块初始化失败');
        }
    }, 200);
});

// 监听图层状态变化事件
document.addEventListener('layerStateChange', (event) => {
    const { layerName, property, value } = event.detail;
    console.log(`收到图层状态变化事件: ${layerName}.${property} = ${value}`);

    // 如果图层控制模块已初始化，同步状态
    if (LayerControlModule.isInitialized) {
        LayerControlModule.syncLayerStates();
    }
});

// 导出图层控制模块（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports.LayerControlModule = LayerControlModule;
}

// 在全局作用域中设置LayerControlModule
if (typeof window !== 'undefined') {
    window.LayerControlModule = LayerControlModule;
}

console.log('图层控制模块已定义');

// ==================== 指北针模块 ====================

const CompassModule = {
    // 模块状态
    compass: null, // 指北针元素
    needle: null, // 指针元素
    isInitialized: false, // 初始化状态
    currentRotation: 0, // 当前旋转角度

    // 初始化模块
    init() {
        console.log('初始化指北针模块');

        try {
            // 获取DOM元素
            this.compass = document.getElementById('compass');
            this.needle = document.querySelector('.compass-needle');

            if (!this.compass || !this.needle) {
                console.error('指北针元素未找到');
                return false;
            }

            // 绑定事件
            this.bindEvents();

            // 初始化指北针方向
            this.updateCompass();

            this.isInitialized = true;
            console.log('指北针模块初始化成功');
            return true;

        } catch (error) {
            console.error('指北针模块初始化失败:', error);
            return false;
        }
    },

    // 绑定事件
    bindEvents() {
        console.log('绑定指北针事件');

        // 绑定点击事件 - 点击指北针回到正北方向
        if (this.compass) {
            this.compass.addEventListener('click', () => {
                this.resetToNorth();
            });
        }

        // 监听地图旋转事件
        if (typeof MapModule !== 'undefined' && MapModule.map) {
            MapModule.map.getView().on('change:rotation', () => {
                this.updateCompass();
            });
        }

        console.log('指北针事件绑定完成');
    },

    // 更新指北针方向
    updateCompass() {
        if (!this.needle || !MapModule.map) return;

        try {
            // 获取地图当前旋转角度（弧度）
            const mapRotation = MapModule.map.getView().getRotation();

            // 转换为度数
            const rotationDegrees = (mapRotation * 180) / Math.PI;

            // 指北针需要反向旋转以保持指向北方
            this.currentRotation = -rotationDegrees;

            // 应用旋转
            this.needle.style.transform = `rotate(${this.currentRotation}deg)`;

            console.log(`指北针更新: 地图旋转 ${rotationDegrees.toFixed(1)}°, 指北针旋转 ${this.currentRotation.toFixed(1)}°`);

        } catch (error) {
            console.error('更新指北针失败:', error);
        }
    },

    // 重置到正北方向
    resetToNorth() {
        console.log('重置地图到正北方向');

        try {
            if (!MapModule.map) {
                console.warn('地图未初始化');
                return;
            }

            // 获取当前视图
            const view = MapModule.map.getView();

            // 平滑旋转到0度（正北）
            view.animate({
                rotation: 0,
                duration: 500,
                easing: ol.easing.easeOut
            });

            console.log('地图已重置到正北方向');

        } catch (error) {
            console.error('重置地图方向失败:', error);
        }
    },

    // 获取当前方向信息
    getCurrentDirection() {
        const directions = ['北', '东北', '东', '东南', '南', '西南', '西', '西北'];
        const angle = ((this.currentRotation % 360) + 360) % 360; // 确保角度为正值
        const directionIndex = Math.round(angle / 45) % 8;

        return {
            angle: angle,
            direction: directions[directionIndex],
            rotation: this.currentRotation
        };
    },

    // 销毁模块
    destroy() {
        console.log('销毁指北针模块');

        // 清空引用
        this.compass = null;
        this.needle = null;
        this.isInitialized = false;
        this.currentRotation = 0;

        console.log('指北针模块已销毁');
    }
};

// 初始化已移至统一管理器

// 导出指北针模块（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports.CompassModule = CompassModule;
}

// 在全局作用域中设置CompassModule
if (typeof window !== 'undefined') {
    window.CompassModule = CompassModule;
}

console.log('指北针模块已定义');

// ==================== 功能按钮事件绑定 ====================

// 功能按钮事件绑定已移至统一管理器

// ==================== 面板管理器 ====================

const PanelManager = {
    // 当前显示的面板
    currentPanel: null,

    // 面板类型映射
    panelTypes: {
        LAYER_CONTROL: 'layer-control',
        ROUTE_PLANNING: 'route-planning',
        SYSTEM_INFO: 'system-info',
        WEATHER: 'weather',
        DISTANCE_MEASURE: 'distance-measure'
    },

    // 显示面板（互斥显示）
    showPanel(panelType, showFunction) {
        console.log(`面板管理器: 请求显示面板 ${panelType}`);

        try {
            // 如果当前已有面板显示，先隐藏
            if (this.currentPanel && this.currentPanel !== panelType) {
                this.hideCurrentPanel();
            }

            // 显示新面板
            if (typeof showFunction === 'function') {
                showFunction();
                this.currentPanel = panelType;
                console.log(`面板管理器: 面板 ${panelType} 已显示`);
            }

        } catch (error) {
            console.error(`面板管理器: 显示面板 ${panelType} 失败:`, error);
        }
    },

    // 隐藏当前面板
    hideCurrentPanel() {
        if (!this.currentPanel) {
            return;
        }

        console.log(`面板管理器: 隐藏当前面板 ${this.currentPanel}`);

        try {
            switch (this.currentPanel) {
                case this.panelTypes.LAYER_CONTROL:
                    if (LayerControlModule && LayerControlModule.isInitialized) {
                        LayerControlModule.hidePanel();
                    }
                    break;
                case this.panelTypes.ROUTE_PLANNING:
                    if (RouteModule && RouteModule.isInitialized) {
                        RouteModule.hidePanel();
                    }
                    break;
                case this.panelTypes.SYSTEM_INFO:
                    if (SystemInfoModule && SystemInfoModule.isInitialized) {
                        SystemInfoModule.hideModal();
                    }
                    break;
                case this.panelTypes.WEATHER:
                    if (WeatherModule && WeatherModule.isInitialized) {
                        WeatherModule.hidePanel();
                    }
                    break;
                case this.panelTypes.DISTANCE_MEASURE:
                    if (DistanceMeasurementModule && DistanceMeasurementModule.isInitialized) {
                        DistanceMeasurementModule.deactivateMeasureMode();
                        // 隐藏测距面板
                        const distancePanel = document.getElementById('distance-measure-panel');
                        if (distancePanel) {
                            distancePanel.style.display = 'none';
                        }
                    }
                    break;
            }

            this.currentPanel = null;

        } catch (error) {
            console.error(`面板管理器: 隐藏面板失败:`, error);
        }
    },

    // 获取当前面板状态
    getCurrentPanel() {
        return this.currentPanel;
    },

    // 通知面板已关闭
    notifyPanelClosed(panelType) {
        if (this.currentPanel === panelType) {
            console.log(`面板管理器: 收到面板关闭通知 ${panelType}`);
            this.currentPanel = null;
        }
    }
};

console.log('面板管理器已定义');

// 绑定功能按钮事件
function bindFunctionButtons() {
    console.log('开始绑定功能按钮事件');

    try {
        // 绑定图层控制按钮事件
        bindLayerControlButton();

        // 绑定系统信息按钮事件
        bindSystemInfoButton();

        // 绑定路径规划按钮事件
        bindRouteplanningButton();

        // 绑定天气查询按钮事件
        bindWeatherButton();

        // 绑定测距功能按钮事件
        bindDistanceMeasureButton();

        // 绑定测距面板事件
        bindDistancePanelEvents();

        console.log('功能按钮事件绑定完成');

    } catch (error) {
        console.error('绑定功能按钮事件失败:', error);
    }
}

// 绑定图层控制按钮事件
function bindLayerControlButton() {
    console.log('绑定图层控制按钮事件');

    const layerControlBtn = document.getElementById('layer-control-btn');
    if (!layerControlBtn) {
        console.error('图层控制按钮元素未找到');
        return;
    }

    layerControlBtn.addEventListener('click', () => {
        console.log('图层控制按钮被点击');

        try {
            // 检查LayerControlModule是否已初始化
            if (!LayerControlModule.isInitialized) {
                console.warn('图层控制模块未初始化，请等待图层加载完成');
                showUserMessage('图层控制功能正在初始化，请稍候...', 'warning');
                return;
            }

            // 使用面板管理器显示图层控制面板
            PanelManager.showPanel(
                PanelManager.panelTypes.LAYER_CONTROL,
                () => LayerControlModule.showPanel()
            );

        } catch (error) {
            console.error('显示图层控制面板失败:', error);
            showUserMessage('图层控制功能暂时不可用', 'error');
        }
    });

    console.log('图层控制按钮事件绑定成功');
}

// 绑定系统信息按钮事件
function bindSystemInfoButton() {
    console.log('绑定系统信息按钮事件');

    const infoBtn = document.getElementById('info-btn');
    if (!infoBtn) {
        console.error('系统信息按钮元素未找到');
        return;
    }

    infoBtn.addEventListener('click', () => {
        console.log('系统信息按钮被点击');

        try {
            // 检查SystemInfoModule是否已初始化
            if (!SystemInfoModule.isInitialized) {
                console.warn('系统信息模块未初始化，请等待系统加载完成');
                showUserMessage('系统信息功能正在初始化，请稍候...', 'warning');
                return;
            }

            // 使用面板管理器显示系统信息模态框
            PanelManager.showPanel(
                PanelManager.panelTypes.SYSTEM_INFO,
                () => SystemInfoModule.showModal()
            );

        } catch (error) {
            console.error('显示系统信息模态框失败:', error);
            showUserMessage('系统信息功能暂时不可用', 'error');
        }
    });

    console.log('系统信息按钮事件绑定成功');
}

// 绑定路径规划按钮事件
function bindRouteplanningButton() {
    console.log('绑定路径规划按钮事件');

    const routeBtn = document.getElementById('route-planning-btn');
    if (!routeBtn) {
        console.error('路径规划按钮元素未找到');
        return;
    }

    routeBtn.addEventListener('click', () => {
        console.log('路径规划按钮被点击');

        try {
            // 检查RouteModule是否已初始化
            if (!RouteModule.isInitialized) {
                console.warn('路径规划模块未初始化，请等待系统加载完成');
                showUserMessage('路径规划功能正在初始化，请稍候...', 'warning');
                return;
            }

            // 使用面板管理器显示路径规划面板
            PanelManager.showPanel(
                PanelManager.panelTypes.ROUTE_PLANNING,
                () => RouteModule.showPanel()
            );

        } catch (error) {
            console.error('显示路径规划面板失败:', error);
            showUserMessage('路径规划功能暂时不可用', 'error');
        }
    });

    console.log('路径规划按钮事件绑定成功');
}

// 绑定天气查询按钮事件
function bindWeatherButton() {
    console.log('绑定天气查询按钮事件');

    const weatherBtn = document.getElementById('weather-btn');
    if (!weatherBtn) {
        console.error('天气查询按钮元素未找到');
        return;
    }

    weatherBtn.addEventListener('click', () => {
        console.log('天气查询按钮被点击');

        try {
            // 检查WeatherModule是否已初始化
            if (!WeatherModule.isInitialized) {
                console.warn('天气模块未初始化，请等待系统加载完成');
                showUserMessage('天气功能正在初始化，请稍候...', 'warning');
                return;
            }

            // 使用面板管理器显示天气面板
            PanelManager.showPanel(
                PanelManager.panelTypes.WEATHER,
                () => WeatherModule.showPanel()
            );

        } catch (error) {
            console.error('显示天气面板失败:', error);
            showUserMessage('天气功能暂时不可用', 'error');
        }
    });

    console.log('天气查询按钮事件绑定成功');
}

// 绑定测距功能按钮事件
function bindDistanceMeasureButton() {
    console.log('绑定测距功能按钮事件');

    const measureBtn = document.getElementById('distance-measure-btn');
    if (!measureBtn) {
        console.error('测距功能按钮元素未找到');
        return;
    }

    measureBtn.addEventListener('click', () => {
        console.log('测距功能按钮被点击');

        try {
            // 检查DistanceMeasurementModule是否存在
            if (typeof DistanceMeasurementModule === 'undefined') {
                console.error('DistanceMeasurementModule 未定义');
                showUserMessage('测距功能模块未加载', 'error');
                return;
            }

            console.log('测距模块状态:', {
                isInitialized: DistanceMeasurementModule.isInitialized,
                isActive: DistanceMeasurementModule.isActive,
                hasMap: !!DistanceMeasurementModule.map,
                hasPanel: !!DistanceMeasurementModule.measurePanel
            });

            // 检查DistanceMeasurementModule是否已初始化
            if (!DistanceMeasurementModule.isInitialized) {
                console.warn('测距模块未初始化，尝试初始化...');

                // 尝试初始化
                const initResult = DistanceMeasurementModule.init();
                if (!initResult) {
                    console.error('测距模块初始化失败');
                    showUserMessage('测距功能初始化失败，请稍后重试', 'error');
                    return;
                }
                console.log('测距模块初始化成功');
            }

            // 使用面板管理器显示测距面板
            PanelManager.showPanel(
                PanelManager.panelTypes.DISTANCE_MEASURE,
                () => DistanceMeasurementModule.toggleMeasureMode()
            );

        } catch (error) {
            console.error('切换测距模式失败:', error);
            showUserMessage('测距功能暂时不可用: ' + error.message, 'error');
        }
    });

    console.log('测距功能按钮事件绑定成功');
}

// 绑定测距面板事件
function bindDistancePanelEvents() {
    console.log('绑定测距面板事件');

    // 开始测距按钮
    const startBtn = document.getElementById('start-distance-measure');
    if (startBtn) {
        startBtn.addEventListener('click', () => {
            console.log('开始测距按钮被点击');
            if (typeof DistanceMeasurementModule !== 'undefined') {
                DistanceMeasurementModule.activateMeasureMode();
            }
        });
    }

    // 清除测距按钮
    const clearBtn = document.getElementById('clear-distance-measure');
    if (clearBtn) {
        clearBtn.addEventListener('click', () => {
            console.log('清除测距按钮被点击');
            if (typeof DistanceMeasurementModule !== 'undefined') {
                DistanceMeasurementModule.clearMeasurement();
                // 隐藏结果显示
                const resultDiv = document.getElementById('distance-result');
                if (resultDiv) {
                    resultDiv.style.display = 'none';
                }
                // 重置距离值显示
                const distanceValue = document.getElementById('distance-value');
                if (distanceValue) {
                    distanceValue.textContent = '--';
                }
            }
        });
    }

    // 测距面板关闭按钮
    const panelCloseBtn = document.getElementById('distance-panel-close');
    if (panelCloseBtn) {
        panelCloseBtn.addEventListener('click', () => {
            console.log('测距面板关闭按钮被点击');
            const panel = document.getElementById('distance-measure-panel');
            if (panel) {
                panel.style.display = 'none';
            }
            // 取消测距模式
            if (typeof DistanceMeasurementModule !== 'undefined') {
                DistanceMeasurementModule.deactivateMeasureMode();
            }
        });
    }

    console.log('测距面板事件绑定成功');
}

// Toast提示系统
const ToastManager = {
    container: null,
    toasts: new Map(),
    maxToasts: 5,

    // 初始化Toast容器
    init() {
        if (this.container) return;

        this.container = document.createElement('div');
        this.container.id = 'toast-container';
        this.container.className = 'toast-container';
        document.body.appendChild(this.container);
    },

    // 显示Toast
    show(message, type = 'info', duration = 4000) {
        this.init();

        const toastId = Date.now() + Math.random();
        const toast = this.createToast(message, type, toastId);

        // 限制Toast数量
        if (this.toasts.size >= this.maxToasts) {
            const oldestToast = this.toasts.keys().next().value;
            this.remove(oldestToast);
        }

        this.toasts.set(toastId, toast);
        this.container.appendChild(toast);

        // 触发显示动画
        setTimeout(() => toast.classList.add('show'), 10);

        // 自动移除
        if (duration > 0) {
            setTimeout(() => this.remove(toastId), duration);
        }

        return toastId;
    },

    // 创建Toast元素
    createToast(message, type, id) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.dataset.toastId = id;

        const iconMap = {
            info: '💡',
            success: '✅',
            warning: '⚠️',
            error: '❌'
        };

        toast.innerHTML = `
            <div class="toast-icon">${iconMap[type] || iconMap.info}</div>
            <div class="toast-content">
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close" onclick="ToastManager.remove(${id})">×</button>
        `;

        return toast;
    },

    // 移除Toast
    remove(toastId) {
        const toast = this.toasts.get(toastId);
        if (!toast) return;

        toast.classList.add('hide');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
            this.toasts.delete(toastId);
        }, 300);
    },

    // 清除所有Toast
    clear() {
        this.toasts.forEach((_, id) => this.remove(id));
    }
};

// 显示用户消息（Toast实现）
function showUserMessage(message, type = 'info') {
    console.log(`用户消息 [${type}]: ${message}`);

    // 显示Toast提示
    ToastManager.show(message, type);

    // 保持原有的console输出
    if (type === 'error') {
        console.error(message);
    } else if (type === 'warning') {
        console.warn(message);
    } else {
        console.info(message);
    }
}

// ==================== 系统信息模块 ====================

const SystemInfoModule = {
    // 模块状态
    modal: null, // 系统信息模态框
    modalOverlay: null, // 模态框遮罩层
    modalClose: null, // 关闭按钮
    isInitialized: false, // 初始化状态

    // 统计数据元素
    buildingsCountElement: null, // 建筑物数量显示元素
    layersCountElement: null, // 图层数量显示元素
    currentZoomElement: null, // 当前缩放级别显示元素
    systemStatusElement: null, // 系统状态显示元素

    // 初始化模块
    init() {
        console.log('初始化系统信息模块');

        try {
            // 获取DOM元素
            this.modal = document.getElementById('info-modal');
            this.modalOverlay = this.modal ? this.modal.querySelector('.modal-overlay') : null;
            this.modalClose = this.modal ? this.modal.querySelector('.modal-close') : null;
            this.buildingsCountElement = document.getElementById('buildings-count');
            this.layersCountElement = document.getElementById('layers-count');
            this.currentZoomElement = document.getElementById('current-zoom');
            this.systemStatusElement = document.getElementById('system-status');

            if (!this.modal) {
                console.error('系统信息模态框元素未找到');
                return false;
            }

            // 绑定事件
            this.bindEvents();

            this.isInitialized = true;
            console.log('系统信息模块初始化成功');
            return true;

        } catch (error) {
            console.error('系统信息模块初始化失败:', error);
            return false;
        }
    },

    // 绑定事件
    bindEvents() {
        console.log('绑定系统信息模态框事件');

        // 绑定关闭按钮事件
        if (this.modalClose) {
            this.modalClose.addEventListener('click', () => {
                this.hideModal();
            });
        }

        // 绑定遮罩层点击关闭事件
        if (this.modalOverlay) {
            this.modalOverlay.addEventListener('click', () => {
                this.hideModal();
            });
        }

        // 绑定ESC键关闭事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isModalVisible()) {
                this.hideModal();
            }
        });

        console.log('系统信息模态框事件绑定完成');
    },

    // 显示模态框
    showModal() {
        console.log('显示系统信息模态框');

        if (!this.modal) {
            console.error('系统信息模态框未初始化');
            return;
        }

        try {
            // 更新统计数据
            this.updateStats();

            // 显示模态框
            this.modal.style.display = 'flex';

            // 添加显示动画类
            setTimeout(() => {
                this.modal.classList.add('show');
            }, 10);

            console.log('系统信息模态框已显示');

        } catch (error) {
            console.error('显示系统信息模态框失败:', error);
        }
    },

    // 隐藏模态框
    hideModal() {
        console.log('隐藏系统信息模态框');

        if (!this.modal) {
            console.error('系统信息模态框未初始化');
            return;
        }

        try {
            // 移除显示动画类
            this.modal.classList.remove('show');

            // 延迟隐藏模态框，等待动画完成
            setTimeout(() => {
                this.modal.style.display = 'none';
            }, 300); // 与CSS动画时间匹配

            console.log('系统信息模态框已隐藏');

            // 通知面板管理器面板已关闭
            if (typeof PanelManager !== 'undefined') {
                PanelManager.notifyPanelClosed(PanelManager.panelTypes.SYSTEM_INFO);
            }

        } catch (error) {
            console.error('隐藏系统信息模态框失败:', error);
        }
    },

    // 检查模态框是否可见
    isModalVisible() {
        return this.modal && this.modal.style.display !== 'none';
    },

    // 收集系统统计数据
    collectSystemStats() {
        console.log('收集系统统计数据');

        const stats = {
            buildingsCount: 0,
            layersCount: 0,
            loadedLayers: [],
            mapCenter: null,
            currentZoom: null,
            systemVersion: '1.0.0',
            timestamp: Date.now()
        };

        try {
            // 获取建筑物数量
            if (SearchModule && SearchModule.buildingsData) {
                stats.buildingsCount = SearchModule.buildingsData.length;
            }

            // 获取图层数量和已加载图层
            if (LayerModule && LayerModule.loadedLayers) {
                stats.layersCount = LayerModule.loadedLayers.size;
                stats.loadedLayers = Array.from(LayerModule.loadedLayers.keys());
            }

            // 获取地图中心和缩放级别
            if (MapModule && MapModule.getMap()) {
                const map = MapModule.getMap();
                const view = map.getView();
                stats.mapCenter = view.getCenter();
                stats.currentZoom = view.getZoom();
            }

            console.log('系统统计数据收集完成:', stats);
            return stats;

        } catch (error) {
            console.error('收集系统统计数据失败:', error);
            return stats;
        }
    },

    // 更新统计数据显示
    updateStats() {
        console.log('更新系统统计数据显示');

        try {
            const stats = this.collectSystemStats();

            // 更新建筑物数量
            if (this.buildingsCountElement) {
                this.buildingsCountElement.textContent = stats.buildingsCount;
            }

            // 更新图层数量
            if (this.layersCountElement) {
                this.layersCountElement.textContent = `${stats.layersCount}/6`;
            }

            // 更新当前缩放级别
            if (this.currentZoomElement) {
                this.currentZoomElement.textContent = stats.currentZoom ?
                    stats.currentZoom.toFixed(1) : '--';
            }

            // 更新系统状态
            if (this.systemStatusElement) {
                const status = this.getSystemStatus(stats);
                this.systemStatusElement.textContent = status.text;
                this.systemStatusElement.style.color = status.color;
            }

            console.log('系统统计数据显示更新完成');

        } catch (error) {
            console.error('更新系统统计数据显示失败:', error);
        }
    },

    // 获取系统状态
    getSystemStatus(stats) {
        if (stats.layersCount === 6 && stats.buildingsCount > 0) {
            return { text: '运行正常', color: '#4caf50' };
        } else if (stats.layersCount > 0) {
            return { text: '部分加载', color: '#ff9800' };
        } else {
            return { text: '加载中...', color: '#f44336' };
        }
    },

    // 获取模块状态
    getModuleState() {
        return {
            isInitialized: this.isInitialized,
            isModalVisible: this.isModalVisible(),
            lastStats: this.collectSystemStats()
        };
    },

    // 销毁模块
    destroy() {
        console.log('销毁系统信息模块');

        // 隐藏模态框
        this.hideModal();

        // 清空DOM引用
        this.modal = null;
        this.modalOverlay = null;
        this.modalClose = null;
        this.buildingsCountElement = null;
        this.layersCountElement = null;
        this.currentZoomElement = null;
        this.systemStatusElement = null;
        this.isInitialized = false;

        console.log('系统信息模块已销毁');
    }
};

// 初始化已移至统一管理器

// 导出系统信息模块（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports.SystemInfoModule = SystemInfoModule;
}

// 在全局作用域中设置SystemInfoModule
if (typeof window !== 'undefined') {
    window.SystemInfoModule = SystemInfoModule;
}

console.log('系统信息模块已定义');

// ==================== 路径规划模块 ====================

const RouteModule = {
    // 模块状态
    panel: null, // 路径规划面板
    panelClose: null, // 关闭按钮
    routeLayer: null, // 路径图层
    startPointLayer: null, // 起点标记图层
    endPointLayer: null, // 终点标记图层
    isInitialized: false, // 初始化状态
    mapClickEnabled: true, // 地图点击开关状态
    mapClickToggle: null, // 地图点击开关元素

    // 路径数据
    startPoint: null, // 起点坐标
    endPoint: null, // 终点坐标
    currentRoute: null, // 当前路径
    startNearestBuilding: null, // 起点最近建筑物
    endNearestBuilding: null, // 终点最近建筑物

    // 道路网络数据
    roadNetwork: null, // 道路网络图
    roadNodes: new Map(), // 道路节点
    roadEdges: [], // 道路边

    // DOM元素
    startInput: null, // 起点输入框
    endInput: null, // 终点输入框
    startClearBtn: null, // 起点清除按钮
    endClearBtn: null, // 终点清除按钮
    calculateBtn: null, // 计算路径按钮
    clearBtn: null, // 清除路径按钮
    resultPanel: null, // 结果面板
    distanceValue: null, // 距离显示
    timeValue: null, // 时间显示
    lastResolvedStartBuilding: null, // 保存解析的起点建筑物名称
    lastResolvedEndBuilding: null, // 保存解析的终点建筑物名称
    pendingRouteResult: null, // 待生成的路径结果
    confirmEventsbound: false, // 确认对话框事件是否已绑定

    // 初始化模块
    init() {
        console.log('初始化路径规划模块');

        try {
            // 获取DOM元素
            this.panel = document.getElementById('route-planning-panel');
            this.panelClose = document.getElementById('route-panel-close');
            this.startInput = document.getElementById('route-start');
            this.endInput = document.getElementById('route-end');
            this.startClearBtn = document.getElementById('route-start-clear');
            this.endClearBtn = document.getElementById('route-end-clear');
            this.calculateBtn = document.getElementById('route-calculate');
            this.clearBtn = document.getElementById('route-clear');
            this.resultPanel = document.getElementById('route-result');
            this.distanceValue = document.getElementById('route-distance-value');
            this.timeValue = document.getElementById('route-time-value');
            this.mapClickToggle = document.getElementById('map-click-enabled');

            if (!this.panel) {
                console.error('路径规划面板元素未找到');
                return false;
            }

            // 创建路径图层
            this.createRouteLayers();

            // 加载道路网络数据
            this.loadRoadNetwork();

            // 绑定事件
            this.bindEvents();

            // 绑定输入框事件
            this.bindInputEvents();

            // 检查URL参数，加载分享的路径
            this.loadSharedRoute();

            this.isInitialized = true;
            console.log('路径规划模块初始化成功');
            return true;

        } catch (error) {
            console.error('路径规划模块初始化失败:', error);
            return false;
        }
    },

    // 创建路径图层
    createRouteLayers() {
        console.log('创建路径图层');

        if (!MapModule.isMapInitialized()) {
            console.warn('地图未初始化，无法创建路径图层');
            return;
        }

        try {
            // 创建路径图层
            this.routeLayer = new ol.layer.Vector({
                source: new ol.source.Vector(),
                style: this.getRouteStyle(),
                zIndex: 1000 // 确保路径图层在最上层
            });

            // 创建起点标记图层
            this.startPointLayer = new ol.layer.Vector({
                source: new ol.source.Vector(),
                style: this.getStartPointStyle(),
                zIndex: 1001
            });

            // 创建终点标记图层
            this.endPointLayer = new ol.layer.Vector({
                source: new ol.source.Vector(),
                style: this.getEndPointStyle(),
                zIndex: 1002
            });

            // 添加到地图
            MapModule.addLayer(this.routeLayer);
            MapModule.addLayer(this.startPointLayer);
            MapModule.addLayer(this.endPointLayer);

            console.log('路径图层创建完成');

        } catch (error) {
            console.error('创建路径图层失败:', error);
        }
    },

    // 获取路径样式
    getRouteStyle() {
        return new ol.style.Style({
            stroke: new ol.style.Stroke({
                color: CONFIG.routing?.routeColor || 'rgba(255, 0, 0, 0.9)', // 使用配置中的红色
                width: CONFIG.routing?.routeWidth || 2, // 使用较细的线条宽度
                lineDash: [10, 5]
            })
        });
    },

    // 获取起点样式
    getStartPointStyle() {
        return new ol.style.Style({
            image: new ol.style.Circle({
                radius: 8,
                fill: new ol.style.Fill({
                    color: '#4caf50'
                }),
                stroke: new ol.style.Stroke({
                    color: '#ffffff',
                    width: 2
                })
            }),
            text: new ol.style.Text({
                text: '起',
                font: '12px Arial',
                fill: new ol.style.Fill({
                    color: '#ffffff'
                })
            })
        });
    },

    // 绑定输入框事件
    bindInputEvents() {
        console.log('绑定路径规划输入框事件');

        const startInput = document.getElementById('route-start');
        const endInput = document.getElementById('route-end');
        const startClear = document.getElementById('route-start-clear');
        const endClear = document.getElementById('route-end-clear');

        if (!startInput || !endInput) {
            console.error('路径规划输入框未找到');
            return;
        }

        // 起点输入框事件
        startInput.addEventListener('input', (e) => {
            this.handleLocationInput(e.target.value, 'start');
            this.toggleClearButton(startClear, e.target.value);
        });

        startInput.addEventListener('blur', (e) => {
            this.parseLocationInput(e.target.value, 'start');
        });

        // 终点输入框事件
        endInput.addEventListener('input', (e) => {
            this.handleLocationInput(e.target.value, 'end');
            this.toggleClearButton(endClear, e.target.value);
        });

        endInput.addEventListener('blur', (e) => {
            this.parseLocationInput(e.target.value, 'end');
        });

        // 清除按钮事件
        if (startClear) {
            startClear.addEventListener('click', () => {
                startInput.value = '';
                this.startPoint = null;
                this.clearStartMarker();
                this.toggleClearButton(startClear, '');
            });
        }

        if (endClear) {
            endClear.addEventListener('click', () => {
                endInput.value = '';
                this.endPoint = null;
                this.clearEndMarker();
                this.toggleClearButton(endClear, '');
            });
        }

        console.log('路径规划输入框事件绑定完成');
    },

    // 切换清除按钮显示
    toggleClearButton(clearBtn, value) {
        if (clearBtn) {
            clearBtn.style.display = value.trim() ? 'block' : 'none';
        }
    },

    // 处理位置输入（实时搜索建议）
    handleLocationInput(value, type) {
        if (!value || value.length < 2) {
            this.hideSuggestions(type);
            return;
        }

        // 检查是否是坐标格式
        if (this.isCoordinateFormat(value)) {
            this.hideSuggestions(type);
            return;
        }

        // 搜索建筑物建议
        this.searchBuildingSuggestions(value, type);
    },

    // 检查是否是坐标格式
    isCoordinateFormat(value) {
        // 匹配格式：纬度,经度 或 纬度, 经度
        const coordPattern = /^\s*\d+\.?\d*\s*,\s*\d+\.?\d*\s*$/;
        return coordPattern.test(value);
    },

    // 搜索建筑物建议
    searchBuildingSuggestions(query, type) {
        if (!SearchModule || !SearchModule.buildingsData) {
            return;
        }

        const suggestions = SearchModule.filterBuildings(query).slice(0, 5); // 最多显示5个建议
        this.showSuggestions(suggestions, type);
    },

    // 显示搜索建议
    showSuggestions(suggestions, type) {
        // 这里可以实现下拉建议列表
        // 暂时简化处理
        console.log(`${type}点搜索建议:`, suggestions.map(s => s.name));
    },

    // 隐藏搜索建议
    hideSuggestions(type) {
        // 隐藏建议列表
        console.log(`隐藏${type}点搜索建议`);
    },

    // 解析位置输入
    parseLocationInput(value, type) {
        if (!value || !value.trim()) {
            return;
        }

        const trimmedValue = value.trim();

        // 尝试解析坐标
        if (this.isCoordinateFormat(trimmedValue)) {
            const coords = this.parseCoordinates(trimmedValue);
            if (coords) {
                this.setLocationFromCoordinates(coords, type);
                return;
            }
        }

        // 尝试解析建筑物名称
        this.setLocationFromBuildingName(trimmedValue, type);
    },

    // 解析坐标字符串
    parseCoordinates(coordString) {
        try {
            const parts = coordString.split(',').map(s => s.trim());
            if (parts.length !== 2) {
                return null;
            }

            const lat = parseFloat(parts[0]);
            const lon = parseFloat(parts[1]);

            if (isNaN(lat) || isNaN(lon)) {
                return null;
            }

            // 验证坐标范围（大致的中国范围）
            if (lat < 18 || lat > 54 || lon < 73 || lon > 135) {
                console.warn('坐标超出合理范围');
                return null;
            }

            return [lat, lon];
        } catch (error) {
            console.error('坐标解析失败:', error);
            return null;
        }
    },

    // 从坐标设置位置
    setLocationFromCoordinates(coords, type) {
        const [lat, lon] = coords;
        const point = ol.proj.fromLonLat([lon, lat]);

        if (type === 'start') {
            this.startPoint = point;
            this.showStartMarker(point);
            console.log('起点坐标设置成功:', coords);
        } else {
            this.endPoint = point;
            this.showEndMarker(point);
            console.log('终点坐标设置成功:', coords);
        }
    },

    // 从建筑物名称设置位置
    setLocationFromBuildingName(buildingName, type) {
        if (!SearchModule || !SearchModule.buildingsData) {
            console.warn('建筑物数据未加载');
            this.showMessage('建筑物数据未加载', 'warning');
            return;
        }

        // 搜索建筑物
        const results = SearchModule.filterBuildings(buildingName);

        if (results.length === 0) {
            console.warn('未找到建筑物:', buildingName);
            this.showMessage(`未找到建筑物"${buildingName}"`, 'warning');
            return;
        }

        // 使用第一个匹配结果
        const building = results[0];
        const point = building.coordinate;

        if (type === 'start') {
            this.startPoint = point;
            this.lastResolvedStartBuilding = building.name; // 保存解析的建筑物名称
            this.displayStartMarker(point);
            console.log('起点建筑物设置成功:', building.name);
            this.showMessage(`起点设置为：${building.name}`, 'success');
        } else {
            this.endPoint = point;
            this.lastResolvedEndBuilding = building.name; // 保存解析的建筑物名称
            this.displayEndMarker(point);
            console.log('终点建筑物设置成功:', building.name);
            this.showMessage(`终点设置为：${building.name}`, 'success');
        }

        // 更新输入框显示建筑物名称
        const inputId = type === 'start' ? 'route-start' : 'route-end';
        const input = document.getElementById(inputId);
        if (input && building.name !== buildingName) {
            input.value = building.name;
        }
    },

    // 加载道路网络数据
    loadRoadNetwork() {
        console.log('开始加载道路网络数据');

        try {
            // 从GeoJSON数据加载道路网络
            fetch('./geojson2_data/roads.geojson')
                .then(response => response.json())
                .then(data => {
                    console.log('道路网络数据加载成功，道路数量:', data.features.length);
                    this.buildRoadNetwork(data);
                })
                .catch(error => {
                    console.error('加载道路网络数据失败:', error);
                    // 如果加载失败，回退到直线路径计算
                    console.warn('将使用直线路径计算作为备选方案');
                });

        } catch (error) {
            console.error('加载道路网络数据异常:', error);
        }
    },

    // 构建道路网络图
    buildRoadNetwork(geojsonData) {
        console.log('构建道路网络图');

        try {
            this.roadNodes.clear();
            this.roadEdges = [];

            const tolerance = 5.0; // 坐标容差（米），用于判断节点是否相同

            // 遍历所有道路要素
            geojsonData.features.forEach((feature, featureIndex) => {
                if (feature.geometry.type === 'LineString') {
                    const coordinates = feature.geometry.coordinates;

                    console.log(`处理道路 ${featureIndex}: ${coordinates.length} 个坐标点`);

                    // 处理道路的每一段
                    for (let i = 0; i < coordinates.length - 1; i++) {
                        // 当前坐标点和下一个坐标点
                        const currentCoord = coordinates[i];
                        const nextCoord = coordinates[i + 1];

                        // 转换为投影坐标（数据已经是EPSG:3857格式）
                        const currentPoint = currentCoord;
                        const nextPoint = nextCoord;

                        // 查找或创建当前节点
                        let currentNodeId = this.findOrCreateNode(currentPoint, tolerance);

                        // 查找或创建下一个节点
                        let nextNodeId = this.findOrCreateNode(nextPoint, tolerance);

                        // 如果两个节点不同，创建边
                        if (currentNodeId !== nextNodeId) {
                            // 计算这一段的长度
                            const segmentLength = this.calculateDistance(currentPoint, nextPoint);

                            // 创建双向边（假设道路是双向的）
                            this.roadEdges.push({
                                from: currentNodeId,
                                to: nextNodeId,
                                length: segmentLength,
                                geometry: [currentCoord, nextCoord],
                                featureIndex: featureIndex,
                                segmentIndex: i
                            });

                            this.roadEdges.push({
                                from: nextNodeId,
                                to: currentNodeId,
                                length: segmentLength,
                                geometry: [nextCoord, currentCoord],
                                featureIndex: featureIndex,
                                segmentIndex: i
                            });
                        }
                    }
                }
            });

            console.log('道路网络构建完成:', {
                nodes: this.roadNodes.size,
                edges: this.roadEdges.length,
                avgEdgesPerNode: (this.roadEdges.length / this.roadNodes.size).toFixed(2)
            });

            // 验证网络连通性
            this.validateNetworkConnectivity();

            // 添加调试信息
            if (this.roadNodes.size > 0) {
                console.log('道路网络样本节点:', Array.from(this.roadNodes.entries()).slice(0, 3));
                console.log('道路网络样本边:', this.roadEdges.slice(0, 3));
            }

        } catch (error) {
            console.error('构建道路网络失败:', error);
        }
    },

    // 验证网络连通性
    validateNetworkConnectivity() {
        console.log('验证道路网络连通性');

        if (this.roadNodes.size === 0) {
            console.warn('道路网络为空');
            return;
        }

        // 统计每个节点的连接数
        const nodeConnections = new Map();
        this.roadEdges.forEach(edge => {
            const count = nodeConnections.get(edge.from) || 0;
            nodeConnections.set(edge.from, count + 1);
        });

        // 找出孤立节点
        const isolatedNodes = [];
        for (let nodeId of this.roadNodes.keys()) {
            if (!nodeConnections.has(nodeId) || nodeConnections.get(nodeId) === 0) {
                isolatedNodes.push(nodeId);
            }
        }

        console.log('网络连通性分析:', {
            totalNodes: this.roadNodes.size,
            connectedNodes: this.roadNodes.size - isolatedNodes.length,
            isolatedNodes: isolatedNodes.length,
            maxConnections: Math.max(...nodeConnections.values()),
            minConnections: Math.min(...nodeConnections.values())
        });

        if (isolatedNodes.length > 0) {
            console.warn(`发现 ${isolatedNodes.length} 个孤立节点`);
        }
    },

    // 查找或创建节点
    findOrCreateNode(coordinate, tolerance) {
        // 查找是否已存在相近的节点
        for (let [nodeId, nodeCoord] of this.roadNodes) {
            // 计算欧几里得距离（坐标已经是投影坐标系，单位为米）
            const distance = Math.sqrt(
                Math.pow(coordinate[0] - nodeCoord[0], 2) +
                Math.pow(coordinate[1] - nodeCoord[1], 2)
            );

            if (distance < tolerance) { // tolerance已经是米为单位
                return nodeId;
            }
        }

        // 创建新节点
        const nodeId = this.roadNodes.size;
        this.roadNodes.set(nodeId, coordinate);
        return nodeId;
    },

    // 基于道路网络计算路径
    calculateRoadRoute(startPoint, endPoint) {
        console.log('开始基于道路网络的路径计算');

        try {
            // 找到最近的道路节点
            const startNodeId = this.findNearestNode(startPoint);
            const endNodeId = this.findNearestNode(endPoint);

            if (startNodeId === null || endNodeId === null) {
                console.warn('无法找到起点或终点附近的道路节点');
                return null;
            }

            if (startNodeId === endNodeId) {
                console.warn('起点和终点在同一个道路节点上');
                return null;
            }

            console.log('找到道路节点:', { startNodeId, endNodeId });

            // 使用Dijkstra算法计算最短路径
            const path = this.dijkstra(startNodeId, endNodeId);

            if (!path || path.length === 0) {
                console.warn('无法找到连接起终点的道路路径');
                return null;
            }

            console.log('找到道路路径，节点数:', path.length);

            // 构建路径几何和计算距离
            const routeResult = this.buildRouteFromPath(path, startPoint, endPoint);

            return routeResult;

        } catch (error) {
            console.error('道路路径计算失败:', error);
            return null;
        }
    },

    // 找到最近的道路节点
    findNearestNode(point) {
        let nearestNodeId = null;
        let minDistance = Infinity;

        for (let [nodeId, nodeCoord] of this.roadNodes) {
            const distance = this.calculateDistance(point, nodeCoord);
            if (distance < minDistance) {
                minDistance = distance;
                nearestNodeId = nodeId;
            }
        }

        // 如果最近节点距离超过500米，认为太远
        if (minDistance > 500) {
            console.warn(`最近的道路节点距离${minDistance.toFixed(0)}米，可能太远`);
            return null;
        }

        return nearestNodeId;
    },

    // Dijkstra算法实现（最短路径）
    dijkstra(startNodeId, endNodeId) {
        console.log('执行Dijkstra算法:', { startNodeId, endNodeId });

        // 初始化距离和前驱节点
        const distances = new Map();
        const previous = new Map();
        const visited = new Set();
        const queue = [];

        // 初始化所有节点距离为无穷大
        for (let nodeId of this.roadNodes.keys()) {
            distances.set(nodeId, Infinity);
            previous.set(nodeId, null);
        }

        // 起点距离为0
        distances.set(startNodeId, 0);
        queue.push({ nodeId: startNodeId, distance: 0 });

        while (queue.length > 0) {
            // 找到距离最小的未访问节点
            queue.sort((a, b) => a.distance - b.distance);
            const current = queue.shift();

            if (visited.has(current.nodeId)) {
                continue;
            }

            visited.add(current.nodeId);

            // 如果到达终点，停止搜索
            if (current.nodeId === endNodeId) {
                break;
            }

            // 检查所有邻居节点
            const neighbors = this.getNeighbors(current.nodeId);
            for (let neighbor of neighbors) {
                if (visited.has(neighbor.nodeId)) {
                    continue;
                }

                const newDistance = distances.get(current.nodeId) + neighbor.distance;

                if (newDistance < distances.get(neighbor.nodeId)) {
                    distances.set(neighbor.nodeId, newDistance);
                    previous.set(neighbor.nodeId, current.nodeId);
                    queue.push({ nodeId: neighbor.nodeId, distance: newDistance });
                }
            }
        }

        // 重建路径
        const path = [];
        let currentNode = endNodeId;

        while (currentNode !== null) {
            path.unshift(currentNode);
            currentNode = previous.get(currentNode);
        }

        // 检查是否找到有效路径
        if (path.length === 0 || path[0] !== startNodeId) {
            console.warn('无法找到从起点到终点的路径');
            return null;
        }

        console.log('最短路径算法完成，路径长度:', path.length);
        return path;
    },


    // 识别起点和终点最近的建筑物
    identifyNearestBuildings() {
        console.log('开始识别起点和终点最近的建筑物');

        // 识别起点最近建筑物
        if (this.startPoint) {
            this.startNearestBuilding = this.findNearestBuilding(this.startPoint);
            console.log('起点最近建筑物:', this.startNearestBuilding?.name || '未找到');
        }

        // 识别终点最近建筑物
        if (this.endPoint) {
            this.endNearestBuilding = this.findNearestBuilding(this.endPoint);
            console.log('终点最近建筑物:', this.endNearestBuilding?.name || '未找到');
        }
    },

    // 查找最近的建筑物
    findNearestBuilding(coordinate) {
        if (!coordinate) {
            console.log('坐标为空，无法查找最近建筑物');
            return null;
        }

        // 检查SearchModule是否已初始化并有建筑物数据
        if (!SearchModule || !SearchModule.isInitialized || !SearchModule.buildingsData || SearchModule.buildingsData.length === 0) {
            console.log('SearchModule未初始化或无建筑物数据', {
                SearchModule: !!SearchModule,
                isInitialized: SearchModule ? SearchModule.isInitialized : false,
                buildingsData: SearchModule ? SearchModule.buildingsData?.length : 0
            });
            return null;
        }

        let nearestBuilding = null;
        let minDistance = Infinity;
        const maxSearchDistance = 500; // 最大搜索距离（米）

        console.log(`开始查找最近建筑物，总共${SearchModule.buildingsData.length}个建筑物`);

        SearchModule.buildingsData.forEach((building, index) => {
            if (building.coordinate) {
                // 计算距离（使用欧几里得距离）
                const dx = coordinate[0] - building.coordinate[0];
                const dy = coordinate[1] - building.coordinate[1];
                const distance = Math.sqrt(dx * dx + dy * dy);

                // 转换为实际距离（米）
                const lonLat1 = ol.proj.toLonLat(coordinate);
                const lonLat2 = ol.proj.toLonLat(building.coordinate);
                const realDistance = ol.sphere.getDistance(lonLat1, lonLat2);

                console.log(`建筑物${index + 1}: ${building.name}, 距离: ${realDistance.toFixed(0)}米`);

                if (distance < minDistance && realDistance <= maxSearchDistance) {
                    minDistance = distance;
                    nearestBuilding = building;
                }
            } else {
                console.log(`建筑物${index + 1}: ${building.name}, 无坐标信息`);
            }
        });

        const finalDistance = nearestBuilding ? ol.sphere.getDistance(
            ol.proj.toLonLat(coordinate),
            ol.proj.toLonLat(nearestBuilding.coordinate)
        ) : null;

        console.log('查找最近建筑物结果:', {
            coordinate: ol.proj.toLonLat(coordinate),
            nearestBuilding: nearestBuilding ? nearestBuilding.name : null,
            distance: finalDistance ? `${finalDistance.toFixed(0)}米` : null,
            totalBuildings: SearchModule.buildingsData.length,
            maxSearchDistance: `${maxSearchDistance}米`
        });

        return nearestBuilding;
    },

    // 显示起点和终点的最近建筑物
    displayNearestBuildings() {
        try {
            const startLocationElement = document.getElementById('route-start-location');
            const endLocationElement = document.getElementById('route-end-location');

            // 优先使用已解析的建筑物名称
            if (startLocationElement) {
                if (this.lastResolvedStartBuilding) {
                    startLocationElement.textContent = this.lastResolvedStartBuilding;
                } else if (this.startPoint) {
                    const nearestStartBuilding = this.findNearestBuilding(this.startPoint);
                    startLocationElement.textContent = nearestStartBuilding ? nearestStartBuilding.name : '未知位置';
                } else {
                    startLocationElement.textContent = '未知位置';
                }
            }

            if (endLocationElement) {
                if (this.lastResolvedEndBuilding) {
                    endLocationElement.textContent = this.lastResolvedEndBuilding;
                } else if (this.endPoint) {
                    const nearestEndBuilding = this.findNearestBuilding(this.endPoint);
                    endLocationElement.textContent = nearestEndBuilding ? nearestEndBuilding.name : '未知位置';
                } else {
                    endLocationElement.textContent = '未知位置';
                }
            }

            console.log('显示最近建筑物:', {
                startBuilding: this.lastResolvedStartBuilding || '未解析',
                endBuilding: this.lastResolvedEndBuilding || '未解析'
            });

        } catch (error) {
            console.error('显示最近建筑物失败:', error);
        }
    },

    // 获取节点的邻居
    getNeighbors(nodeId) {
        const neighbors = [];

        for (let edge of this.roadEdges) {
            if (edge.from === nodeId) {
                neighbors.push({
                    nodeId: edge.to,
                    distance: edge.length,
                    edge: edge
                });
            }
        }

        return neighbors;
    },

    // 从路径节点构建路径几何
    buildRouteFromPath(path, startPoint, endPoint) {
        console.log('构建路径几何，节点数:', path.length);

        try {
            const routeCoordinates = [];
            let totalDistance = 0;
            const segments = [];

            // 添加起点
            routeCoordinates.push(startPoint);

            // 添加起点到第一个道路节点的连接
            const firstNodeCoord = this.roadNodes.get(path[0]);
            if (firstNodeCoord) {
                routeCoordinates.push(firstNodeCoord);
                totalDistance += this.calculateDistance(startPoint, firstNodeCoord);
            }

            // 添加道路段
            for (let i = 0; i < path.length - 1; i++) {
                const fromNodeId = path[i];
                const toNodeId = path[i + 1];

                // 找到连接这两个节点的边
                const edge = this.roadEdges.find(e => e.from === fromNodeId && e.to === toNodeId);

                if (edge) {
                    // 添加道路几何（跳过第一个点，避免重复）
                    const roadCoords = edge.geometry.slice(1);
                    routeCoordinates.push(...roadCoords);

                    totalDistance += edge.length;
                    segments.push({
                        from: fromNodeId,
                        to: toNodeId,
                        distance: edge.length,
                        coordinates: edge.geometry
                    });
                } else {
                    // 如果找不到边，使用直线连接
                    console.warn(`找不到连接节点${fromNodeId}和${toNodeId}的道路边，使用直线连接`);
                    const fromCoord = this.roadNodes.get(fromNodeId);
                    const toCoord = this.roadNodes.get(toNodeId);

                    if (fromCoord && toCoord) {
                        routeCoordinates.push(toCoord);

                        const segmentDistance = this.calculateDistance(fromCoord, toCoord);
                        totalDistance += segmentDistance;
                        segments.push({
                            from: fromNodeId,
                            to: toNodeId,
                            distance: segmentDistance,
                            coordinates: [fromCoord, toCoord]
                        });
                    }
                }
            }

            // 添加最后一个道路节点到终点的连接
            const lastNodeCoord = this.roadNodes.get(path[path.length - 1]);
            if (lastNodeCoord) {
                routeCoordinates.push(endPoint);
                totalDistance += this.calculateDistance(lastNodeCoord, endPoint);
            }

            // 创建路径几何（坐标已经是投影坐标系）
            const routeGeometry = new ol.geom.LineString(routeCoordinates);

            // 计算预估时间
            const estimatedTime = this.calculateEstimatedTime(totalDistance);

            console.log('路径几何构建完成:', {
                totalDistance: totalDistance.toFixed(2) + '米',
                segments: segments.length,
                coordinates: routeCoordinates.length
            });

            return {
                startPoint: startPoint,
                endPoint: endPoint,
                distance: totalDistance,
                estimatedTime: estimatedTime,
                geometry: routeGeometry,
                segments: segments,
                path: path
            };

        } catch (error) {
            console.error('构建路径几何失败:', error);
            return null;
        }
    },

    // 获取终点样式
    getEndPointStyle() {
        return new ol.style.Style({
            image: new ol.style.Circle({
                radius: 8,
                fill: new ol.style.Fill({
                    color: '#f44336'
                }),
                stroke: new ol.style.Stroke({
                    color: '#ffffff',
                    width: 2
                })
            }),
            text: new ol.style.Text({
                text: '终',
                font: '12px Arial',
                fill: new ol.style.Fill({
                    color: '#ffffff'
                })
            })
        });
    },

    // 绑定事件
    bindEvents() {
        console.log('绑定路径规划面板事件');

        // 绑定关闭按钮事件
        if (this.panelClose) {
            this.panelClose.addEventListener('click', () => {
                this.hidePanel();
            });
        }

        // 绑定起点清除按钮事件
        if (this.startClearBtn) {
            this.startClearBtn.addEventListener('click', () => {
                this.clearStartPoint();
            });
        }

        // 绑定终点清除按钮事件
        if (this.endClearBtn) {
            this.endClearBtn.addEventListener('click', () => {
                this.clearEndPoint();
            });
        }

        // 绑定计算路径按钮事件（占位符）
        if (this.calculateBtn) {
            this.calculateBtn.addEventListener('click', () => {
                this.calculateRoute();
            });
        }

        // 绑定清除路径按钮事件
        if (this.clearBtn) {
            this.clearBtn.addEventListener('click', () => {
                this.clearRoute();
            });
        }

        // 绑定导出路径按钮事件
        const exportBtn = document.getElementById('route-export');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportRoute();
            });
        }

        // 绑定地图点击开关事件
        if (this.mapClickToggle) {
            this.mapClickToggle.addEventListener('change', (e) => {
                this.toggleMapClick(e.target.checked);
            });
            console.log('地图点击开关事件已绑定');
        }

        // 绑定分享路径按钮事件
        const shareBtn = document.getElementById('route-share');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => {
                this.shareRoute();
            });
        }

        // 绑定面板外部点击关闭事件（排除地图区域）
        document.addEventListener('click', (e) => {
            if (this.panel &&
                this.panel.style.display !== 'none' &&
                !this.panel.contains(e.target) &&
                !e.target.closest('#route-planning-btn') &&
                !e.target.closest('.ol-viewport') &&  // 排除地图区域
                !e.target.closest('#map') &&          // 排除地图容器
                !e.target.closest('.route-planning-reminder-modal') &&  // 排除路径规划提醒模态框
                !e.target.closest('#route-report-confirm-modal') &&     // 排除路径规划确认模态框
                !e.target.closest('#route-report-modal')) {             // 排除路径规划报告模态框
                // 延迟关闭，避免与按钮点击冲突
                setTimeout(() => {
                    this.hidePanel();
                }, 100);
            }
        });

        console.log('路径规划面板事件绑定完成');
    },

    // 启用地图点击选择模式
    enableMapClickSelection() {
        console.log('启用地图点击选择模式');

        if (!MapModule.isMapInitialized()) {
            console.warn('地图未初始化，无法启用点击选择');
            return;
        }

        try {
            // 添加地图点击事件监听器
            MapModule.map.on('click', this.handleMapClick.bind(this));

            // 改变鼠标样式，提示用户可以点击
            MapModule.map.getViewport().style.cursor = 'crosshair';

            console.log('地图点击选择模式已启用');

        } catch (error) {
            console.error('启用地图点击选择失败:', error);
        }
    },

    // 禁用地图点击选择模式
    disableMapClickSelection() {
        console.log('禁用地图点击选择模式');

        if (!MapModule.isMapInitialized()) {
            return;
        }

        try {
            // 移除地图点击事件监听器
            MapModule.map.un('click', this.handleMapClick.bind(this));

            // 恢复鼠标样式
            MapModule.map.getViewport().style.cursor = '';

            console.log('地图点击选择模式已禁用');

        } catch (error) {
            console.error('禁用地图点击选择失败:', error);
        }
    },

    // 切换地图点击功能
    toggleMapClick(enabled) {
        this.mapClickEnabled = enabled;
        console.log(`🖱️ 地图点击功能已${enabled ? '开启' : '关闭'}`);

        // 显示状态提示
        const statusText = enabled ? '开启' : '关闭';
        const statusIcon = enabled ? '✅' : '🚫';
        this.showMessage(`${statusIcon} 地图点击设置已${statusText}`, enabled ? 'success' : 'info');

        // 如果关闭了地图点击，清除当前的选择状态
        if (!enabled) {
            this.clearSelectionState();
        }
    },

    // 清除选择状态
    clearSelectionState() {
        // 重置选择模式
        this.isSelectingStart = false;
        this.isSelectingEnd = false;

        // 更新按钮状态
        this.updateSelectionButtons();

        console.log('🧹 已清除地图点击选择状态');
    },

    // 处理地图点击事件
    handleMapClick(event) {
        console.log('🗺️ 路径规划模块收到地图点击事件，坐标:', event.coordinate);

        // 检查地图点击开关是否启用
        if (!this.mapClickEnabled) {
            console.log('🚫 地图点击功能已关闭，忽略点击事件');
            return;
        }

        try {
            const coordinate = event.coordinate;

            // 🎯 首先检查是否点击了建筑物，如果是则不处理路径规划
            if (MapModule && MapModule.map) {
                const feature = MapModule.map.forEachFeatureAtPixel(event.pixel, (feature, layer) => {
                    const layerName = layer.get('name');
                    if (layerName === 'buildings') {
                        console.log('🏢 路径规划模块检测到建筑物点击，让建筑物模块处理');
                        return feature;
                    }
                    return null;
                });

                // 如果点击了建筑物，不处理路径规划逻辑
                if (feature) {
                    console.log('🏢 点击了建筑物，路径规划模块跳过处理');
                    return;
                }
            }

            console.log('🗺️ 没有点击建筑物，继续路径规划逻辑');

            // 检查是否处于导航模式（通过搜索设置了终点）
            if (this.isNavigationMode && this.endPoint && !this.startPoint) {
                console.log('导航模式：设置起点，保持已设置的终点');
                this.setStartPoint(coordinate);
                // 起点设置完成，用户需要手动点击"计算路径"按钮
                console.log('起点已设置，请点击"计算路径"按钮进行路径计算');
                return;
            }

            // 普通模式：按原有逻辑设置起点和终点
            if (!this.startPoint) {
                this.setStartPoint(coordinate);
            } else if (!this.endPoint) {
                this.setEndPoint(coordinate);
                // 终点设置完成，用户需要手动点击"计算路径"按钮
                console.log('起点和终点已设置，请点击"计算路径"按钮进行路径计算');
            } else {
                // 如果起点和终点都已设置，重新设置起点
                this.clearStartPoint();
                this.setStartPoint(coordinate);
            }

        } catch (error) {
            console.error('处理地图点击失败:', error);
        }
    },

    // 设置起点
    setStartPoint(coordinate, buildingName = null) {
        console.log('设置起点:', coordinate, buildingName ? `(${buildingName})` : '');

        // 📊 记录设置起点行为
        if (typeof UserTrackingModule !== 'undefined') {
            UserTrackingModule.trackRouteAction('设置起点', {
                coordinate: coordinate,
                buildingName: buildingName
            });
        }

        try {
            this.startPoint = coordinate;

            // 如果提供了建筑物名称，保存它
            if (buildingName) {
                this.lastResolvedStartBuilding = buildingName;
                console.log('保存起点建筑物名称:', buildingName);
            }

            // 更新起点输入框显示
            if (this.startInput) {
                if (buildingName) {
                    // 如果有建筑物名称，显示建筑物名称
                    this.startInput.value = buildingName;
                } else {
                    // 否则显示坐标
                    const lonLat = ol.proj.toLonLat(coordinate);
                    this.startInput.value = `${lonLat[1].toFixed(6)}, ${lonLat[0].toFixed(6)}`;
                }
            }

            // 显示起点标记
            this.displayStartMarker(coordinate);

            console.log('起点已设置');

        } catch (error) {
            console.error('设置起点失败:', error);
        }
    },

    // 设置终点
    setEndPoint(coordinate, buildingName = null) {
        console.log('设置终点:', coordinate, buildingName ? `(${buildingName})` : '');

        // 📊 记录设置终点行为
        if (typeof UserTrackingModule !== 'undefined') {
            UserTrackingModule.trackRouteAction('设置终点', {
                coordinate: coordinate,
                buildingName: buildingName
            });
        }

        try {
            this.endPoint = coordinate;

            // 如果提供了建筑物名称，保存它
            if (buildingName) {
                this.lastResolvedEndBuilding = buildingName;
                console.log('保存终点建筑物名称:', buildingName);
            }

            // 更新终点输入框显示
            if (this.endInput) {
                if (buildingName) {
                    // 如果有建筑物名称，显示建筑物名称
                    this.endInput.value = buildingName;
                } else {
                    // 否则显示坐标
                    const lonLat = ol.proj.toLonLat(coordinate);
                    this.endInput.value = `${lonLat[1].toFixed(6)}, ${lonLat[0].toFixed(6)}`;
                }
            }

            // 显示终点标记
            this.displayEndMarker(coordinate);

            console.log('终点已设置');

        } catch (error) {
            console.error('设置终点失败:', error);
        }
    },

    // 显示起点标记
    displayStartMarker(coordinate) {
        console.log('显示起点标记');

        try {
            // 清除之前的起点标记
            if (this.startPointLayer) {
                this.startPointLayer.getSource().clear();
            }

            // 创建起点要素
            const startFeature = new ol.Feature({
                geometry: new ol.geom.Point(coordinate),
                type: 'start'
            });

            // 设置起点样式（使用配置中的颜色）
            const startStyle = new ol.style.Style({
                image: new ol.style.Circle({
                    radius: 8,
                    fill: new ol.style.Fill({
                        color: CONFIG.routing.startMarkerColor
                    }),
                    stroke: new ol.style.Stroke({
                        color: '#ffffff',
                        width: 2
                    })
                }),
                text: new ol.style.Text({
                    text: '起',
                    font: '12px Arial',
                    fill: new ol.style.Fill({
                        color: '#ffffff'
                    })
                })
            });

            startFeature.setStyle(startStyle);

            // 添加到起点图层
            this.startPointLayer.getSource().addFeature(startFeature);

            console.log('起点标记已显示');

        } catch (error) {
            console.error('显示起点标记失败:', error);
        }
    },

    // 显示终点标记
    displayEndMarker(coordinate) {
        console.log('显示终点标记');

        try {
            // 清除之前的终点标记
            if (this.endPointLayer) {
                this.endPointLayer.getSource().clear();
            }

            // 创建终点要素
            const endFeature = new ol.Feature({
                geometry: new ol.geom.Point(coordinate),
                type: 'end'
            });

            // 设置终点样式（使用配置中的颜色）
            const endStyle = new ol.style.Style({
                image: new ol.style.Circle({
                    radius: 8,
                    fill: new ol.style.Fill({
                        color: CONFIG.routing.endMarkerColor
                    }),
                    stroke: new ol.style.Stroke({
                        color: '#ffffff',
                        width: 2
                    })
                }),
                text: new ol.style.Text({
                    text: '终',
                    font: '12px Arial',
                    fill: new ol.style.Fill({
                        color: '#ffffff'
                    })
                })
            });

            endFeature.setStyle(endStyle);

            // 添加到终点图层
            this.endPointLayer.getSource().addFeature(endFeature);

            console.log('终点标记已显示');

        } catch (error) {
            console.error('显示终点标记失败:', error);
        }
    },

    // 显示面板
    showPanel() {
        console.log('显示路径规划面板');

        // 📊 记录路径规划面板打开行为
        if (typeof UserTrackingModule !== 'undefined') {
            UserTrackingModule.trackRouteAction('打开路径规划面板', {});
        }

        if (!this.panel) {
            console.error('路径规划面板未初始化');
            return;
        }

        try {
            // 🔄 智能重置：只有在没有预设终点时才重置状态
            const hasPresetEndPoint = this.endPoint !== null;
            if (!hasPresetEndPoint) {
                console.log('🔄 重置路径规划状态，准备重新开始');
                this.resetToInitialState();
            } else {
                console.log('🎯 保留预设终点，继续路径规划');
                // 只清除路径显示，保留起点和终点
                this.clearRouteDisplay();

                // 重新显示终点标记和输入框内容
                if (this.endPoint) {
                    this.displayEndMarker(this.endPoint);
                    if (this.endInput && this.lastResolvedEndBuilding) {
                        this.endInput.value = this.lastResolvedEndBuilding;
                    }
                }
            }

            // 显示面板
            this.panel.style.display = 'block';

            // 添加显示动画类
            setTimeout(() => {
                this.panel.classList.add('show');
            }, 10);

            // 启用地图点击选择模式
            this.enableMapClickSelection();

            // 根据当前状态显示相应提示
            if (hasPresetEndPoint) {
                this.showMessage('🎯 终点已设置，请点击地图设置起点', 'info');
            } else {
                this.showMessage('🎯 路径规划已开启，请点击地图设置起点', 'info');
            }

            console.log('路径规划面板已显示，状态已重置');

        } catch (error) {
            console.error('显示路径规划面板失败:', error);
        }
    },

    // 隐藏面板
    hidePanel() {
        console.log('隐藏路径规划面板');

        if (!this.panel) {
            console.error('路径规划面板未初始化');
            return;
        }

        try {
            // 移除显示动画类
            this.panel.classList.remove('show');

            // 禁用地图点击选择模式
            this.disableMapClickSelection();

            // 延迟隐藏面板，等待动画完成
            setTimeout(() => {
                this.panel.style.display = 'none';
            }, 300); // 与CSS动画时间匹配

            console.log('路径规划面板已隐藏');

            // 通知面板管理器面板已关闭
            if (typeof PanelManager !== 'undefined') {
                PanelManager.notifyPanelClosed(PanelManager.panelTypes.ROUTE_PLANNING);
            }

        } catch (error) {
            console.error('隐藏路径规划面板失败:', error);
        }
    },

    // 激活路径规划模式（用于导航功能）
    activateRouteMode() {
        console.log('激活路径规划模式');

        try {
            // 显示路径规划面板
            this.showPanel();

            // 启用地图点击选择模式
            this.enableMapClickSelection();

            // 设置模式标志
            this.isNavigationMode = true;

            console.log('路径规划模式已激活');

        } catch (error) {
            console.error('激活路径规划模式失败:', error);
        }
    },

    // 取消路径规划模式
    deactivateRouteMode() {
        console.log('取消路径规划模式');

        try {
            // 重置模式标志（在清除路径之前，这样clearRoute就会清除所有数据）
            this.isNavigationMode = false;

            // 清除所有路径数据
            this.clearStartPoint();
            this.clearEndPoint();
            this.lastResolvedStartBuilding = null;
            this.lastResolvedEndBuilding = null;

            // 清除路径
            this.currentRoute = null;
            if (this.routeLayer) {
                this.routeLayer.getSource().clear();
            }

            // 隐藏结果面板
            if (this.resultPanel) {
                this.resultPanel.style.display = 'none';
            }

            // 禁用地图点击选择模式
            this.disableMapClickSelection();

            console.log('路径规划模式已取消');

        } catch (error) {
            console.error('取消路径规划模式失败:', error);
        }
    },

    // 清除起点
    clearStartPoint() {
        console.log('清除起点');

        try {
            this.startPoint = null;
            if (this.startInput) {
                this.startInput.value = '';
            }

            // 清除起点标记
            if (this.startPointLayer) {
                this.startPointLayer.getSource().clear();
            }

            console.log('起点已清除');

        } catch (error) {
            console.error('清除起点失败:', error);
        }
    },

    // 清除终点
    clearEndPoint() {
        console.log('清除终点');

        try {
            this.endPoint = null;
            if (this.endInput) {
                this.endInput.value = '';
            }

            // 清除终点标记
            if (this.endPointLayer) {
                this.endPointLayer.getSource().clear();
            }

            console.log('终点已清除');

        } catch (error) {
            console.error('清除终点失败:', error);
        }
    },

    // 计算路径
    calculateRoute() {
        console.log('开始计算路径');

        // 📊 记录计算路径行为
        if (typeof UserTrackingModule !== 'undefined') {
            UserTrackingModule.trackRouteAction('计算路径', {
                hasStartPoint: !!this.startPoint,
                hasEndPoint: !!this.endPoint
            });
        }

        try {
            // 如果没有通过地图点击设置坐标，尝试从输入框解析
            if (!this.startPoint || !this.endPoint) {
                console.log('起点或终点未设置，尝试从输入框解析');
                this.parseCoordinatesFromInputs();
            }

            if (!this.startPoint || !this.endPoint) {
                console.warn('请先设置起点和终点');
                this.showMessage('请先设置起点和终点', 'warning');
                return;
            }

            // 识别起点和终点最近的建筑物
            this.identifyNearestBuildings();

            // 调试信息：检查计算路径时的建筑物名称
            console.log('计算路径时的建筑物名称状态:', {
                lastResolvedStartBuilding: this.lastResolvedStartBuilding,
                lastResolvedEndBuilding: this.lastResolvedEndBuilding,
                startNearestBuilding: this.startNearestBuilding,
                endNearestBuilding: this.endNearestBuilding,
                startPoint: this.startPoint,
                endPoint: this.endPoint
            });

            // 尝试使用道路网络计算路径
            if (this.roadNodes.size > 0) {
                console.log('使用道路网络计算路径');
                const roadRoute = this.calculateRoadRoute(this.startPoint, this.endPoint);

                if (roadRoute) {
                    // 使用道路路径
                    this.displayRoute(roadRoute.geometry);
                    const roadResult = {
                        distance: this.formatDistance(roadRoute.distance),
                        time: this.formatTime(roadRoute.estimatedTime)
                    };


                    this.currentRoute = roadRoute;

                    // 保存路径结果用于生成报告
                    this.pendingRouteResult = roadResult;

                    // 显示路径报告确认对话框
                    this.showRouteConfirmDialog(roadResult);

                    console.log('道路路径计算完成:', {
                        distance: roadRoute.distance.toFixed(2) + '米',
                        time: roadRoute.estimatedTime.toFixed(0) + '秒',
                        segments: roadRoute.segments ? roadRoute.segments.length : 0
                    });
                    return;
                }
            }

            // 回退到直线距离计算
            console.log('回退到直线路径计算');
            const distance = this.calculateDistance(this.startPoint, this.endPoint);

            // 检查距离是否超过最大限制
            if (distance > CONFIG.routing.maxDistance) {
                console.warn(`路径距离${distance.toFixed(0)}米超过最大限制${CONFIG.routing.maxDistance}米`);
                this.showMessage(`路径距离过长，请选择更近的目标点`, 'warning');
                return;
            }

            // 计算预估时间
            const estimatedTime = this.calculateEstimatedTime(distance);

            // 创建路径几何
            const routeGeometry = this.createRouteGeometry(this.startPoint, this.endPoint);

            // 显示路径
            this.displayRoute(routeGeometry);

            // 显示简化的路径结果
            const directResult = {
                distance: this.formatDistance(distance),
                time: this.formatTime(estimatedTime)
            };


            // 保存当前路径
            this.currentRoute = {
                startPoint: this.startPoint,
                endPoint: this.endPoint,
                distance: distance,
                estimatedTime: estimatedTime,
                geometry: routeGeometry
            };

            // 保存路径结果用于生成报告
            this.pendingRouteResult = directResult;

            // 显示路径报告确认对话框
            this.showRouteConfirmDialog(directResult);

            console.log('直线路径计算完成:', {
                distance: distance.toFixed(2) + '米',
                time: estimatedTime.toFixed(0) + '秒'
            });

        } catch (error) {
            console.error('计算路径失败:', error);
            this.showMessage('路径计算失败', 'error');
        }
    },

    // 重置到初始状态（完全清除所有路径规划相关状态）
    resetToInitialState() {
        console.log('🔄 重置路径规划到初始状态');

        try {
            // 🧹 清除所有路径数据
            this.startPoint = null;
            this.endPoint = null;
            this.currentRoute = null;

            // 🧹 清除建筑物名称缓存
            this.lastResolvedStartBuilding = null;
            this.lastResolvedEndBuilding = null;
            this.startNearestBuilding = null;
            this.endNearestBuilding = null;

            // 🧹 重置所有模式标志
            this.isNavigationMode = false;
            this.isSelectingStart = false;
            this.isSelectingEnd = false;

            // 🧹 清除输入框内容
            if (this.startInput) {
                this.startInput.value = '';
            }
            if (this.endInput) {
                this.endInput.value = '';
            }

            // 🧹 清除地图上的所有标记和路径
            if (this.startPointLayer) {
                this.startPointLayer.getSource().clear();
            }
            if (this.endPointLayer) {
                this.endPointLayer.getSource().clear();
            }
            if (this.routeLayer) {
                this.routeLayer.getSource().clear();
            }

            // 🧹 隐藏结果面板
            if (this.resultPanel) {
                this.resultPanel.style.display = 'none';
            }

            // 🎯 重要：确保地图点击功能开启，让用户可以重新开始
            if (this.mapClickToggle && !this.mapClickToggle.checked) {
                this.mapClickToggle.checked = true;
                this.toggleMapClick(true);
            }

            // 🧹 清除任何错误状态
            this.retryCount = 0;
            this.isCalculating = false;

            console.log('✅ 路径规划状态已完全重置到初始状态');

        } catch (error) {
            console.error('重置路径规划状态失败:', error);
        }
    },

    // 清除路径显示（保留起点和终点）
    clearRouteDisplay() {
        console.log('🧹 清除路径显示，保留起点和终点');

        try {
            // 清除路径图层
            if (this.routeLayer) {
                this.routeLayer.getSource().clear();
            }

            // 清除当前路径数据
            this.currentRoute = null;

            // 保持输入框显示当前的起点和终点（不清除）

            console.log('✅ 路径显示已清除，起点和终点保留');
        } catch (error) {
            console.error('清除路径显示失败:', error);
        }
    },

    // 清除路径
    clearRoute() {
        console.log('🔄 清除路径 - 完全重置到初始状态');

        try {
            // 🎯 关键：调用完全重置方法，确保与第一次使用完全一样
            this.resetToInitialState();

            // 🔄 额外确保：显示明确的重置提示
            this.showMessage('🔄 路径已清除，可以重新开始规划', 'info');

            console.log('✅ 路径已完全清除，状态已重置到初始状态');

        } catch (error) {
            console.error('清除路径失败:', error);
            this.showMessage('清除路径失败，请重试', 'error');
        }
    },



    // 显示路径位置提示信息
    showRouteLocationNotice() {
        console.log('显示路径位置提示信息');

        try {
            // 获取路径提示元素
            const routeLocationNotice = document.getElementById('route-location-notice');
            const routeLocationText = document.getElementById('route-location-text');

            if (!routeLocationNotice || !routeLocationText) {
                console.warn('路径位置提示元素未找到');
                return;
            }

            // 重新识别起点和终点最近的建筑物
            let startLocation = null;
            let endLocation = null;

            if (this.startPoint) {
                const startBuilding = this.findNearestBuilding(this.startPoint);
                startLocation = startBuilding ? startBuilding.name : null;
                console.log('起点最近建筑物:', startLocation);
            }

            if (this.endPoint) {
                const endBuilding = this.findNearestBuilding(this.endPoint);
                endLocation = endBuilding ? endBuilding.name : null;
                console.log('终点最近建筑物:', endLocation);
            }

            // 如果没有找到建筑物，尝试使用已解析的建筑物名称
            if (!startLocation) {
                startLocation = this.lastResolvedStartBuilding;
            }
            if (!endLocation) {
                endLocation = this.lastResolvedEndBuilding;
            }

            console.log('路径位置信息:', {
                startPoint: this.startPoint,
                endPoint: this.endPoint,
                startLocation: startLocation,
                endLocation: endLocation,
                lastResolvedStartBuilding: this.lastResolvedStartBuilding,
                lastResolvedEndBuilding: this.lastResolvedEndBuilding
            });

            // 检查是否符合特定条件：起点更靠近图书馆，终点更靠近西操场
            if (this.shouldShowSpecialNotice(startLocation, endLocation)) {
                const noticeText = `路径起点：${startLocation}，终点：${endLocation}，请注意过往车辆！`;
                routeLocationText.textContent = noticeText;
                routeLocationNotice.style.display = 'flex';
                console.log('显示特殊路径提示:', noticeText);
            } else {
                // 隐藏提示
                routeLocationNotice.style.display = 'none';
                console.log('不符合特殊提示条件，隐藏提示');
                console.log('条件检查详情:', {
                    startLocation,
                    endLocation,
                    hasStartLocation: !!startLocation,
                    hasEndLocation: !!endLocation
                });
            }

        } catch (error) {
            console.error('显示路径位置提示信息失败:', error);
        }
    },

    // 判断是否应该显示特殊提示
    shouldShowSpecialNotice(startLocation, endLocation) {
        console.log('开始检查特殊提示条件:', { startLocation, endLocation });

        if (!startLocation || !endLocation) {
            console.log('起点或终点位置为空，不显示提示');
            return false;
        }

        // 检查起点是否更靠近图书馆
        const isStartNearLibrary = startLocation.includes('图书馆') ||
                                   startLocation.includes('图书') ||
                                   startLocation.includes('Library');

        // 检查终点是否更靠近西操场
        const isEndNearWestPlayground = endLocation.includes('西操场') ||
                                        endLocation.includes('操场') ||
                                        endLocation.includes('运动场') ||
                                        endLocation.includes('体育场') ||
                                        endLocation.includes('Playground');

        console.log('特殊提示条件检查:', {
            startLocation,
            endLocation,
            isStartNearLibrary,
            isEndNearWestPlayground,
            shouldShow: isStartNearLibrary && isEndNearWestPlayground
        });

        // 检查是否有起点和终点
        if (startLocation && endLocation) {
            return true;
        }

        return isStartNearLibrary && isEndNearWestPlayground;
    },



    // 显示路径确认对话框
    showRouteConfirmDialog(result) {
        console.log('显示路径确认对话框');

        try {
            const confirmModal = document.getElementById('route-report-confirm-modal');
            if (!confirmModal) {
                console.error('路径确认对话框未找到');
                return;
            }

            // 更新确认对话框中的信息
            const startLocationElement = document.getElementById('confirm-start-location');
            const endLocationElement = document.getElementById('confirm-end-location');
            const distanceElement = document.getElementById('confirm-distance');
            const timeElement = document.getElementById('confirm-time');

            // 获取起点位置名称
            let startLocationText = this.lastResolvedStartBuilding;
            if (!startLocationText && this.startPoint) {
                const nearestStartBuilding = this.findNearestBuilding(this.startPoint);
                startLocationText = nearestStartBuilding ? nearestStartBuilding.name : null;
            }
            startLocationText = startLocationText || '未知位置';

            // 获取终点位置名称
            let endLocationText = this.lastResolvedEndBuilding;
            if (!endLocationText && this.endPoint) {
                const nearestEndBuilding = this.findNearestBuilding(this.endPoint);
                endLocationText = nearestEndBuilding ? nearestEndBuilding.name : null;
            }
            endLocationText = endLocationText || '未知位置';

            console.log('确认对话框位置信息:', {
                startLocationText,
                endLocationText,
                lastResolvedStartBuilding: this.lastResolvedStartBuilding,
                lastResolvedEndBuilding: this.lastResolvedEndBuilding
            });

            if (startLocationElement) {
                startLocationElement.textContent = startLocationText;
            }

            if (endLocationElement) {
                endLocationElement.textContent = endLocationText;
            }

            if (distanceElement) {
                distanceElement.textContent = result.distance || '--';
            }

            if (timeElement) {
                timeElement.textContent = result.time || '--';
            }

            // 显示对话框
            confirmModal.style.display = 'flex';

            // 绑定确认对话框事件（如果还没有绑定）
            this.bindRouteConfirmEvents();

        } catch (error) {
            console.error('显示路径确认对话框失败:', error);
        }
    },

    // 绑定路径确认对话框事件
    bindRouteConfirmEvents() {
        // 避免重复绑定
        if (this.confirmEventsbound) {
            return;
        }

        const confirmModal = document.getElementById('route-report-confirm-modal');
        const reportModal = document.getElementById('route-report-modal');
        const generateBtn = document.getElementById('route-report-generate');
        const cancelBtn = document.getElementById('route-report-cancel');
        const confirmCloseBtn = document.getElementById('route-report-confirm-close');
        const reportCloseBtn = document.getElementById('route-report-close');

        // 生成报告按钮
        if (generateBtn) {
            generateBtn.addEventListener('click', () => {
                this.generateRouteReport();
                confirmModal.style.display = 'none';
            });
        }

        // 取消按钮
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                confirmModal.style.display = 'none';
                this.pendingRouteResult = null;
            });
        }

        // 确认对话框关闭按钮
        if (confirmCloseBtn) {
            confirmCloseBtn.addEventListener('click', () => {
                confirmModal.style.display = 'none';
                this.pendingRouteResult = null;
            });
        }

        // 报告对话框关闭按钮
        if (reportCloseBtn) {
            reportCloseBtn.addEventListener('click', () => {
                reportModal.style.display = 'none';
            });
        }

        // 点击遮罩层关闭对话框
        if (confirmModal) {
            confirmModal.addEventListener('click', (e) => {
                if (e.target === confirmModal) {
                    confirmModal.style.display = 'none';
                    this.pendingRouteResult = null;
                }
            });
        }

        if (reportModal) {
            reportModal.addEventListener('click', (e) => {
                if (e.target === reportModal) {
                    reportModal.style.display = 'none';
                }
            });
        }

        this.confirmEventsbound = true;
        console.log('路径确认对话框事件绑定完成');
    },

    // 生成路径报告
    generateRouteReport() {
        console.log('生成路径报告');

        try {
            if (!this.pendingRouteResult) {
                console.error('没有待生成的路径结果');
                return;
            }

            const result = this.pendingRouteResult;
            const reportModal = document.getElementById('route-report-modal');
            const reportDisplay = document.getElementById('route-report-display');

            if (!reportModal || !reportDisplay) {
                console.error('路径报告模态框未找到');
                return;
            }

            // 生成报告HTML内容
            const reportHTML = this.generateRouteReportHTML(result);
            reportDisplay.innerHTML = reportHTML;

            // 绑定保存按钮事件
            this.bindSaveReportEvent(result);

            // 显示报告模态框
            reportModal.style.display = 'flex';

            console.log('路径报告已生成并显示');

        } catch (error) {
            console.error('生成路径报告失败:', error);
        }
    },

    // 生成路径报告HTML内容
    generateRouteReportHTML(result) {
        // 调试信息：检查建筑物名称变量
        console.log('生成路径报告时的建筑物名称:', {
            lastResolvedStartBuilding: this.lastResolvedStartBuilding,
            lastResolvedEndBuilding: this.lastResolvedEndBuilding,
            startPoint: this.startPoint,
            endPoint: this.endPoint
        });

        // 获取起点和终点位置名称，优先使用已解析的建筑物名称
        let startLocation = this.lastResolvedStartBuilding;
        let endLocation = this.lastResolvedEndBuilding;

        // 如果没有已解析的建筑物名称，尝试查找最近的建筑物
        if (!startLocation && this.startPoint) {
            const nearestStartBuilding = this.findNearestBuilding(this.startPoint);
            startLocation = nearestStartBuilding ? nearestStartBuilding.name : null;
            console.log('查找起点最近建筑物:', startLocation);
        }

        if (!endLocation && this.endPoint) {
            const nearestEndBuilding = this.findNearestBuilding(this.endPoint);
            endLocation = nearestEndBuilding ? nearestEndBuilding.name : null;
            console.log('查找终点最近建筑物:', endLocation);
        }

        // 如果仍然没有找到，使用默认值
        startLocation = startLocation || t('common.unknown');
        endLocation = endLocation || t('common.unknown');

        console.log('最终使用的位置名称:', { startLocation, endLocation });

        // 生成路径提示信息
        const routeNotice = this.generateRouteNotice(startLocation, endLocation);

        const transportMode = this.getSelectedTransportMode();
        const routeType = this.getSelectedRouteType();
        const segments = this.currentRoute && this.currentRoute.segments ? this.currentRoute.segments.length : 1;

        return `
            <div class="route-summary">
                <div class="route-summary-header">
                    <h4 class="route-title">🗺️ ${t('report.title')}</h4>
                    <div class="route-status">
                        <span class="status-indicator success"></span>
                        <span class="status-text">${t('report.success')}</span>
                    </div>
                </div>

                <div class="route-main-info">
                    <div class="route-distance-card">
                        <div class="info-icon">📏</div>
                        <div class="info-content">
                            <div class="info-label">${t('route.distance')}</div>
                            <div class="info-value primary">${result.distance || '--'}</div>
                        </div>
                    </div>

                    <div class="route-time-card">
                        <div class="info-icon">⏱️</div>
                        <div class="info-content">
                            <div class="info-label">${t('route.time')}</div>
                            <div class="info-value primary">${result.time || '--'}</div>
                        </div>
                    </div>
                </div>

                <div class="route-details">
                    <div class="detail-item">
                        <span class="detail-icon">📍</span>
                        <span class="detail-label">${t('report.startLocation')}</span>
                        <span class="detail-value">${startLocation}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-icon">🎯</span>
                        <span class="detail-label">${t('report.endLocation')}</span>
                        <span class="detail-value">${endLocation}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-icon">🛤️</span>
                        <span class="detail-label">${t('report.segments')}</span>
                        <span class="detail-value">${segments}${t('common.segment')}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-icon">🚶</span>
                        <span class="detail-label">${t('route.transportMode')}</span>
                        <span class="detail-value">${transportMode}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-icon">⚡</span>
                        <span class="detail-label">${t('route.routeType')}</span>
                        <span class="detail-value">${routeType}</span>
                    </div>
                </div>

                ${routeNotice ? `
                <div class="route-notice">
                    <div class="notice-icon">⚠️</div>
                    <div class="notice-content">
                        <div class="notice-title">${t('route.notice')}</div>
                        <div class="notice-text">${routeNotice}</div>
                    </div>
                </div>
                ` : ''}
            </div>


        `;
    },

    // 生成路径提示信息
    generateRouteNotice(startLocation, endLocation) {
        console.log('生成路径提示信息:', { startLocation, endLocation });

        // 如果没有识别到建筑物，尝试生成通用提示
        if (startLocation === '未知位置' && endLocation === '未知位置') {
            console.log('起点和终点都未识别到建筑物，不生成提示');
            return null;
        }

        // 如果只有一个位置未知，也生成提示
        if (startLocation === '未知位置' || endLocation === '未知位置') {
            console.log('部分位置未识别到建筑物，生成通用提示');
            return '路径规划完成，请注意出行安全！';
        }

        try {
            // 定义需要特殊提示的建筑物和区域
            const specialNotices = {
                // 图书馆相关
                '图书馆': {
                    keywords: ['图书馆', '图书', '阅览'],
                    notice: '图书馆'
                },
                // 操场相关
                '西操场': {
                    keywords: ['西操场', '操场', '运动场', '体育场'],
                    notice: '西操场'
                },
                '东操场': {
                    keywords: ['东操场'],
                    notice: '东操场'
                },
                // 食堂相关
                '食堂': {
                    keywords: ['食堂', '餐厅', '饭堂'],
                    notice: '食堂'
                },
                // 宿舍相关
                '宿舍': {
                    keywords: ['宿舍', '公寓', '学生公寓'],
                    notice: '宿舍区'
                },
                // 教学楼相关
                '教学楼': {
                    keywords: ['教学楼', '教学', '实验楼'],
                    notice: '教学区'
                }
            };

            // 匹配起点建筑物类型
            let startType = null;
            let endType = null;

            for (const config of Object.values(specialNotices)) {
                // 检查起点
                if (config.keywords.some(keyword => startLocation.includes(keyword))) {
                    startType = config.notice;
                    console.log(`起点匹配到类型: ${startLocation} -> ${startType}`);
                }
                // 检查终点
                if (config.keywords.some(keyword => endLocation.includes(keyword))) {
                    endType = config.notice;
                    console.log(`终点匹配到类型: ${endLocation} -> ${endType}`);
                }
            }

            console.log('建筑物类型匹配结果:', { startType, endType });

            // 生成特定的提示信息
            if (startType && endType) {
                console.log('检查特殊组合提示:', { startType, endType });

                // 特殊组合提示
                if ((startType === '图书馆' && endType === '西操场') ||
                    (startType === '西操场' && endType === '图书馆')) {
                    const notice = '您将从图书馆附近到达西操场附近，请注意交通车辆！';
                    console.log('生成图书馆-西操场特殊提示:', notice);
                    return notice;
                }

                if ((startType === '图书馆' && endType === '东操场') ||
                    (startType === '东操场' && endType === '图书馆')) {
                    const notice = '您将从图书馆附近到达东操场附近，请注意交通车辆！';
                    console.log('生成图书馆-东操场特殊提示:', notice);
                    return notice;
                }

                if (startType === '宿舍区' && endType === '教学区') {
                    const notice = '您将从宿舍区到达教学区，建议提前出发以免迟到！';
                    console.log('生成宿舍-教学区特殊提示:', notice);
                    return notice;
                }

                if (startType === '教学区' && endType === '食堂') {
                    const notice = '您将从教学区到达食堂，用餐高峰期请注意人流密集！';
                    console.log('生成教学区-食堂特殊提示:', notice);
                    return notice;
                }

                // 通用提示
                const notice = `您将从${startType}附近到达${endType}附近，请注意安全！`;
                console.log('生成通用提示:', notice);
                return notice;
            } else if (startType || endType) {
                // 单一特殊位置提示
                const specialLocation = startType || endType;
                const notice = `路径经过${specialLocation}附近，请注意周边环境！`;
                console.log('生成单一位置提示:', notice);
                return notice;
            }

            // 没有特殊提示
            console.log('没有生成特殊提示');
            return null;

        } catch (error) {
            console.error('生成路径提示信息失败:', error);
            return null;
        }
    },

    // 获取选择的路径类型
    getSelectedRouteType() {
        const routeTypeSelect = document.getElementById('route-type');
        if (routeTypeSelect) {
            const selectedOption = routeTypeSelect.options[routeTypeSelect.selectedIndex];
            return selectedOption ? selectedOption.text : '最短路径';
        }
        return '最短路径';
    },

    // 获取选择的交通方式
    getSelectedTransportMode() {
        const transportModeSelect = document.getElementById('transport-mode');
        if (transportModeSelect) {
            const selectedOption = transportModeSelect.options[transportModeSelect.selectedIndex];
            return selectedOption ? selectedOption.text : '步行';
        }
        return '步行';
    },

    // 显示消息（简单实现）
    showMessage(message, type = 'info') {
        console.log(`路径规划消息 [${type}]: ${message}`);

        // 这里可以实现更复杂的用户提示功能
        // 暂时使用console.log输出
        if (type === 'error') {
            console.error(message);
        } else if (type === 'warning') {
            console.warn(message);
        } else {
            console.info(message);
        }
    },

    // 计算两点间距离（米）
    calculateDistance(point1, point2) {
        // 将投影坐标转换为经纬度坐标
        const lonLat1 = ol.proj.toLonLat(point1);
        const lonLat2 = ol.proj.toLonLat(point2);

        // 使用OpenLayers的getDistance函数计算球面距离（需要经纬度坐标）
        const distance = ol.sphere.getDistance(lonLat1, lonLat2);
        return distance;
    },

    // 计算预估时间（秒）
    calculateEstimatedTime(distance) {
        // 获取当前选择的交通方式
        const transportMode = this.getSelectedTransportMode();

        let speed; // 公里/小时
        if (transportMode === '骑行') {
            speed = CONFIG.routing.cyclingSpeed; // 17 公里/小时
        } else {
            speed = CONFIG.routing.walkingSpeed; // 7 公里/小时
        }

        // 转换为米/秒：公里/小时 * 1000 / 3600
        const speedMeterPerSecond = (speed * 1000) / 3600;

        // 计算时间（秒）
        const timeInSeconds = distance / speedMeterPerSecond;

        console.log(`时间计算: 距离${distance.toFixed(0)}米, 交通方式${transportMode}, 速度${speed}km/h, 预计时间${timeInSeconds.toFixed(0)}秒`);

        return timeInSeconds;
    },

    // 创建路径几何（直线）
    createRouteGeometry(startPoint, endPoint) {
        // 创建直线几何
        const lineGeometry = new ol.geom.LineString([startPoint, endPoint]);
        return lineGeometry;
    },

    // 显示路径在地图上
    displayRoute(geometry) {
        console.log('在地图上显示路径');

        try {
            // 清除之前的路径
            if (this.routeLayer) {
                this.routeLayer.getSource().clear();
            }

            // 创建路径要素
            const routeFeature = new ol.Feature({
                geometry: geometry,
                type: 'route'
            });

            // 设置路径样式（使用配置中的颜色）
            const routeStyle = new ol.style.Style({
                stroke: new ol.style.Stroke({
                    color: CONFIG.routing.routeColor,
                    width: CONFIG.routing.routeWidth,
                    lineDash: [10, 5]
                })
            });

            routeFeature.setStyle(routeStyle);

            // 添加到路径图层
            this.routeLayer.getSource().addFeature(routeFeature);

            console.log('路径已显示在地图上');

        } catch (error) {
            console.error('显示路径失败:', error);
        }
    },

    // 格式化距离显示
    formatDistance(distance) {
        if (distance < 1000) {
            return `${Math.round(distance)}米`;
        } else {
            return `${(distance / 1000).toFixed(1)}公里`;
        }
    },

    // 格式化时间显示
    formatTime(timeInSeconds) {
        const minutes = Math.round(timeInSeconds / 60);
        if (minutes < 60) {
            return `约${minutes}分钟`;
        } else {
            const hours = Math.floor(minutes / 60);
            const remainingMinutes = minutes % 60;
            return `约${hours}小时${remainingMinutes}分钟`;
        }
    },

    // 从输入框解析坐标
    parseCoordinatesFromInputs() {
        console.log('从输入框解析坐标');

        try {
            // 解析起点（只有在起点未设置时才解析）
            if (!this.startPoint && this.startInput && this.startInput.value.trim()) {
                const startValue = this.startInput.value.trim();

                // 尝试解析坐标
                const startCoord = this.parseCoordinateString(startValue);
                if (startCoord) {
                    this.startPoint = startCoord;
                    this.displayStartMarker(startCoord);
                    console.log('从输入框解析起点坐标成功:', startCoord);
                } else {
                    // 尝试解析建筑物名称（只有在没有已解析的建筑物名称时才重新解析）
                    if (!this.lastResolvedStartBuilding) {
                        this.parseLocationInput(startValue, 'start');
                    } else {
                        console.log('起点建筑物名称已存在，跳过重新解析:', this.lastResolvedStartBuilding);
                    }
                }
            }

            // 解析终点（只有在终点未设置时才解析）
            if (!this.endPoint && this.endInput && this.endInput.value.trim()) {
                const endValue = this.endInput.value.trim();

                // 尝试解析坐标
                const endCoord = this.parseCoordinateString(endValue);
                if (endCoord) {
                    this.endPoint = endCoord;
                    this.displayEndMarker(endCoord);
                    console.log('从输入框解析终点坐标成功:', endCoord);
                } else {
                    // 尝试解析建筑物名称（只有在没有已解析的建筑物名称时才重新解析）
                    if (!this.lastResolvedEndBuilding) {
                        this.parseLocationInput(endValue, 'end');
                    } else {
                        console.log('终点建筑物名称已存在，跳过重新解析:', this.lastResolvedEndBuilding);
                    }
                }
            }

        } catch (error) {
            console.error('解析输入框坐标失败:', error);
        }
    },

    // 解析坐标字符串
    parseCoordinateString(coordStr) {
        try {
            // 支持多种格式：
            // "31.970000, 120.900000" (纬度,经度)
            // "120.900000, 31.970000" (经度,纬度)
            // "31.970000,120.900000" (无空格)

            const parts = coordStr.split(',').map(part => part.trim());
            if (parts.length !== 2) {
                console.warn('坐标格式错误，应为：纬度,经度 或 经度,纬度');
                return null;
            }

            const num1 = parseFloat(parts[0]);
            const num2 = parseFloat(parts[1]);

            if (isNaN(num1) || isNaN(num2)) {
                console.warn('坐标数值无效');
                return null;
            }

            // 判断是纬度,经度还是经度,纬度格式
            // 纬度范围：-90到90，经度范围：-180到180
            let lat, lon;

            if (Math.abs(num1) <= 90 && Math.abs(num2) <= 180) {
                // 假设是纬度,经度格式
                lat = num1;
                lon = num2;
            } else if (Math.abs(num2) <= 90 && Math.abs(num1) <= 180) {
                // 假设是经度,纬度格式
                lat = num2;
                lon = num1;
            } else {
                console.warn('坐标值超出有效范围');
                return null;
            }

            // 转换为投影坐标
            const projectedCoord = ol.proj.fromLonLat([lon, lat]);
            return projectedCoord;

        } catch (error) {
            console.error('解析坐标字符串失败:', error);
            return null;
        }
    },

    // 获取模块状态
    getModuleState() {
        return {
            isInitialized: this.isInitialized,
            isPanelVisible: this.panel && this.panel.style.display !== 'none',
            hasStartPoint: !!this.startPoint,
            hasEndPoint: !!this.endPoint,
            hasRoute: !!this.currentRoute
        };
    },

    // 销毁模块
    destroy() {
        console.log('销毁路径规划模块');

        // 隐藏面板
        this.hidePanel();

        // 清除路径和标记
        this.clearRoute();

        // 移除图层
        if (this.routeLayer) {
            MapModule.removeLayer(this.routeLayer);
        }
        if (this.startPointLayer) {
            MapModule.removeLayer(this.startPointLayer);
        }
        if (this.endPointLayer) {
            MapModule.removeLayer(this.endPointLayer);
        }

        // 清空数据
        this.startPoint = null;
        this.endPoint = null;
        this.currentRoute = null;
        this.isInitialized = false;

        // 清空DOM引用
        this.panel = null;
        this.panelClose = null;
        this.routeLayer = null;
        this.startPointLayer = null;
        this.endPointLayer = null;
        this.startInput = null;
        this.endInput = null;
        this.startClearBtn = null;
        this.endClearBtn = null;
        this.calculateBtn = null;
        this.clearBtn = null;
        this.resultPanel = null;
        this.distanceValue = null;
        this.timeValue = null;

        console.log('路径规划模块已销毁');
    },

    // 导出路径
    exportRoute() {
        console.log('导出路径');

        if (!this.currentRoute) {
            this.showMessage('没有可导出的路径', 'warning');
            return;
        }

        try {
            // 构建导出数据
            const exportData = {
                startPoint: {
                    coordinate: ol.proj.toLonLat(this.currentRoute.startPoint),
                    name: '起点'
                },
                endPoint: {
                    coordinate: ol.proj.toLonLat(this.currentRoute.endPoint),
                    name: '终点'
                },
                distance: this.currentRoute.distance,
                estimatedTime: this.currentRoute.estimatedTime,
                routeType: this.getSelectedRouteType(),
                transportMode: this.getSelectedTransportMode(),
                segments: this.currentRoute.segments || [],
                exportTime: new Date().toISOString()
            };

            // 转换为JSON字符串
            const jsonData = JSON.stringify(exportData, null, 2);

            // 创建下载链接
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `route_${new Date().getTime()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            URL.revokeObjectURL(url);

            this.showMessage('路径已导出', 'success');
            console.log('路径导出成功');

        } catch (error) {
            console.error('导出路径失败:', error);
            this.showMessage('导出路径失败', 'error');
        }
    },

    // 分享路径
    shareRoute() {
        console.log('分享路径');

        if (!this.currentRoute) {
            this.showMessage('没有可分享的路径', 'warning');
            return;
        }

        try {
            // 构建分享URL
            const startLonLat = ol.proj.toLonLat(this.currentRoute.startPoint);
            const endLonLat = ol.proj.toLonLat(this.currentRoute.endPoint);

            const shareUrl = `${window.location.origin}${window.location.pathname}?` +
                `start=${startLonLat[1].toFixed(6)},${startLonLat[0].toFixed(6)}&` +
                `end=${endLonLat[1].toFixed(6)},${endLonLat[0].toFixed(6)}&` +
                `type=${this.getSelectedRouteType()}&` +
                `mode=${this.getSelectedTransportMode()}`;

            // 尝试使用Web Share API
            if (navigator.share) {
                navigator.share({
                    title: '智慧校园路径规划',
                    text: `从${this.formatDistance(this.currentRoute.distance)}的路径，预计${this.formatTime(this.currentRoute.estimatedTime)}`,
                    url: shareUrl
                }).then(() => {
                    console.log('路径分享成功');
                }).catch((error) => {
                    console.log('分享取消或失败:', error);
                    this.fallbackShare(shareUrl);
                });
            } else {
                // 回退到复制链接
                this.fallbackShare(shareUrl);
            }

        } catch (error) {
            console.error('分享路径失败:', error);
            this.showMessage('分享路径失败', 'error');
        }
    },

    // 回退分享方法（复制到剪贴板）
    fallbackShare(url) {
        try {
            // 尝试复制到剪贴板
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(() => {
                    this.showMessage('路径链接已复制到剪贴板', 'success');
                }).catch(() => {
                    this.showShareDialog(url);
                });
            } else {
                this.showShareDialog(url);
            }
        } catch (error) {
            console.error('复制链接失败:', error);
            this.showShareDialog(url);
        }
    },

    // 显示分享对话框
    showShareDialog(url) {
        // 简单的分享对话框实现
        const message = `请复制以下链接分享路径：\n${url}`;

        // 创建临时文本区域用于复制
        const textArea = document.createElement('textarea');
        textArea.value = url;
        document.body.appendChild(textArea);
        textArea.select();

        try {
            document.execCommand('copy');
            this.showMessage('路径链接已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            alert(message);
        }

        document.body.removeChild(textArea);
    },

    // 加载分享的路径
    loadSharedRoute() {
        console.log('检查URL参数，加载分享的路径');

        try {
            const urlParams = new URLSearchParams(window.location.search);
            const startParam = urlParams.get('start');
            const endParam = urlParams.get('end');
            const typeParam = urlParams.get('type');
            const modeParam = urlParams.get('mode');

            if (startParam && endParam) {
                console.log('发现分享的路径参数');

                // 解析起点坐标
                const startCoords = startParam.split(',').map(s => parseFloat(s.trim()));
                const endCoords = endParam.split(',').map(s => parseFloat(s.trim()));

                if (startCoords.length === 2 && endCoords.length === 2 &&
                    !isNaN(startCoords[0]) && !isNaN(startCoords[1]) &&
                    !isNaN(endCoords[0]) && !isNaN(endCoords[1])) {

                    // 设置路径类型和交通方式
                    if (typeParam) {
                        const routeTypeSelect = document.getElementById('route-type');
                        if (routeTypeSelect) {
                            for (let option of routeTypeSelect.options) {
                                if (option.text === typeParam) {
                                    routeTypeSelect.selectedIndex = option.index;
                                    break;
                                }
                            }
                        }
                    }

                    if (modeParam) {
                        const transportModeSelect = document.getElementById('transport-mode');
                        if (transportModeSelect) {
                            for (let option of transportModeSelect.options) {
                                if (option.text === modeParam) {
                                    transportModeSelect.selectedIndex = option.index;
                                    break;
                                }
                            }
                        }
                    }

                    // 延迟设置坐标，确保界面已初始化
                    setTimeout(() => {
                        // 设置起点和终点
                        this.setLocationFromCoordinates(startCoords, 'start');
                        this.setLocationFromCoordinates(endCoords, 'end');

                        // 更新输入框
                        if (this.startInput) {
                            this.startInput.value = `${startCoords[0]}, ${startCoords[1]}`;
                        }
                        if (this.endInput) {
                            this.endInput.value = `${endCoords[0]}, ${endCoords[1]}`;
                        }

                        // 分享路径已加载，用户需要手动点击"计算路径"按钮
                        console.log('分享路径已加载，请点击"计算路径"按钮进行路径计算');

                        console.log('分享的路径已加载');
                        this.showMessage('已加载分享的路径', 'success');

                    }, 1000);
                }
            }

        } catch (error) {
            console.error('加载分享路径失败:', error);
        }
    },

    // 建筑物信息显示已统一到SearchModule

    // 高亮建筑物（带闪烁动画效果）
    highlightBuildingWithAnimation(building) {
        if (!this.highlightLayer || !building.geometry) return;

        console.log('高亮建筑物（带闪烁动画）:', building.name);

        // 先清除之前的高亮（带渐隐动画）
        this.clearHighlightWithAnimation(() => {
            // 渐隐完成后，添加新的高亮
            this.addHighlightFeature(building);
        });
    },

    // 添加高亮要素（带渐显动画）
    addHighlightFeature(building) {
        if (!this.highlightLayer || !building.geometry) return;

        try {
            console.log('添加高亮要素:', building.name);

            // 创建高亮要素
            let feature;
            if (building.feature) {
                // 如果已有要素，克隆它
                feature = building.feature.clone();
            } else {
                // 否则从几何数据创建新要素
                const format = new ol.format.GeoJSON();
                const geojson = {
                    type: 'Feature',
                    geometry: building.geometry,
                    properties: building.properties || {}
                };
                feature = format.readFeature(geojson, {
                    dataProjection: 'EPSG:4326',
                    featureProjection: 'EPSG:3857'
                });
            }

            // 保存当前高亮要素引用
            this.currentHighlightFeature = feature;

            // 添加到高亮图层（先设置透明度为0）
            this.highlightLayer.getSource().addFeature(feature);
            this.highlightLayer.setOpacity(0);

            // 渐显动画
            this.animateHighlightFadeIn();

            // 启动闪烁动画
            this.startHighlightPulseAnimation();

            console.log('建筑物高亮成功（带动画）:', building.name);

        } catch (error) {
            console.error('高亮建筑物失败:', error);
        }
    },

    // 渐显动画
    animateHighlightFadeIn() {
        if (!this.highlightLayer) return;

        const duration = 1000; // 1秒
        const startTime = Date.now();
        const startOpacity = 0;
        const targetOpacity = 0.8;

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeProgress = 1 - Math.pow(1 - progress, 3);
            const currentOpacity = startOpacity + (targetOpacity - startOpacity) * easeProgress;

            if (this.highlightLayer) {
                this.highlightLayer.setOpacity(currentOpacity);
            }

            if (progress < 1) {
                this.highlightAnimationId = requestAnimationFrame(animate);
            } else {
                // 动画完成
                console.log('高亮渐显动画完成');
            }
        };

        animate();
    },

    // 启动高亮闪烁动画
    startHighlightPulseAnimation() {
        if (!this.highlightLayer) return;

        // 清除之前的闪烁动画
        if (this.pulseAnimationId) {
            cancelAnimationFrame(this.pulseAnimationId);
        }

        const duration = 2000; // 2秒一个完整周期
        const startTime = Date.now();
        const baseOpacity = 0.8;
        const pulseIntensity = 0.2; // 闪烁强度

        const animatePulse = () => {
            const elapsed = (Date.now() - startTime) % duration;
            const progress = elapsed / duration;
            
            // 使用正弦函数创建平滑的闪烁效果
            const pulseEffect = Math.sin(progress * Math.PI * 2);
            const currentOpacity = baseOpacity + pulseEffect * pulseIntensity;
            
            if (this.highlightLayer) {
                this.highlightLayer.setOpacity(currentOpacity);
            }
            
            this.pulseAnimationId = requestAnimationFrame(animatePulse);
        };
        
        this.pulseAnimationId = requestAnimationFrame(animatePulse);
        console.log('启动高亮闪烁动画');
    },

    // 清除高亮（带渐隐动画）
    clearHighlightWithAnimation(callback) {
        if (!this.highlightLayer || !this.highlightLayer.getSource().getFeatures().length) {
            if (callback) callback();
            return;
        }

        console.log('清除高亮（带渐隐动画）');

        // 停止闪烁动画
        if (this.pulseAnimationId) {
            cancelAnimationFrame(this.pulseAnimationId);
            this.pulseAnimationId = null;
        }

        // 渐隐动画
        let opacity = this.highlightLayer.getOpacity();
        const duration = 1000; // 1秒
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeProgress = 1 - Math.pow(1 - progress, 3);
            const currentOpacity = opacity * (1 - easeProgress);

            if (this.highlightLayer) {
                this.highlightLayer.setOpacity(currentOpacity);
            }

            if (progress < 1) {
                this.highlightAnimationId = requestAnimationFrame(animate);
            } else {
                // 动画完成，清除要素
                this.highlightLayer.getSource().clear();
                this.highlightLayer.setOpacity(0.8); // 重置透明度
                this.currentHighlightFeature = null;
                console.log('清除建筑物高亮（带动画）');

                if (callback) callback();
            }
        };

        this.highlightAnimationId = requestAnimationFrame(animate);
    },

    // 绑定保存报告按钮事件
    bindSaveReportEvent(result) {
        const saveBtn = document.getElementById('route-report-save');
        if (!saveBtn) {
            console.warn('保存报告按钮未找到');
            return;
        }

        // 移除之前的事件监听器（避免重复绑定）
        const newSaveBtn = saveBtn.cloneNode(true);
        saveBtn.parentNode.replaceChild(newSaveBtn, saveBtn);

        // 绑定新的事件监听器
        newSaveBtn.addEventListener('click', () => {
            this.saveRouteReportToTxt(result);
        });

        console.log('保存报告按钮事件已绑定');
    },

    // 保存路径报告为txt文件
    saveRouteReportToTxt(result) {
        console.log('保存路径报告为txt文件');

        try {
            // 生成txt格式的报告内容
            const txtContent = this.generateRouteReportTxt(result);

            // 创建Blob对象
            const blob = new Blob([txtContent], { type: 'text/plain;charset=utf-8' });

            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;

            // 生成文件名（包含时间戳）
            const now = new Date();
            const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_');
            link.download = `智慧校园路径规划报告_${timestamp}.txt`;

            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 释放URL对象
            window.URL.revokeObjectURL(url);

            console.log('路径报告txt文件下载已触发');
            this.showMessage('路径报告已保存为txt文件', 'success');

        } catch (error) {
            console.error('保存路径报告失败:', error);
            this.showMessage('保存路径报告失败', 'error');
        }
    },

    // 生成txt格式的路径报告内容
    generateRouteReportTxt(result) {
        // 获取起点和终点位置名称
        let startLocation = this.lastResolvedStartBuilding;
        let endLocation = this.lastResolvedEndBuilding;

        // 如果没有已解析的建筑物名称，尝试查找最近的建筑物
        if (!startLocation && this.startPoint) {
            const nearestStartBuilding = this.findNearestBuilding(this.startPoint);
            startLocation = nearestStartBuilding ? nearestStartBuilding.name : null;
        }

        if (!endLocation && this.endPoint) {
            const nearestEndBuilding = this.findNearestBuilding(this.endPoint);
            endLocation = nearestEndBuilding ? nearestEndBuilding.name : null;
        }

        // 使用默认值
        startLocation = startLocation || t('common.unknown');
        endLocation = endLocation || t('common.unknown');

        // 生成路径提示信息
        const routeNotice = this.generateRouteNotice(startLocation, endLocation);
        const transportMode = this.getSelectedTransportMode();
        const routeType = this.getSelectedRouteType();
        const segments = this.currentRoute && this.currentRoute.segments ? this.currentRoute.segments.length : 1;

        // 生成当前时间
        const now = new Date();
        const currentLang = window.I18N ? window.I18N.currentLanguage : 'zh';
        const locale = currentLang === 'en' ? 'en-US' : 'zh-CN';
        const reportTime = now.toLocaleString(locale, {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        // 生成txt内容
        const txtContent = this.generateLocalizedTxtContent(
            reportTime, startLocation, endLocation, result,
            transportMode, routeType, segments, routeNotice
        );

        return txtContent;
    },

    // 生成本地化的TXT内容
    generateLocalizedTxtContent(reportTime, startLocation, endLocation, result, transportMode, routeType, segments, routeNotice) {
        const currentLang = window.I18N ? window.I18N.currentLanguage : 'zh';

        if (currentLang === 'en') {
            // 英文版本
            return `
================================================================================
                        Smart Campus Route Planning Report
================================================================================

Report Generation Time: ${reportTime}
System Version: Smart Campus Map System v1.0

================================================================================
                            Route Planning Results
================================================================================

Route Status: Calculation Successful ✓

Basic Information:
  Start Location: ${startLocation}
  End Location: ${endLocation}
  Total Distance: ${result.distance || '--'}
  Estimated Time: ${result.time || '--'}
  Transport Mode: ${transportMode}
  Route Type: ${routeType}
  Route Segments: ${segments} segment${segments > 1 ? 's' : ''}

================================================================================
                            Detailed Route Information
================================================================================

Route Coordinate Information:
  Start Coordinates: ${this.startPoint ? ol.proj.toLonLat(this.startPoint).map(coord => coord.toFixed(6)).join(', ') : 'Not set'}
  End Coordinates: ${this.endPoint ? ol.proj.toLonLat(this.endPoint).map(coord => coord.toFixed(6)).join(', ') : 'Not set'}

Route Features:
  - Route calculation based on campus map data
  - Considers building distribution and road network
  - Provides optimized walking route suggestions

${routeNotice ? `
================================================================================
                                Special Notice
================================================================================

${routeNotice}

` : ''}
================================================================================
                                Usage Instructions
================================================================================

1. This report is generated based on current campus map data
2. Actual walking time may vary due to personal pace, weather and other factors
3. It is recommended to combine with actual conditions and campus signs for navigation
4. If you have any questions, please consult the campus service center

================================================================================
                                Technical Information
================================================================================

Map Projection: Web Mercator (EPSG:3857)
Data Source: Campus GIS Database
Calculation Engine: OpenLayers Route Planning Algorithm
Report Format: Plain Text Format (UTF-8 Encoding)

================================================================================
                    Nantong University Smart Campus Map System
                              Copyright © 2024
================================================================================
`.trim();
        } else {
            // 中文版本
            return `
================================================================================
                           智慧校园路径规划报告
================================================================================

报告生成时间：${reportTime}
系统版本：智慧校园地图系统 v1.0

================================================================================
                              路径规划结果
================================================================================

路径状态：计算成功 ✓

基本信息：
  起点位置：${startLocation}
  终点位置：${endLocation}
  总距离：${result.distance || '--'}
  预计时间：${result.time || '--'}
  交通方式：${transportMode}
  路径类型：${routeType}
  路径段数：${segments}段

================================================================================
                              路径详细信息
================================================================================

路径坐标信息：
  起点坐标：${this.startPoint ? ol.proj.toLonLat(this.startPoint).map(coord => coord.toFixed(6)).join(', ') : '未设置'}
  终点坐标：${this.endPoint ? ol.proj.toLonLat(this.endPoint).map(coord => coord.toFixed(6)).join(', ') : '未设置'}

路径特征：
  - 路径计算基于校园地图数据
  - 考虑了建筑物分布和道路网络
  - 提供最优化的行走路线建议

${routeNotice ? `
================================================================================
                              特别提示
================================================================================

${routeNotice}

` : ''}
================================================================================
                              使用说明
================================================================================

1. 本报告基于当前校园地图数据生成
2. 实际行走时间可能因个人步速、天气等因素有所差异
3. 建议结合实际情况和校园标识进行导航
4. 如有疑问，请咨询校园服务中心

================================================================================
                              技术信息
================================================================================

地图投影：Web Mercator (EPSG:3857)
数据来源：校园GIS数据库
计算引擎：OpenLayers路径规划算法
报告格式：纯文本格式 (UTF-8编码)

================================================================================
                        南通大学智慧校园地图系统
                           版权所有 © 2024
================================================================================
`.trim();
        }
    }
};

// 初始化已移至统一管理器

// 导出路径规划模块（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports.RouteModule = RouteModule;
}

// 在全局作用域中设置RouteModule
if (typeof window !== 'undefined') {
    window.RouteModule = RouteModule;
}

console.log('路径规划模块已定义');

// ============================================================================
// 天气查询模块 - WeatherModule
// ============================================================================

const WeatherModule = {
    // 模块状态
    isInitialized: false, // 初始化状态
    weatherData: null, // 当前天气数据
    lastUpdateTime: null, // 最后更新时间
    panel: null, // 天气面板元素
    updateTimer: null, // 定时更新器
    isLoading: false, // 是否正在加载
    retryCount: 0, // 重试计数

    // 智能缓存系统
    cache: {
        memoryCache: null, // 内存缓存（会话期间）
        lastMemoryUpdate: null, // 内存缓存最后更新时间
        backgroundUpdateInProgress: false, // 后台更新进行中标志

        // 保存数据到缓存（内存+localStorage）
        saveData(data) {
            const timestamp = Date.now();
            const cacheData = {
                data: data,
                timestamp: timestamp,
                expiry: timestamp + CONFIG.weather.cacheExpiry,
                version: '1.0' // 缓存版本，用于兼容性检查
            };

            // 保存到内存缓存
            this.memoryCache = cacheData;
            this.lastMemoryUpdate = timestamp;

            // 保存到localStorage
            try {
                localStorage.setItem(CONFIG.weather.cacheKey, JSON.stringify(cacheData));
                console.log('天气数据已保存到双层缓存');
            } catch (error) {
                console.warn('localStorage保存失败，仅使用内存缓存:', error);
            }
        },

        // 从缓存获取数据（优先内存，降级localStorage）
        getData() {
            // 1. 优先检查内存缓存
            if (this.memoryCache && this.isValid(this.memoryCache)) {
                console.log('使用内存缓存数据');
                return this.memoryCache;
            }

            // 2. 检查localStorage缓存
            try {
                const stored = localStorage.getItem(CONFIG.weather.cacheKey);
                if (!stored) return null;

                const cacheData = JSON.parse(stored);

                // 检查缓存版本兼容性
                if (!cacheData.version || cacheData.version !== '1.0') {
                    console.log('缓存版本不兼容，清除旧缓存');
                    this.clearStorage();
                    return null;
                }

                if (this.isValid(cacheData)) {
                    // 恢复到内存缓存
                    this.memoryCache = cacheData;
                    this.lastMemoryUpdate = cacheData.timestamp;
                    console.log('从localStorage恢复缓存到内存');
                    return cacheData;
                }

                // 缓存已过期，清除
                this.clearStorage();
                return null;

            } catch (error) {
                console.warn('读取localStorage缓存失败:', error);
                this.clearStorage();
                return null;
            }
        },

        // 检查缓存是否有效
        isValid(cacheData) {
            if (!cacheData || !cacheData.data || !cacheData.timestamp) return false;
            return Date.now() < cacheData.expiry;
        },

        // 检查数据是否陈旧（需要后台更新）
        isStale(cacheData) {
            if (!cacheData || !cacheData.timestamp) return true;
            return Date.now() - cacheData.timestamp > CONFIG.weather.staleThreshold;
        },

        // 清除localStorage缓存
        clearStorage() {
            try {
                localStorage.removeItem(CONFIG.weather.cacheKey);
                console.log('已清除localStorage天气缓存');
            } catch (error) {
                console.warn('清除localStorage缓存失败:', error);
            }
        },

        // 清除所有缓存
        clearAll() {
            this.memoryCache = null;
            this.lastMemoryUpdate = null;
            this.backgroundUpdateInProgress = false;
            this.clearStorage();
            console.log('已清除所有天气缓存');
        },

        // 获取缓存统计信息
        getStats() {
            const memorySize = this.memoryCache ? JSON.stringify(this.memoryCache).length : 0;
            let storageSize = 0;

            try {
                const stored = localStorage.getItem(CONFIG.weather.cacheKey);
                storageSize = stored ? stored.length : 0;
            } catch (error) {
                // localStorage不可用
            }

            return {
                hasMemoryCache: !!this.memoryCache,
                hasStorageCache: storageSize > 0,
                memorySize: memorySize,
                storageSize: storageSize,
                lastUpdate: this.lastMemoryUpdate,
                backgroundUpdateInProgress: this.backgroundUpdateInProgress
            };
        }
    },

    // 页面可见性管理器
    visibilityManager: {
        isVisible: true, // 当前页面可见状态
        isInitialized: false, // 是否已初始化

        // 初始化页面可见性管理器
        init() {
            if (this.isInitialized) {
                console.log('页面可见性管理器已初始化，跳过重复初始化');
                return;
            }

            // 检查浏览器是否支持Page Visibility API
            if (typeof document.visibilityState === 'undefined') {
                console.warn('Page Visibility API不支持，跳过可见性管理');
                return;
            }

            // 绑定页面可见性变化事件
            document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));

            // 设置初始状态
            this.isVisible = !document.hidden;
            this.isInitialized = true;

            console.log('页面可见性管理器已初始化，当前状态:', this.isVisible ? '可见' : '隐藏');
        },

        // 处理页面可见性变化
        handleVisibilityChange() {
            const wasVisible = this.isVisible;
            this.isVisible = !document.hidden;

            console.log('页面可见性变化:', wasVisible ? '可见' : '隐藏', '->', this.isVisible ? '可见' : '隐藏');

            // 根据配置决定是否暂停/恢复更新
            if (CONFIG.weather.pauseOnHidden) {
                if (this.isVisible && !wasVisible) {
                    // 页面从隐藏变为可见
                    console.log('页面重新可见，恢复天气自动更新');
                    WeatherModule.resumeAutoUpdate();
                } else if (!this.isVisible && wasVisible) {
                    // 页面从可见变为隐藏
                    console.log('页面隐藏，暂停天气自动更新');
                    WeatherModule.pauseAutoUpdate();
                }
            }
        },

        // 清理页面可见性管理器
        cleanup() {
            if (this.isInitialized) {
                document.removeEventListener('visibilitychange', this.handleVisibilityChange);
                this.isInitialized = false;
                console.log('页面可见性管理器已清理');
            }
        },

        // 获取当前状态
        getStatus() {
            return {
                isVisible: this.isVisible,
                isInitialized: this.isInitialized,
                isSupported: typeof document.visibilityState !== 'undefined'
            };
        }
    },

    // 用户偏好设置管理
    userPreferences: {
        // 加载用户偏好设置
        load() {
            console.log('加载用户偏好设置');

            try {
                const stored = localStorage.getItem(CONFIG.weather.userPreferencesKey);
                if (stored) {
                    const prefs = JSON.parse(stored);

                    // 验证偏好设置的有效性
                    if (typeof prefs.autoUpdateEnabled === 'boolean') {
                        CONFIG.weather.autoUpdateEnabled = prefs.autoUpdateEnabled;
                    }
                    if (typeof prefs.showUpdateStatus === 'boolean') {
                        CONFIG.weather.showUpdateStatus = prefs.showUpdateStatus;
                    }

                    console.log('用户偏好设置已加载:', prefs);
                    return prefs;
                }
            } catch (error) {
                console.warn('加载用户偏好设置失败:', error);
                // 清除损坏的数据
                try {
                    localStorage.removeItem(CONFIG.weather.userPreferencesKey);
                } catch (clearError) {
                    console.warn('清除损坏的偏好设置失败:', clearError);
                }
            }

            // 返回默认设置
            const defaults = this.getDefaults();
            console.log('使用默认偏好设置:', defaults);
            return defaults;
        },

        // 保存用户偏好设置
        save(preferences) {
            console.log('保存用户偏好设置:', preferences);

            try {
                const prefs = { ...this.getDefaults(), ...preferences };

                // 验证数据有效性
                if (typeof prefs.autoUpdateEnabled !== 'boolean') {
                    prefs.autoUpdateEnabled = true;
                }
                if (typeof prefs.showUpdateStatus !== 'boolean') {
                    prefs.showUpdateStatus = true;
                }

                // 保存到localStorage
                localStorage.setItem(CONFIG.weather.userPreferencesKey, JSON.stringify(prefs));

                // 同步到CONFIG（注意：CONFIG被冻结，需要特殊处理）
                // 这里我们直接修改CONFIG的属性值
                if (CONFIG.weather.hasOwnProperty('autoUpdateEnabled')) {
                    CONFIG.weather.autoUpdateEnabled = prefs.autoUpdateEnabled;
                }
                if (CONFIG.weather.hasOwnProperty('showUpdateStatus')) {
                    CONFIG.weather.showUpdateStatus = prefs.showUpdateStatus;
                }

                console.log('用户偏好设置已保存:', prefs);
                return true;

            } catch (error) {
                console.error('保存用户偏好设置失败:', error);
                return false;
            }
        },

        // 获取默认偏好设置
        getDefaults() {
            return {
                autoUpdateEnabled: true,
                showUpdateStatus: true
            };
        },

        // 切换自动更新状态
        toggleAutoUpdate() {
            const currentState = CONFIG.weather.autoUpdateEnabled;
            const newState = !currentState;

            console.log(`切换自动更新状态: ${currentState} -> ${newState}`);

            // 保存新状态
            const saved = this.save({ autoUpdateEnabled: newState });
            if (!saved) {
                console.error('保存偏好设置失败，无法切换自动更新状态');
                return currentState;
            }

            // 根据新状态启用或禁用自动更新
            if (newState) {
                console.log('启用自动更新');
                WeatherModule.setupAutoUpdate();
            } else {
                console.log('禁用自动更新');
                WeatherModule.pauseAutoUpdate();
            }

            // 更新UI状态
            WeatherModule.updatePanelDisplay();

            return newState;
        },

        // 获取当前偏好设置
        getCurrent() {
            return {
                autoUpdateEnabled: CONFIG.weather.autoUpdateEnabled,
                showUpdateStatus: CONFIG.weather.showUpdateStatus
            };
        },

        // 重置为默认设置
        reset() {
            console.log('重置用户偏好设置为默认值');
            const defaults = this.getDefaults();
            return this.save(defaults);
        }
    },

    // 初始化模块
    init() {
        console.log('初始化天气模块');

        try {
            // 加载用户偏好设置
            this.userPreferences.load();

            // 获取面板元素
            this.panel = document.getElementById('weather-panel');
            if (!this.panel) {
                console.error('天气面板元素未找到');
                return false;
            }

            // 绑定面板事件
            this.bindPanelEvents();

            // 加载缓存数据
            this.loadCachedData();

            // 设置定时更新（根据用户偏好）
            this.setupAutoUpdate();

            // 设置倒计时定时器（如果启用状态显示）
            if (CONFIG.weather.showUpdateStatus) {
                this.setupCountdownTimer();
            }

            // 绑定页面卸载事件
            window.addEventListener('beforeunload', this.cleanup.bind(this));

            this.isInitialized = true;
            console.log('天气模块初始化完成');
            return true;

        } catch (error) {
            console.error('天气模块初始化失败:', error);
            return false;
        }
    },

    // 绑定面板事件
    bindPanelEvents() {
        if (!this.panel) return;

        // 关闭按钮事件
        const closeBtn = this.panel.querySelector('#weather-panel-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hidePanel();
            });
        }

        // 刷新按钮事件
        const refreshBtn = this.panel.querySelector('.weather-refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                console.log('用户点击刷新天气数据');
                this.refreshWeatherData();
            });
        }

        // 重试按钮事件
        const retryBtn = this.panel.querySelector('.weather-retry-btn');
        if (retryBtn) {
            retryBtn.addEventListener('click', () => {
                this.refreshWeatherData();
            });
        }


    },

    // 显示天气面板
    showPanel() {
        console.log('显示天气面板');

        if (!this.panel) {
            console.error('天气面板元素未找到');
            return;
        }

        this.panel.style.display = 'block';

        // 如果没有数据或数据过期，则获取新数据
        if (!this.weatherData || this.needsUpdate()) {
            this.fetchWeatherData();
        } else {
            this.updatePanelDisplay();
        }
    },

    // 隐藏天气面板
    hidePanel() {
        console.log('隐藏天气面板');

        if (this.panel) {
            this.panel.style.display = 'none';
        }

        // 通知面板管理器面板已关闭
        if (typeof PanelManager !== 'undefined') {
            PanelManager.notifyPanelClosed(PanelManager.panelTypes.WEATHER);
        }
    },

    // 获取天气数据（智能缓存策略）
    async fetchWeatherData(forceRefresh = false) {
        if (this.isLoading && !forceRefresh) {
            console.log('天气数据正在加载中，跳过重复请求');
            return;
        }

        console.log('开始获取天气数据', forceRefresh ? '(强制刷新)' : '');
        this.isLoading = true;

        try {
            // 获取缓存数据
            const cachedData = this.cache.getData();

            if (!forceRefresh && cachedData) {
                // 有有效缓存数据
                console.log('使用缓存的天气数据');
                this.weatherData = cachedData.data;
                this.lastUpdateTime = cachedData.timestamp;
                this.updatePanelDisplay();
                this.hideErrorState();

                // 检查是否需要后台更新
                if (this.cache.isStale(cachedData) && !this.cache.backgroundUpdateInProgress) {
                    console.log('数据陈旧，启动后台更新');
                    this.startBackgroundUpdate();
                }

                this.isLoading = false;
                return;
            }

            // 显示加载状态（仅在没有缓存数据时）
            if (!cachedData) {
                this.showLoadingState();
            }

            // 从API获取新数据
            const freshData = await this.fetchWeatherFromAPI();
            this.weatherData = freshData;
            this.lastUpdateTime = Date.now();

            // 保存到智能缓存
            this.cache.saveData(freshData);

            // 更新显示
            this.updatePanelDisplay();
            this.hideErrorState();
            this.retryCount = 0;

            console.log('天气数据获取成功');

        } catch (error) {
            console.error('获取天气数据失败:', error);
            this.handleFetchError(error);
        } finally {
            this.hideLoadingState();
            this.isLoading = false;
        }
    },

    // 启动后台更新
    async startBackgroundUpdate() {
        if (this.cache.backgroundUpdateInProgress) {
            console.log('后台更新已在进行中');
            return;
        }

        this.cache.backgroundUpdateInProgress = true;
        console.log('开始后台更新天气数据');

        try {
            const freshData = await this.fetchWeatherFromAPI();

            // 保存新数据到缓存
            this.cache.saveData(freshData);

            // 如果面板正在显示，静默更新数据
            if (this.panel && this.panel.style.display !== 'none') {
                this.weatherData = freshData;
                this.lastUpdateTime = Date.now();
                this.updatePanelDisplay();
                console.log('后台更新完成，面板已静默更新');
            } else {
                // 仅更新内存数据
                this.weatherData = freshData;
                this.lastUpdateTime = Date.now();
                console.log('后台更新完成，数据已更新');
            }

        } catch (error) {
            console.warn('后台更新失败:', error);
        } finally {
            this.cache.backgroundUpdateInProgress = false;
        }
    },

    // 从API获取天气数据
    async fetchWeatherFromAPI() {
        const { apiUrl, retryAttempts, retryDelay } = CONFIG.weather;

        for (let attempt = 1; attempt <= retryAttempts; attempt++) {
            try {
                console.log(`尝试获取天气数据 (${attempt}/${retryAttempts})`);

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000);

                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: { 'Accept': 'application/json' },
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                return this.transformWeatherData(data);

            } catch (error) {
                console.warn(`天气API调用失败 (尝试 ${attempt}/${retryAttempts}):`, error.message);

                if (attempt === retryAttempts) {
                    throw error;
                }

                await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
            }
        }
    },

    // 转换天气数据格式（适配wttr.in API）
    transformWeatherData(apiData) {
        // wttr.in API数据结构
        const currentCondition = apiData.current_condition?.[0] || {};
        const nearestArea = apiData.nearest_area?.[0] || {};
        const todayWeather = apiData.weather?.[0] || {};

        // 获取中文天气描述
        const weatherDesc = currentCondition.lang_zh?.[0]?.value ||
                           currentCondition.weatherDesc?.[0]?.value || '未知';

        // 处理每小时温度数据
        const hourlyData = todayWeather.hourly || [];
        const temperatureChart = this.processHourlyTemperature(hourlyData);

        return {
            city: nearestArea.areaName?.[0]?.value || CONFIG.weather.city,
            weather: weatherDesc,
            currentTemp: parseInt(currentCondition.temp_C || '0'),
            maxTemp: parseInt(todayWeather.maxtempC || '0'),
            minTemp: parseInt(todayWeather.mintempC || '0'),
            humidity: (currentCondition.humidity || '0') + '%',
            pressure: (currentCondition.pressure || '0') + ' hPa',
            windSpeed: (currentCondition.windspeedKmph || '0') + ' km/h',
            uvIndex: currentCondition.uvIndex || '0',
            icon: this.getWeatherIcon(weatherDesc),
            date: todayWeather.date || new Date().toISOString().split('T')[0],
            updateTime: new Date().toLocaleString('zh-CN'),
            timestamp: Date.now(),
            temperatureChart: temperatureChart
        };
    },

    // 处理每小时温度数据
    processHourlyTemperature(hourlyData) {
        const chartData = [];

        hourlyData.forEach(hour => {
            const time = parseInt(hour.time || '0');
            const temp = parseInt(hour.tempC || '0');
            const timeLabel = this.formatHourTime(time);

            chartData.push({
                time: timeLabel,
                temperature: temp,
                rawTime: time
            });
        });

        return chartData;
    },

    // 格式化小时时间显示
    formatHourTime(time) {
        const hour = Math.floor(time / 100);
        return `${hour.toString().padStart(2, '0')}:00`;
    },

    // 获取天气图标
    getWeatherIcon(weatherDesc) {
        if (!weatherDesc) return '🌤️';

        const desc = weatherDesc.toString().toLowerCase();

        // 晴天相关
        if (desc.includes('晴')) return '☀️';

        // 云相关
        if (desc.includes('多云') || desc.includes('少云')) return '⛅';
        if (desc.includes('阴') || desc.includes('云')) return '☁️';

        // 雨相关
        if (desc.includes('雷') || desc.includes('雷雨')) return '⛈️';
        if (desc.includes('大雨') || desc.includes('暴雨')) return '🌧️';
        if (desc.includes('中雨')) return '🌦️';
        if (desc.includes('小雨') || desc.includes('细雨') || desc.includes('毛毛雨')) return '🌦️';
        if (desc.includes('雨')) return '🌧️';

        // 雪相关
        if (desc.includes('雪')) return '❄️';

        // 雾霾相关
        if (desc.includes('雾') || desc.includes('霾') || desc.includes('霭')) return '🌫️';

        // 风相关
        if (desc.includes('风')) return '💨';

        return '🌤️'; // 默认图标
    },

    // 兼容性方法（保持向后兼容）
    getCachedData() {
        return this.cache.getData();
    },

    saveCachedData(data) {
        this.cache.saveData(data);
    },

    isDataExpired(cacheData) {
        return !this.cache.isValid(cacheData);
    },

    needsUpdate() {
        if (!this.lastUpdateTime) return true;
        return Date.now() - this.lastUpdateTime > CONFIG.weather.updateInterval;
    },

    // 加载缓存数据（使用智能缓存）
    loadCachedData() {
        const cached = this.cache.getData();
        if (cached) {
            this.weatherData = cached.data;
            this.lastUpdateTime = cached.timestamp;
            console.log('已加载智能缓存的天气数据');

            // 检查是否需要后台更新
            if (this.cache.isStale(cached)) {
                console.log('缓存数据陈旧，稍后将进行后台更新');
            }
        }
    },

    // 获取缓存状态信息
    getCacheStatus() {
        const stats = this.cache.getStats();
        const cached = this.cache.getData();

        return {
            ...stats,
            isValid: cached ? this.cache.isValid(cached) : false,
            isStale: cached ? this.cache.isStale(cached) : true,
            dataAge: cached ? Date.now() - cached.timestamp : null,
            expiresIn: cached ? cached.expiry - Date.now() : null
        };
    },

    // 清除所有缓存
    clearCache() {
        this.cache.clearAll();
        this.weatherData = null;
        this.lastUpdateTime = null;
        console.log('天气模块缓存已清除');
    },

    // 设置自动更新
    setupAutoUpdate() {
        console.log('设置天气自动更新定时器');

        // 检查是否启用自动更新
        if (!CONFIG.weather.autoUpdateEnabled) {
            console.log('自动更新已禁用，跳过定时器设置');
            return;
        }

        // 清理现有定时器
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }

        // 初始化页面可见性管理器
        this.visibilityManager.init();

        // 设置定时器
        this.updateTimer = setInterval(() => {
            // 检查页面是否可见和是否需要更新
            if (this.visibilityManager.isVisible && this.needsUpdate()) {
                console.log('定时更新天气数据（页面可见）');
                this.fetchWeatherData();
            } else if (!this.visibilityManager.isVisible) {
                console.log('页面隐藏，跳过定时更新');
            } else {
                console.log('数据仍然新鲜，跳过更新');
            }
        }, CONFIG.weather.updateInterval);

        console.log(`天气自动更新定时器已设置，间隔：${CONFIG.weather.updateInterval / 1000 / 60}分钟`);
    },

    // 暂停自动更新
    pauseAutoUpdate() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
            console.log('天气自动更新已暂停（页面隐藏）');
        } else {
            console.log('天气自动更新定时器未运行，无需暂停');
        }
    },

    // 恢复自动更新
    resumeAutoUpdate() {
        console.log('恢复天气自动更新');

        // 检查是否启用自动更新
        if (!CONFIG.weather.autoUpdateEnabled) {
            console.log('自动更新已禁用，不恢复定时器');
            return;
        }

        // 检查是否需要立即更新
        if (CONFIG.weather.resumeUpdateOnVisible && this.needsUpdate()) {
            console.log('页面重新可见，数据陈旧，立即更新天气数据');
            this.fetchWeatherData();
        } else {
            console.log('页面重新可见，数据仍然新鲜');
        }

        // 重新设置定时器
        this.setupAutoUpdate();
    },

    // 手动刷新天气数据
    refreshWeatherData() {
        console.log('手动刷新天气数据');
        this.retryCount = 0;
        this.fetchWeatherData(true); // 强制刷新，跳过缓存
    },

    // 显示加载状态
    showLoadingState() {
        if (!this.panel) return;

        const loadingEl = this.panel.querySelector('.weather-loading');
        const contentEl = this.panel.querySelector('.weather-content');
        const errorEl = this.panel.querySelector('.weather-error');

        if (loadingEl) loadingEl.style.display = 'flex';
        if (contentEl) contentEl.style.display = 'none';
        if (errorEl) errorEl.style.display = 'none';
    },

    // 隐藏加载状态
    hideLoadingState() {
        if (!this.panel) return;

        const loadingEl = this.panel.querySelector('.weather-loading');
        if (loadingEl) loadingEl.style.display = 'none';
    },

    // 显示错误状态
    showErrorState(message, solution = '') {
        if (!this.panel) return;

        const loadingEl = this.panel.querySelector('.weather-loading');
        const contentEl = this.panel.querySelector('.weather-content');
        const errorEl = this.panel.querySelector('.weather-error');
        const errorText = this.panel.querySelector('.error-text');

        if (loadingEl) loadingEl.style.display = 'none';
        if (contentEl) contentEl.style.display = 'none';
        if (errorEl) errorEl.style.display = 'flex';

        // 更新错误消息，如果有解决方案则显示
        if (errorText) {
            let fullMessage = message || '天气信息获取失败';
            if (solution) {
                fullMessage += `\n\n💡 解决方案：${solution}`;
            }
            errorText.textContent = fullMessage;
        }
    },

    // 隐藏错误状态
    hideErrorState() {
        if (!this.panel) return;

        const errorEl = this.panel.querySelector('.weather-error');
        if (errorEl) errorEl.style.display = 'none';
    },

    // 更新面板显示
    updatePanelDisplay() {
        if (!this.panel || !this.weatherData) return;

        // 显示内容区域
        const contentEl = this.panel.querySelector('.weather-content');
        if (contentEl) contentEl.style.display = 'block';

        // 更新日期
        const dateEl = this.panel.querySelector('.weather-date-text');
        if (dateEl && this.weatherData.date) {
            const date = new Date(this.weatherData.date);
            const formattedDate = date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            dateEl.textContent = formattedDate;
        }

        // 更新主要天气信息
        const iconEl = this.panel.querySelector('.weather-icon');
        const currentTempEl = this.panel.querySelector('.weather-current-temp');
        const tempHighEl = this.panel.querySelector('.temp-high');
        const tempLowEl = this.panel.querySelector('.temp-low');
        const descEl = this.panel.querySelector('.weather-desc');

        if (iconEl) iconEl.textContent = this.weatherData.icon || '🌤️';

        // 处理当前温度显示（兼容不同数据格式）
        const currentTemp = this.weatherData.currentTemp ||
                           (this.weatherData.temperature ? parseInt(this.weatherData.temperature) : 22);
        if (currentTempEl) currentTempEl.textContent = `${currentTemp}°C`;

        // 处理最高最低温度显示
        const maxTemp = this.weatherData.maxTemp || 28;
        const minTemp = this.weatherData.minTemp || 18;
        if (tempHighEl) tempHighEl.textContent = `${maxTemp}°`;
        if (tempLowEl) tempLowEl.textContent = `${minTemp}°`;

        if (descEl) descEl.textContent = this.weatherData.weather || '晴朗';

        // 更新详细信息
        const detailItems = this.panel.querySelectorAll('.weather-item .weather-value');
        if (detailItems.length >= 5) {
            detailItems[0].textContent = this.weatherData.humidity || '65%'; // 湿度
            detailItems[1].textContent = this.weatherData.pressure || '1013 hPa'; // 气压
            detailItems[2].textContent = this.weatherData.windSpeed || '15 km/h'; // 风速
            detailItems[3].textContent = this.weatherData.uvIndex || '3'; // 紫外线
            detailItems[4].textContent = this.getRelativeTime(); // 更新时间
        }

        // 绘制温度图表
        this.drawTemperatureChart();

        // 更新状态指示和控制
        this.updateStatusDisplay();

        console.log('天气面板显示已更新');
    },

    // 更新状态指示和控制
    updateStatusDisplay() {
        if (!this.panel) return;

        // 更新倒计时显示
        this.updateCountdown();
    },

    // 更新倒计时显示
    updateCountdown() {
        if (!CONFIG.weather.showUpdateStatus || !this.lastUpdateTime) return;

        const countdownElement = this.panel.querySelector('#weather-countdown');
        if (!countdownElement) return;

        const nextUpdate = this.lastUpdateTime + CONFIG.weather.updateInterval;
        const remaining = Math.max(0, nextUpdate - Date.now());
        const minutes = Math.ceil(remaining / (60 * 1000));

        if (remaining > 0) {
            countdownElement.textContent = `下次更新：${minutes}分钟后`;
        } else {
            countdownElement.textContent = '准备更新...';
        }
    },





    // 设置倒计时定时器
    setupCountdownTimer() {
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
        }

        this.countdownTimer = setInterval(() => {
            this.updateCountdown();
        }, 60000); // 每分钟更新一次倒计时

        console.log('倒计时定时器已设置');
    },

    // 清理倒计时定时器
    clearCountdownTimer() {
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
            console.log('倒计时定时器已清理');
        }
    },

    // 绘制温度变化图表
    drawTemperatureChart() {
        console.log('开始绘制温度图表');
        console.log('天气数据:', this.weatherData);

        if (!this.weatherData) {
            console.log('没有天气数据，跳过图表绘制');
            return;
        }

        if (!this.weatherData.temperatureChart) {
            console.log('没有温度图表数据，使用默认图表数据');
            console.log('temperatureChart:', this.weatherData.temperatureChart);

            // 使用默认的温度图表数据
            this.weatherData.temperatureChart = CONFIG.weather.defaultData.temperatureChart;
            console.log('已设置默认温度图表数据:', this.weatherData.temperatureChart);
        }

        const canvas = this.panel.querySelector('#temperature-chart');
        if (!canvas) {
            console.log('找不到Canvas元素，跳过图表绘制');
            return;
        }

        console.log('Canvas元素找到，开始绘制图表');

        const ctx = canvas.getContext('2d');
        const chartData = this.weatherData.temperatureChart;

        // 设置画布尺寸
        const width = canvas.width;
        const height = canvas.height;
        const padding = 20;
        const chartWidth = width - padding * 2;
        const chartHeight = height - padding * 2;

        // 清空画布
        ctx.clearRect(0, 0, width, height);

        if (chartData.length === 0) return;

        // 计算温度范围
        const temps = chartData.map(d => d.temperature);
        const minTemp = Math.min(...temps);
        const maxTemp = Math.max(...temps);
        const tempRange = maxTemp - minTemp || 1;

        // 绘制背景
        ctx.fillStyle = 'rgba(116, 185, 255, 0.02)';
        ctx.fillRect(0, 0, width, height);

        // 绘制网格线
        ctx.strokeStyle = 'rgba(116, 185, 255, 0.2)';
        ctx.lineWidth = 1;
        for (let i = 1; i < 4; i++) {
            const y = padding + (chartHeight / 4) * i;
            ctx.beginPath();
            ctx.moveTo(padding, y);
            ctx.lineTo(width - padding, y);
            ctx.stroke();
        }

        // 绘制温度曲线
        ctx.strokeStyle = '#74b9ff';
        ctx.lineWidth = 3;
        ctx.beginPath();

        chartData.forEach((point, index) => {
            const x = padding + (chartWidth / (chartData.length - 1)) * index;
            const y = padding + chartHeight - ((point.temperature - minTemp) / tempRange) * chartHeight;

            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });
        ctx.stroke();

        // 绘制数据点
        ctx.fillStyle = '#0984e3';
        chartData.forEach((point, index) => {
            const x = padding + (chartWidth / (chartData.length - 1)) * index;
            const y = padding + chartHeight - ((point.temperature - minTemp) / tempRange) * chartHeight;

            ctx.beginPath();
            ctx.arc(x, y, 4, 0, 2 * Math.PI);
            ctx.fill();
        });

        // 绘制时间标签
        ctx.fillStyle = '#666666';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';

        chartData.forEach((point, index) => {
            if (index % 2 === 0) { // 只显示偶数索引的时间标签，避免拥挤
                const x = padding + (chartWidth / (chartData.length - 1)) * index;
                ctx.fillText(point.time, x, height - 5);
            }
        });

        // 绘制温度标签
        ctx.fillStyle = '#333333';
        ctx.textAlign = 'left';
        chartData.forEach((point, index) => {
            if (index % 2 === 0) { // 只显示偶数索引的温度标签
                const x = padding + (chartWidth / (chartData.length - 1)) * index;
                const y = padding + chartHeight - ((point.temperature - minTemp) / tempRange) * chartHeight;
                ctx.fillText(`${point.temperature}°`, x + 5, y - 5);
            }
        });
    },

    // 获取相对时间显示
    getRelativeTime() {
        if (!this.lastUpdateTime) return '未知';

        const now = Date.now();
        const diff = now - this.lastUpdateTime;
        const minutes = Math.floor(diff / (1000 * 60));

        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;

        const hours = Math.floor(minutes / 60);
        if (hours < 24) return `${hours}小时前`;

        const days = Math.floor(hours / 24);
        return `${days}天前`;
    },

    // 处理获取错误（智能降级策略）
    handleFetchError(error) {
        this.retryCount++;

        // 1. 优先使用智能缓存数据（即使过期）
        const cached = this.cache.getData();
        if (!cached) {
            // 尝试获取过期的缓存数据
            try {
                const stored = localStorage.getItem(CONFIG.weather.cacheKey);
                if (stored) {
                    const expiredCache = JSON.parse(stored);
                    if (expiredCache && expiredCache.data) {
                        console.log('API失败，使用过期的缓存数据');
                        this.weatherData = expiredCache.data;
                        this.lastUpdateTime = expiredCache.timestamp;
                        this.updatePanelDisplay();
                        showUserMessage('天气数据可能不是最新的（使用缓存数据）', 'warning');
                        return;
                    }
                }
            } catch (cacheError) {
                console.warn('读取过期缓存失败:', cacheError);
            }
        } else {
            console.log('API失败，使用智能缓存数据');
            this.weatherData = cached.data;
            this.lastUpdateTime = cached.timestamp;
            this.updatePanelDisplay();
            showUserMessage('天气数据可能不是最新的', 'warning');
            return;
        }

        // 2. 使用默认数据作为最后降级方案
        console.log('使用默认天气数据');
        this.weatherData = {
            ...CONFIG.weather.defaultData,
            updateTime: '数据不可用',
            timestamp: Date.now()
        };
        this.updatePanelDisplay();

        // 3. 显示详细错误信息和解决方案
        let errorMessage = '天气信息获取失败';
        let solution = '';

        if (error.message.includes('CORS') || error.message.includes('Access-Control-Allow-Origin')) {
            errorMessage = '天气API访问受限';
            solution = '由于浏览器安全策略，从本地文件无法访问外部API。建议使用HTTP服务器运行系统（如：python -m http.server 8000）';
            showUserMessage('💡 提示：请使用HTTP服务器访问系统以获取实时天气数据', 'info');
        } else if (error.message.includes('网络')) {
            errorMessage = '网络连接失败';
            solution = '请检查网络连接状态';
        } else if (error.message.includes('HTTP')) {
            errorMessage = '天气服务暂时不可用';
            solution = '天气API服务可能暂时不可用，请稍后重试';
        } else if (error.message.includes('timeout') || error.message.includes('abort')) {
            errorMessage = '请求超时';
            solution = '网络响应较慢，请检查网络连接或稍后重试';
        }

        showUserMessage(errorMessage, 'error');
        this.showErrorState(errorMessage, solution);
    },

    // 清理模块资源（页面卸载时调用）
    cleanup() {
        console.log('清理天气模块资源...');

        // 清理定时器
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
            console.log('自动更新定时器已清理');
        }

        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
            console.log('倒计时定时器已清理');
        }

        // 清理页面可见性管理器
        if (this.visibilityManager && this.visibilityManager.cleanup) {
            this.visibilityManager.cleanup();
        }

        // 停止后台更新
        if (this.cache) {
            this.cache.backgroundUpdateInProgress = false;
        }

        // 移除事件监听器
        try {
            window.removeEventListener('beforeunload', this.cleanup);
        } catch (error) {
            console.warn('移除beforeunload事件监听器失败:', error);
        }

        console.log('天气模块资源清理完成');
    },

    // 销毁模块
    destroy() {
        console.log('销毁天气模块');

        // 调用清理方法
        this.cleanup();

        // 清理引用但保留缓存数据
        this.panel = null;
        this.weatherData = null;
        this.lastUpdateTime = null;
        this.isInitialized = false;
        this.isLoading = false;
        this.retryCount = 0;

        console.log('天气模块已销毁（缓存数据保留）');
    }
};

// 初始化已移至统一管理器

// 导出天气模块（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports.WeatherModule = WeatherModule;
}

// 在全局作用域中设置WeatherModule
if (typeof window !== 'undefined') {
    window.WeatherModule = WeatherModule;
}

console.log('天气模块已定义');

// ============================================================================
// API调用模块 - 本地SQLite数据访问
// ============================================================================

const ApiModule = {
    // 检查本地数据是否可用
    isLocalDataAvailable() {
        return typeof window.buildingsData !== 'undefined' && Array.isArray(window.buildingsData);
    },

    // 获取所有建筑物
    async getBuildings(filters = {}) {
        try {
            if (!this.isLocalDataAvailable()) {
                throw new Error('本地建筑物数据未加载');
            }

            let buildings = [...window.buildingsData];

            // 应用过滤器
            if (filters.type) {
                buildings = buildings.filter(b => b.type === filters.type);
            }
            if (filters.status) {
                buildings = buildings.filter(b => b.status === filters.status);
            }

            return {
                success: true,
                data: {
                    data: buildings,
                    total: buildings.length,
                    source: 'sqlite_local'
                }
            };

        } catch (error) {
            console.error('获取建筑物数据失败:', error);
            return { success: false, error: error.message };
        }
    },

    // 搜索建筑物
    async searchBuildings(query) {
        try {
            if (!this.isLocalDataAvailable()) {
                throw new Error('本地建筑物数据未加载');
            }

            if (!query || query.trim() === '') {
                return await this.getBuildings();
            }

            const searchResults = window.searchBuildings(query.trim());

            return {
                success: true,
                data: {
                    data: searchResults,
                    total: searchResults.length,
                    query: query.trim(),
                    source: 'sqlite_local'
                }
            };

        } catch (error) {
            console.error('搜索建筑物失败:', error);
            return { success: false, error: error.message };
        }
    },

    // 获取建筑物详情
    async getBuildingById(id) {
        try {
            if (!this.isLocalDataAvailable()) {
                throw new Error('本地建筑物数据未加载');
            }

            const building = window.getBuildingById(parseInt(id));

            if (!building) {
                return { success: false, error: '建筑物不存在' };
            }

            return {
                success: true,
                data: {
                    data: building,
                    source: 'sqlite_local'
                }
            };

        } catch (error) {
            console.error('获取建筑物详情失败:', error);
            return { success: false, error: error.message };
        }
    },

    // 获取天气信息（保持原有逻辑）
    async getWeather() {
        // 天气功能保持使用外部API
        return { success: false, error: '天气功能使用独立API' };
    },

    // 健康检查
    async healthCheck() {
        try {
            if (!this.isLocalDataAvailable()) {
                return {
                    success: false,
                    error: '本地建筑物数据未加载'
                };
            }

            const stats = window.getBuildingTypeStats();

            return {
                success: true,
                data: {
                    status: 'healthy',
                    database: 'sqlite_local',
                    buildings_count: window.buildingsData.length,
                    building_types: stats,
                    message: '本地SQLite数据访问正常'
                }
            };

        } catch (error) {
            console.error('健康检查失败:', error);
            return { success: false, error: error.message };
        }
    }
};

// ============================================================================
// 数据库搜索模块 - 覆盖原有搜索功能
// ============================================================================

// 保存原有的performSearch方法
if (typeof SearchModule !== 'undefined') {
    SearchModule.performSearchOriginal = SearchModule.performSearch;

    // 重写performSearch方法使用数据库
    SearchModule.performSearch = async function(query) {
        console.log(`🔍 执行数据库搜索: "${query}"`);

        // 清空当前结果
        this.currentResults = [];
        this.selectedIndex = -1;

        // 检查查询长度
        if (!query || query.trim().length < CONFIG.search.minQueryLength) {
            console.log('查询长度不足，隐藏结果');
            this.hideSearchResults();
            this.clearHighlight();
            return;
        }

        try {
            // 显示加载状态
            console.log('显示加载状态...');
            this.showSearchLoading();

            // 从数据库搜索
            console.log('开始API调用...');
            const result = await ApiModule.searchBuildings(query.trim());
            console.log('API调用结果:', result);

            if (result.success) {
                console.log('搜索成功，数据:', result.data);

                // 检查数据结构
                const buildings = result.data.data || result.data;
                console.log('建筑物数据:', buildings);

                if (buildings && buildings.length > 0) {
                    // 🎯 混合数据源：数据库信息 + GeoJSON几何
                    this.currentResults = buildings.map(building => {
                        console.log(`🔍 处理数据库建筑: ${building.name}`);

                        // 在原有GeoJSON数据中查找匹配的建筑物
                        let matchedGeoBuilding = null;
                        if (this.buildingsData && this.buildingsData.length > 0) {
                            // 🔧 改进的智能匹配算法 - 解决名称不匹配问题
                            matchedGeoBuilding = this.buildingsData.find(geoBuilding => {
                                if (!geoBuilding.name || !building.name) return false;

                                const geoName = geoBuilding.name.toLowerCase().trim();
                                const dbName = building.name.toLowerCase().trim();

                                console.log(`🔍 匹配检查: 数据库"${dbName}" vs GeoJSON"${geoName}"`);

                                // 🎯 特殊匹配规则：解决已知的名称差异
                                const specialMatches = {
                                    "体育中心、操场看台": "体育中心、操场看台",
                                    "西操场": "西操场",
                                    "东操场": "东操场"
                                };

                                // 检查特殊匹配规则
                                if (specialMatches[dbName] && geoName === specialMatches[dbName]) {
                                    console.log(`✅ 特殊规则匹配: "${dbName}" -> "${geoName}"`);
                                    return true;
                                }

                                // 精确匹配
                                if (geoName === dbName) {
                                    console.log(`✅ 精确匹配: "${dbName}"`);
                                    return true;
                                }

                                // 包含匹配
                                if (geoName.includes(dbName) || dbName.includes(geoName)) {
                                    console.log(`✅ 包含匹配: "${dbName}" <-> "${geoName}"`);
                                    return true;
                                }

                                // 关键词匹配（去掉"号"、"楼"等后缀）
                                const geoCore = geoName.replace(/[号楼]/g, '');
                                const dbCore = dbName.replace(/[号楼]/g, '');
                                if (geoCore === dbCore) {
                                    console.log(`✅ 关键词匹配: "${dbCore}"`);
                                    return true;
                                }

                                return false;
                            });
                        }

                        if (matchedGeoBuilding) {
                            console.log(`✅ 找到GeoJSON匹配: ${building.name} -> ${matchedGeoBuilding.name}`);

                            // 使用数据库信息 + GeoJSON几何
                            return {
                                // 数据库的详细信息
                                id: building.id,
                                name: building.name,
                                type: building.type,
                                description: building.description,
                                area: building.area,
                                floor_count: building.floor_count, // 🔧 添加楼层数字段
                                build_year: building.build_year,   // 🔧 添加建造年份字段
                                status: building.status,
                                longitude: building.longitude,
                                latitude: building.latitude,

                                // GeoJSON的几何和坐标信息
                                coordinate: matchedGeoBuilding.coordinate,
                                geometry: matchedGeoBuilding.geometry,

                                // 合并的属性信息
                                properties: {
                                    // 数据库信息优先
                                    name: building.name,
                                    type: building.type,
                                    description: building.description,
                                    floor_count: building.floor_count,
                                    area: building.area,
                                    build_year: building.build_year,
                                    status: building.status,
                                    longitude: building.longitude,
                                    latitude: building.latitude,

                                    // GeoJSON的原始属性作为备用
                                    ...matchedGeoBuilding.properties
                                }
                            };
                        } else {
                            console.log(`⚠️ 未找到GeoJSON匹配: ${building.name}，使用数据库坐标`);

                            // 只有数据库信息，创建点几何
                            return {
                                id: building.id,
                                name: building.name,
                                type: building.type,
                                description: building.description,
                                area: building.area,
                                floor_count: building.floor_count, // 🔧 添加楼层数字段
                                build_year: building.build_year,   // 🔧 添加建造年份字段
                                status: building.status,
                                longitude: building.longitude,
                                latitude: building.latitude,
                                coordinate: [building.longitude, building.latitude],
                                geometry: {
                                    type: 'Point',
                                    coordinates: [building.longitude, building.latitude]
                                },
                                properties: {
                                    name: building.name,
                                    type: building.type,
                                    description: building.description,
                                    floor_count: building.floor_count,
                                    area: building.area,
                                    build_year: building.build_year,
                                    status: building.status,
                                    longitude: building.longitude,
                                    latitude: building.latitude
                                }
                            };
                        }
                    }).slice(0, CONFIG.search.maxResults);

                    console.log('转换后的结果:', this.currentResults);

                    // 显示搜索结果
                    this.displaySearchResults();
                } else {
                    console.log('没有找到匹配的建筑物');
                    this.showSearchError('未找到相关建筑物');
                }
            } else {
                console.error('数据库搜索失败:', result.error);
                this.showSearchError('搜索失败：' + result.error);
            }

        } catch (error) {
            console.error('搜索异常:', error);
            console.log('🔄 API搜索失败，回退到原有搜索方法');

            // 显示用户友好的错误提示
            showUserMessage('🔄 正在切换到备用搜索模式...', 'info');

            // 回退到原有的搜索方法
            if (this.performSearchOriginal) {
                console.log('📋 使用原有GeoJSON数据搜索');
                this.performSearchOriginal.call(this, query);

                // 延迟显示备用模式提示
                setTimeout(() => {
                    showUserMessage('💡 当前使用静态数据搜索，功能可能受限', 'warning');
                }, 1000);
            } else {
                this.showSearchError('搜索功能暂时不可用');
                showUserMessage('❌ 搜索功能暂时不可用，请稍后重试', 'error');
            }
        }
    };

    // 添加加载状态显示方法
    SearchModule.showSearchLoading = function() {
        if (this.searchResults) {
            this.searchResults.innerHTML = `
                <div class="search-loading" style="padding: 15px; text-align: center; color: #666;">
                    <div style="display: inline-block; width: 16px; height: 16px; border: 2px solid #ddd; border-top: 2px solid #4A90E2; border-radius: 50%; animation: spin 1s linear infinite; margin-right: 8px;"></div>
                    搜索中...
                </div>
            `;
            this.searchResults.style.display = 'block';
        }
    };

    // 添加错误状态显示方法
    SearchModule.showSearchError = function(message) {
        if (this.searchResults) {
            this.searchResults.innerHTML = `
                <div class="search-error" style="padding: 15px; text-align: center; color: #f44336;">
                    <span style="margin-right: 8px;">⚠️</span>
                    ${message}
                </div>
            `;
        }
    };

    console.log('SearchModule已升级为数据库版本');
}

// 在全局作用域中设置ApiModule
if (typeof window !== 'undefined') {
    window.ApiModule = ApiModule;
}

console.log('API模块和数据库集成已定义');

// ==================== 全局关闭按钮修复 ====================
// 🔧 确保建筑物信息面板关闭按钮正常工作
// 注意：此功能已集成到主要的初始化流程中（initializeBackgroundServices函数）

// 全局关闭事件处理器
function globalCloseHandler(e) {
    e.preventDefault();
    e.stopPropagation();

    console.log('🔧 全局关闭按钮被点击');

    const panel = document.getElementById('building-info-panel');
    if (panel) {
        panel.classList.remove('show');
        panel.classList.add('hide');
        console.log('✅ 建筑物信息面板已关闭');

        // 📊 记录关闭行为
        if (typeof UserTrackingModule !== 'undefined') {
            UserTrackingModule.trackAction('building_click', '关闭建筑信息', {
                method: 'close_button'
            });
        }

        // 清除高亮显示
        if (typeof SearchModule !== 'undefined' && SearchModule.clearHighlightWithAnimation) {
            SearchModule.clearHighlightWithAnimation();
        }
    } else {
        console.error('❌ 建筑物信息面板未找到');
    }
}

// ============================================================================
// 数据库连接初始化检查
// ============================================================================
// 注意：此功能已集成到主要的初始化流程中（initializeBackgroundServices函数）

// ============================================================================
// 统一初始化管理器 - 替代重复的事件监听器
// ============================================================================

const InitializationManager = {
    // 初始化队列
    initQueue: [
        { name: 'SearchModule', module: () => SearchModule, delay: 500 },
        { name: 'CompassModule', module: () => CompassModule, delay: 500 },
        { name: 'SystemInfoModule', module: () => SystemInfoModule, delay: 600 },
        { name: 'RouteModule', module: () => RouteModule, delay: 700 },
        { name: 'WeatherModule', module: () => WeatherModule, delay: 800 },
        { name: 'DistanceMeasurementModule', module: () => DistanceMeasurementModule, delay: 900 }
    ],

    // 统一初始化所有模块
    async initializeAllModules() {
        console.log('🚀 开始统一初始化所有模块...');

        for (const item of this.initQueue) {
            try {
                await new Promise(resolve => {
                    setTimeout(() => {
                        const module = item.module();
                        if (module && typeof module.init === 'function') {
                            if (module.init()) {
                                console.log(`✅ ${item.name} 初始化成功`);
                            } else {
                                console.error(`❌ ${item.name} 初始化失败`);
                            }
                        } else {
                            console.warn(`⚠️ ${item.name} 不存在或没有init方法`);
                        }
                        resolve();
                    }, item.delay);
                });
            } catch (error) {
                console.error(`❌ ${item.name} 初始化异常:`, error);
            }
        }

        // 绑定功能按钮事件
        setTimeout(() => {
            if (typeof bindFunctionButtons === 'function') {
                bindFunctionButtons();
                console.log('✅ 功能按钮事件绑定完成');
            }
        }, 300);

        console.log('🎉 所有模块初始化完成！');
    }
};

// ==================== 测距功能模块 ====================

const DistanceMeasurementModule = {
    // 模块状态
    isInitialized: false,
    isActive: false,

    // 地图相关
    map: null,
    measureLayer: null,
    measureSource: null,

    // 测量状态
    startPoint: null,
    endPoint: null,
    measureLine: null,
    startMarker: null,
    endMarker: null,

    // UI元素
    measureBtn: null,
    measurePanel: null,
    distanceDisplay: null,
    clearBtn: null,

    // 测量结果
    currentDistance: 0,

    // 初始化模块
    init() {
        console.log('🔧 初始化测距功能模块');

        try {
            // 检查地图是否已初始化
            if (!MapModule || !MapModule.isMapInitialized()) {
                console.warn('地图未初始化，延迟初始化测距功能');
                setTimeout(() => this.init(), 500);
                return false;
            }

            this.map = MapModule.map;
            if (!this.map) {
                console.error('地图实例为空，无法初始化测距功能');
                return false;
            }

            // 创建测量图层
            this.createMeasureLayer();

            // 获取UI元素
            this.initializeUI();

            // 创建结果面板
            this.createDistanceResultPanel();

            // 绑定事件
            this.bindEvents();

            // 监听语言切换事件
            this.bindLanguageChangeEvent();

            this.isInitialized = true;
            console.log('✅ 测距功能模块初始化成功');
            console.log('测距面板状态:', !!this.measurePanel);
            return true;

        } catch (error) {
            console.error('❌ 测距功能模块初始化失败:', error);
            console.error('错误详情:', error.stack);
            return false;
        }
    },

    // 创建测量图层
    createMeasureLayer() {
        console.log('创建测量图层');

        this.measureSource = new ol.source.Vector();
        this.measureLayer = new ol.layer.Vector({
            source: this.measureSource,
            style: this.getMeasureStyle(),
            zIndex: 1500 // 确保在其他图层之上
        });

        // 添加到地图
        this.map.addLayer(this.measureLayer);

        console.log('测量图层创建完成');
    },

    // 获取测量样式
    getMeasureStyle() {
        return (feature) => {
            const geometry = feature.getGeometry();
            const type = geometry.getType();

            if (type === 'LineString') {
                // 测量线样式
                return new ol.style.Style({
                    stroke: new ol.style.Stroke({
                        color: '#ff4444',
                        width: 3,
                        lineDash: [10, 5]
                    })
                });
            } else if (type === 'Point') {
                // 标记点样式
                const isStart = feature.get('type') === 'start';
                return new ol.style.Style({
                    image: new ol.style.Circle({
                        radius: 8,
                        fill: new ol.style.Fill({
                            color: isStart ? '#4caf50' : '#f44336'
                        }),
                        stroke: new ol.style.Stroke({
                            color: '#ffffff',
                            width: 2
                        })
                    }),
                    text: new ol.style.Text({
                        text: isStart ? t('distance.startPoint') : t('distance.endPoint'),
                        font: '12px Microsoft YaHei',
                        fill: new ol.style.Fill({
                            color: '#ffffff'
                        }),
                        stroke: new ol.style.Stroke({
                            color: '#000000',
                            width: 2
                        }),
                        offsetY: -20
                    })
                });
            }
        };
    },

    // 初始化UI
    initializeUI() {
        console.log('初始化测距UI');

        // 获取测距按钮
        this.measureBtn = document.getElementById('distance-measure-btn');
        if (!this.measureBtn) {
            console.warn('测距按钮未找到，创建按钮');
            this.createMeasureButton();
        }

        // 创建测距面板
        this.createMeasurePanel();
    },

    // 创建测距按钮
    createMeasureButton() {
        const functionButtons = document.querySelector('.function-buttons');
        if (!functionButtons) {
            console.error('功能按钮容器未找到');
            return;
        }

        const measureBtn = document.createElement('button');
        measureBtn.id = 'distance-measure-btn';
        measureBtn.className = 'function-btn';
        measureBtn.innerHTML = `
            <span class="btn-icon">📏</span>
            <span class="btn-text" data-i18n="measure.title">测距工具</span>
        `;

        functionButtons.appendChild(measureBtn);
        this.measureBtn = measureBtn;

        console.log('测距按钮已创建');
    },

    // 创建测距面板
    createMeasurePanel() {
        // 检查是否已存在
        let existingPanel = document.getElementById('distance-measure-panel');
        if (existingPanel) {
            this.measurePanel = existingPanel;
            this.distanceDisplay = document.getElementById('distance-value');
            this.clearBtn = document.getElementById('distance-clear');
            return;
        }

        const panelHTML = `
            <div id="distance-measure-panel" class="measure-panel" style="display: none; position: absolute; top: 70px; right: 20px; width: 280px; background: white; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1000; overflow: hidden;">
                <div class="panel-header" style="padding: 15px; background: linear-gradient(135deg, #4A90E2 0%, #2E86AB 100%); color: white; display: flex; justify-content: space-between; align-items: center;">
                    <h3 class="panel-title" style="margin: 0; font-size: 18px;" data-i18n="distance.panelTitle">📏 测距工具</h3>
                    <button id="distance-panel-close" class="panel-close" style="background: none; border: none; color: white; font-size: 20px; cursor: pointer;">×</button>
                </div>
                <div class="panel-content" style="padding: 15px;">
                    <div class="measure-info" style="margin-bottom: 15px;">
                        <div class="measure-status" style="margin-bottom: 10px; padding: 8px; background: #f5f5f5; border-radius: 6px;">
                            <span id="measure-status-text" style="font-size: 14px;" data-i18n="distance.clickMapStart">点击地图设置起点</span>
                        </div>
                        <div class="distance-result" style="display: flex; align-items: center; padding: 10px; background: #e3f2fd; border-radius: 6px;">
                            <div class="distance-label" style="font-weight: bold; margin-right: 10px;" data-i18n="distance.distance">距离：</div>
                            <div id="distance-value" class="distance-value" style="font-size: 16px;">-- <span data-i18n="distance.meters">米</span></div>
                        </div>
                    </div>
                    <div class="measure-controls" style="display: flex; gap: 10px; margin-bottom: 15px;">
                        <button id="distance-clear" class="btn btn-secondary" style="flex: 1; padding: 8px; border: none; background: #f5f5f5; border-radius: 6px; cursor: pointer;" data-i18n="distance.clearMeasure">清除测量</button>
                        <button id="distance-export" class="btn btn-primary" style="flex: 1; padding: 8px; border: none; background: #4A90E2; color: white; border-radius: 6px; cursor: pointer;" data-i18n="distance.exportResult">导出结果</button>
                    </div>
                    <div class="measure-help" style="font-size: 12px; color: #666; background: #f9f9f9; padding: 10px; border-radius: 6px;">
                        <p style="margin: 5px 0;" data-i18n="distance.helpText1">📍 点击地图上两个点进行测距</p>
                        <p style="margin: 5px 0;" data-i18n="distance.helpText2">📏 支持直线距离测量</p>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', panelHTML);

        // 获取元素引用
        this.measurePanel = document.getElementById('distance-measure-panel');
        this.distanceDisplay = document.getElementById('distance-value');
        this.clearBtn = document.getElementById('distance-clear');

        console.log('测距面板已创建');
    },

    // 绑定事件
    bindEvents() {
        console.log('绑定测距功能事件');

        // 注意：测距按钮点击事件在 bindDistanceMeasureButton() 函数中处理
        // 这里不重复绑定，避免事件冲突

        // 面板关闭按钮
        const closeBtn = document.getElementById('distance-panel-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.deactivateMeasureMode();
            });
        }

        // 清除按钮
        if (this.clearBtn) {
            this.clearBtn.addEventListener('click', () => {
                this.clearMeasurement();
            });
        }

        // 导出按钮
        const exportBtn = document.getElementById('distance-export');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportMeasurement();
            });
        }

        // 地图点击事件
        this.map.on('click', (event) => {
            if (this.isActive) {
                this.handleMapClick(event);
            }
        });

        console.log('测距功能事件绑定完成');
    },

    // 绑定语言切换事件
    bindLanguageChangeEvent() {
        document.addEventListener('languageChanged', () => {
            console.log('测距模块响应语言切换');
            this.updateLanguageTexts();
        });
    },

    // 更新语言文本
    updateLanguageTexts() {
        // 更新距离显示单位
        if (this.distanceDisplay && this.currentDistance === 0) {
            this.distanceDisplay.innerHTML = '-- <span data-i18n="distance.meters">' + t('distance.meters') + '</span>';
        }

        // 更新状态文本
        if (this.isActive) {
            const statusElement = document.getElementById('measure-status-text');
            if (statusElement) {
                if (!this.startPoint) {
                    statusElement.textContent = t('distance.clickMapStart');
                } else if (!this.endPoint) {
                    statusElement.textContent = t('distance.clickMapEnd');
                } else {
                    statusElement.textContent = t('distance.measureComplete');
                }
            }
        }

        // 更新地图标记文本
        if (this.measureSource) {
            this.measureSource.getFeatures().forEach(feature => {
                if (feature.getGeometry().getType() === 'Point') {
                    const isStart = feature.get('type') === 'start';
                    const style = feature.getStyle();
                    if (style && style.getText) {
                        const textStyle = style.getText();
                        if (textStyle) {
                            textStyle.setText(isStart ? t('distance.startPoint') : t('distance.endPoint'));
                        }
                    }
                }
            });
        }

        console.log('测距模块语言文本已更新');
    },

    // 切换测距模式
    toggleMeasureMode() {
        if (this.isActive) {
            this.deactivateMeasureMode();
        } else {
            this.activateMeasureMode();
        }
    },

    // 激活测距模式
    activateMeasureMode() {
        console.log('激活测距模式');

        // 检查面板是否存在
        if (!this.measurePanel) {
            console.error('测距面板不存在，尝试重新创建');
            this.createMeasurePanel();
            if (!this.measurePanel) {
                console.error('无法创建测距面板');
                showUserMessage('测距面板创建失败', 'error');
                return;
            }
        }

        this.isActive = true;

        // 显示面板
        console.log('显示测距面板');
        this.measurePanel.style.display = 'block';

        // 更新按钮状态
        if (this.measureBtn) {
            this.measureBtn.classList.add('active');
        }

        // 清除之前的测量
        this.clearMeasurement();

        // 更新状态文本
        this.updateStatusText('distance.clickMapStart');

        // 改变鼠标样式
        try {
            if (this.map && this.map.getTargetElement) {
                this.map.getTargetElement().style.cursor = 'crosshair';
            }
        } catch (error) {
            console.warn('无法设置鼠标样式:', error);
        }

        console.log('测距模式已激活');
    },

    // 取消测距模式
    deactivateMeasureMode() {
        console.log('取消测距模式');

        this.isActive = false;

        // 隐藏面板
        if (this.measurePanel) {
            this.measurePanel.style.display = 'none';
        }

        // 更新按钮状态
        if (this.measureBtn) {
            this.measureBtn.classList.remove('active');
        }

        // 恢复鼠标样式
        this.map.getTargetElement().style.cursor = '';

        // 清除测量
        this.clearMeasurement();

        // 通知面板管理器面板已关闭
        if (typeof PanelManager !== 'undefined') {
            PanelManager.notifyPanelClosed(PanelManager.panelTypes.DISTANCE_MEASURE);
        }

        console.log('测距模式已取消');
    },

    // 处理地图点击事件
    handleMapClick(event) {
        const coordinate = event.coordinate;

        if (!this.startPoint) {
            // 设置起点
            this.setStartPoint(coordinate);
        } else if (!this.endPoint) {
            // 设置终点并计算距离
            this.setEndPoint(coordinate);
            this.calculateDistance();
        } else {
            // 重新开始测量
            this.clearMeasurement();
            this.setStartPoint(coordinate);
        }
    },

    // 设置起点
    setStartPoint(coordinate) {
        console.log('设置测距起点:', coordinate);

        this.startPoint = coordinate;

        // 创建起点标记
        const startFeature = new ol.Feature({
            geometry: new ol.geom.Point(coordinate),
            type: 'start'
        });

        this.measureSource.addFeature(startFeature);
        this.startMarker = startFeature;

        // 更新状态
        this.updateStatusText('distance.clickMapEnd');

        // 记录用户行为
        if (typeof UserTrackingModule !== 'undefined') {
            UserTrackingModule.trackAction('distance_measure', '设置起点', {
                coordinate: coordinate
            });
        }
    },

    // 设置终点
    setEndPoint(coordinate) {
        console.log('设置测距终点:', coordinate);

        this.endPoint = coordinate;

        // 创建终点标记
        const endFeature = new ol.Feature({
            geometry: new ol.geom.Point(coordinate),
            type: 'end'
        });

        this.measureSource.addFeature(endFeature);
        this.endMarker = endFeature;

        // 创建测量线
        const lineFeature = new ol.Feature({
            geometry: new ol.geom.LineString([this.startPoint, this.endPoint]),
            type: 'line'
        });

        this.measureSource.addFeature(lineFeature);
        this.measureLine = lineFeature;

        // 记录用户行为
        if (typeof UserTrackingModule !== 'undefined') {
            UserTrackingModule.trackAction('distance_measure', '设置终点', {
                coordinate: coordinate
            });
        }
    },

    // 计算距离
    calculateDistance() {
        if (!this.startPoint || !this.endPoint) {
            return;
        }

        // 计算直线距离（米）
        const distance = this.getDistance(this.startPoint, this.endPoint);
        this.currentDistance = distance;

        // 更新显示
        this.updateDistanceDisplay(distance);
        this.updateStatusText('distance.measureComplete');

        // 显示测距结果面板
        this.showDistanceResult(distance);

        console.log('测距完成:', distance.toFixed(2) + '米');

        // 记录用户行为
        if (typeof UserTrackingModule !== 'undefined') {
            UserTrackingModule.trackAction('distance_measure', '计算距离', {
                distance: distance,
                startPoint: this.startPoint,
                endPoint: this.endPoint
            });
        }
    },

    // 计算两点间距离
    getDistance(coord1, coord2) {
        // 将投影坐标转换为地理坐标
        const lonLat1 = ol.proj.toLonLat(coord1);
        const lonLat2 = ol.proj.toLonLat(coord2);

        // 使用Haversine公式计算地球表面两点间距离
        const R = 6371000; // 地球半径（米）
        const lat1Rad = lonLat1[1] * Math.PI / 180;
        const lat2Rad = lonLat2[1] * Math.PI / 180;
        const deltaLatRad = (lonLat2[1] - lonLat1[1]) * Math.PI / 180;
        const deltaLonRad = (lonLat2[0] - lonLat1[0]) * Math.PI / 180;

        const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                  Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                  Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c;
    },

    // 更新距离显示
    updateDistanceDisplay(distance) {
        if (!this.distanceDisplay) return;

        let displayText;
        if (distance >= 1000) {
            displayText = (distance / 1000).toFixed(2) + ' ' + t('distance.kilometers');
        } else {
            displayText = distance.toFixed(2) + ' ' + t('distance.meters');
        }

        this.distanceDisplay.innerHTML = displayText;
        this.distanceDisplay.style.color = '#4caf50';
        this.distanceDisplay.style.fontWeight = 'bold';
    },

    // 创建测距结果面板
    createDistanceResultPanel() {
        // 检查是否已存在
        let existingPanel = document.getElementById('distance-result-panel');
        if (existingPanel) {
            this.resultPanel = existingPanel;
            return;
        }

        const panelHTML = `
            <div id="distance-result-panel" class="distance-result-panel" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%) scale(0.9); width: 400px; background: rgba(255, 255, 255, 0.95); border-radius: 16px; box-shadow: 0 8px 32px rgba(0,0,0,0.2); z-index: 2000; overflow: hidden; backdrop-filter: blur(20px); opacity: 0; transition: all 0.3s ease;">
                <div class="panel-header" style="padding: 20px; background: linear-gradient(135deg, #4A90E2 0%, #2E86AB 100%); color: white; text-align: center;">
                    <h3 style="margin: 0; font-size: 20px; display: flex; align-items: center; justify-content: center; gap: 10px;">
                        <span>📏</span>
                        <span data-i18n="distance.resultTitle">测距结果</span>
                    </h3>
                    <button id="distance-result-close" style="position: absolute; top: 15px; right: 20px; background: none; border: none; color: white; font-size: 24px; cursor: pointer; opacity: 0.8; transition: opacity 0.3s;">×</button>
                </div>
                <div class="panel-content" style="padding: 25px;">
                    <div class="result-summary" style="text-align: center; margin-bottom: 25px;">
                        <div class="distance-icon" style="font-size: 48px; margin-bottom: 15px;">📐</div>
                        <div class="distance-main" style="font-size: 32px; font-weight: bold; color: #4A90E2; margin-bottom: 10px;" id="result-distance-value">-- <span data-i18n="distance.meters">米</span></div>
                        <div class="distance-type" style="color: #666; font-size: 14px;" data-i18n="distance.straightLine">直线距离</div>
                    </div>

                    <div class="result-details" style="background: #f8f9fa; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                        <div class="detail-item" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <span style="color: #666;" data-i18n="distance.startCoord">📍 起点坐标:</span>
                            <span id="result-start-coord" style="font-family: monospace; font-size: 12px;">--</span>
                        </div>
                        <div class="detail-item" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <span style="color: #666;" data-i18n="distance.endCoord">🎯 终点坐标:</span>
                            <span id="result-end-coord" style="font-family: monospace; font-size: 12px;">--</span>
                        </div>
                        <div class="detail-item" style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="color: #666;" data-i18n="distance.measureTime">⏰ 测量时间:</span>
                            <span id="result-measure-time" style="font-size: 12px;">--</span>
                        </div>
                    </div>

                    <div class="result-actions" style="display: flex; gap: 12px;">
                        <button id="distance-result-export" style="flex: 1; padding: 12px; background: #4A90E2; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; transition: background 0.3s;" data-i18n="distance.exportResult">📤 导出结果</button>
                        <button id="distance-result-new" style="flex: 1; padding: 12px; background: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; transition: background 0.3s;" data-i18n="distance.newMeasure">📏 重新测距</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', panelHTML);
        this.resultPanel = document.getElementById('distance-result-panel');

        // 绑定结果面板事件
        this.bindResultPanelEvents();

        console.log('测距结果面板已创建');
    },

    // 绑定结果面板事件
    bindResultPanelEvents() {
        // 关闭按钮
        const closeBtn = document.getElementById('distance-result-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hideDistanceResult();
            });
        }

        // 导出按钮
        const exportBtn = document.getElementById('distance-result-export');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportMeasurement();
            });
        }

        // 重新测距按钮
        const newBtn = document.getElementById('distance-result-new');
        if (newBtn) {
            newBtn.addEventListener('click', () => {
                this.hideDistanceResult();
                this.clearMeasurement();
                this.updateStatusText('distance.clickMapStart');
            });
        }

        // 点击面板外部关闭
        this.resultPanel.addEventListener('click', (e) => {
            if (e.target === this.resultPanel) {
                this.hideDistanceResult();
            }
        });
    },

    // 显示测距结果
    showDistanceResult(distance) {
        // 确保结果面板存在
        if (!this.resultPanel) {
            this.createDistanceResultPanel();
        }

        // 格式化距离显示
        let displayText;
        if (distance >= 1000) {
            displayText = (distance / 1000).toFixed(2) + ' ' + t('distance.kilometers');
        } else {
            displayText = distance.toFixed(2) + ' ' + t('distance.meters');
        }

        // 更新结果内容
        const distanceValue = document.getElementById('result-distance-value');
        if (distanceValue) {
            distanceValue.innerHTML = displayText;
        }

        // 更新坐标信息
        const startCoord = document.getElementById('result-start-coord');
        const endCoord = document.getElementById('result-end-coord');
        if (startCoord && this.startPoint) {
            const lonLat1 = ol.proj.toLonLat(this.startPoint);
            startCoord.textContent = `${lonLat1[0].toFixed(6)}, ${lonLat1[1].toFixed(6)}`;
        }
        if (endCoord && this.endPoint) {
            const lonLat2 = ol.proj.toLonLat(this.endPoint);
            endCoord.textContent = `${lonLat2[0].toFixed(6)}, ${lonLat2[1].toFixed(6)}`;
        }

        // 更新测量时间
        const measureTime = document.getElementById('result-measure-time');
        if (measureTime) {
            measureTime.textContent = new Date().toLocaleString();
        }

        // 显示面板
        this.resultPanel.style.display = 'block';

        // 添加显示动画
        setTimeout(() => {
            this.resultPanel.style.opacity = '1';
            this.resultPanel.style.transform = 'translate(-50%, -50%) scale(1)';
        }, 10);

        console.log('测距结果面板已显示');
    },

    // 隐藏测距结果
    hideDistanceResult() {
        if (this.resultPanel) {
            this.resultPanel.style.opacity = '0';
            this.resultPanel.style.transform = 'translate(-50%, -50%) scale(0.9)';

            setTimeout(() => {
                this.resultPanel.style.display = 'none';
            }, 300);
        }
    },

    // 更新状态文本
    updateStatusText(textKey) {
        const statusElement = document.getElementById('measure-status-text');
        if (statusElement) {
            // 如果是翻译键，使用翻译函数
            if (textKey.includes('.')) {
                statusElement.textContent = t(textKey);
            } else {
                // 兼容直接传入文本的情况
                statusElement.textContent = textKey;
            }
        }
    },

    // 清除测量
    clearMeasurement() {
        console.log('清除测量数据');

        // 清除地图上的要素
        if (this.measureSource) {
            this.measureSource.clear();
        }

        // 重置状态
        this.startPoint = null;
        this.endPoint = null;
        this.measureLine = null;
        this.startMarker = null;
        this.endMarker = null;
        this.currentDistance = 0;

        // 重置显示
        if (this.distanceDisplay) {
            this.distanceDisplay.innerHTML = '-- <span data-i18n="distance.meters">' + t('distance.meters') + '</span>';
            this.distanceDisplay.style.color = '';
            this.distanceDisplay.style.fontWeight = '';
        }

        // 更新状态文本
        if (this.isActive) {
            this.updateStatusText('distance.clickMapStart');
        }
    },

    // 导出测量结果
    exportMeasurement() {
        if (!this.startPoint || !this.endPoint || this.currentDistance === 0) {
            this.showMessage(t('distance.noData'), 'warning');
            return;
        }

        try {
            // 构建导出数据
            const currentLang = window.I18N ? window.I18N.currentLanguage : 'zh';
            const locale = currentLang === 'en' ? 'en-US' : 'zh-CN';

            const exportData = {
                title: t('distance.reportTitle'),
                measureTime: new Date().toLocaleString(locale),
                startPoint: {
                    coordinate: ol.proj.toLonLat(this.startPoint),
                    name: t('distance.startPoint')
                },
                endPoint: {
                    coordinate: ol.proj.toLonLat(this.endPoint),
                    name: t('distance.endPoint')
                },
                distance: {
                    meters: this.currentDistance,
                    kilometers: this.currentDistance / 1000,
                    formatted: this.currentDistance >= 1000 ?
                        (this.currentDistance / 1000).toFixed(2) + ' ' + t('distance.kilometers') :
                        this.currentDistance.toFixed(2) + ' ' + t('distance.meters')
                },
                measureType: t('distance.straightLine'),
                system: t('distance.system')
            };

            // 生成文本格式报告
            const txtContent = this.generateTxtReport(exportData);

            // 创建下载
            const blob = new Blob([txtContent], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            const fileName = currentLang === 'en' ?
                `Distance_Result_${new Date().getTime()}.txt` :
                `测距结果_${new Date().getTime()}.txt`;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            URL.revokeObjectURL(url);

            this.showMessage(t('distance.exportSuccess'), 'success');

            console.log('测距结果已导出');

        } catch (error) {
            console.error('导出测距结果失败:', error);
            this.showMessage(t('distance.exportFailed'), 'error');
        }
    },

    // 生成文本格式报告
    generateTxtReport(data) {
        const currentLang = window.I18N ? window.I18N.currentLanguage : 'zh';

        if (currentLang === 'en') {
            return this.generateEnglishTxtReport(data);
        } else {
            return this.generateChineseTxtReport(data);
        }
    },

    // 生成中文文本报告
    generateChineseTxtReport(data) {
        return `
================================================================================
                           南通大学地图系统测距报告
================================================================================

测量时间：${data.measureTime}
系统版本：${data.system}

================================================================================
                              测距结果
================================================================================

测量状态：测量成功 ✓

基本信息：
  起点坐标：${data.startPoint.coordinate.map(c => c.toFixed(6)).join(', ')}
  终点坐标：${data.endPoint.coordinate.map(c => c.toFixed(6)).join(', ')}
  直线距离：${data.distance.formatted}
  测量类型：${data.measureType}

================================================================================
                              详细信息
================================================================================

坐标系统：WGS84 地理坐标系
测量精度：米级精度
计算方法：Haversine公式（球面距离计算）

距离换算：
  米：${data.distance.meters.toFixed(2)} 米
  公里：${data.distance.kilometers.toFixed(6)} 公里

================================================================================
                              使用说明
================================================================================

1. 此测距结果基于地图坐标计算得出
2. 显示的是两点间的直线距离
3. 实际行走距离可能因道路、建筑物等因素而有所不同
4. 建议结合实际情况进行路径规划

================================================================================
                        南通大学地图系统
                         版权所有 © 2025
================================================================================
        `.trim();
    },

    // 生成英文文本报告
    generateEnglishTxtReport(data) {
        return `
================================================================================
                    Nantong University Map System Distance Report
================================================================================

Measurement Time: ${data.measureTime}
System Version: ${data.system}

================================================================================
                              Distance Results
================================================================================

Measurement Status: Successful ✓

Basic Information:
  Start Coordinates: ${data.startPoint.coordinate.map(c => c.toFixed(6)).join(', ')}
  End Coordinates: ${data.endPoint.coordinate.map(c => c.toFixed(6)).join(', ')}
  Straight Distance: ${data.distance.formatted}
  Measurement Type: ${data.measureType}

================================================================================
                              Detailed Information
================================================================================

Coordinate System: WGS84 Geographic Coordinate System
Measurement Accuracy: Meter-level precision
Calculation Method: Haversine formula (spherical distance calculation)

Distance Conversion:
  Meters: ${data.distance.meters.toFixed(2)} meters
  Kilometers: ${data.distance.kilometers.toFixed(6)} kilometers

================================================================================
                              Usage Instructions
================================================================================

1. This distance result is calculated based on map coordinates
2. Shows the straight-line distance between two points
3. Actual walking distance may vary due to roads, buildings, and other factors
4. It is recommended to combine with actual conditions for route planning

================================================================================
                        Nantong University Map System
                         Copyright © 2025
================================================================================
        `.trim();
    },

    // 显示消息
    showMessage(message, type = 'info') {
        console.log(`测距模块消息 [${type}]: ${message}`);

        // 可以集成到全局消息系统
        if (typeof showUserMessage === 'function') {
            showUserMessage(message, type);
        }
    },

    // 获取模块状态
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            isActive: this.isActive,
            hasStartPoint: !!this.startPoint,
            hasEndPoint: !!this.endPoint,
            currentDistance: this.currentDistance
        };
    }
};

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.DistanceMeasurementModule = DistanceMeasurementModule;
}

// 调试函数已删除以减少代码冗余

// 统一的地图准备事件监听器
document.addEventListener('mapReady', () => {
    console.log('🗺️ 地图准备完成，开始初始化所有模块...');
    InitializationManager.initializeAllModules();
});

// ============================================================================
// 资源加载检测模块 - 检测关键外部资源加载状态
// ============================================================================

const ResourceLoadingDetector = {
    // 需要检测的关键资源列表
    requiredResources: [
        {
            name: 'OpenLayers',
            check: () => typeof ol !== 'undefined' && typeof ol.Map !== 'undefined'
        },
        {
            name: 'GoogleFonts',
            check: () => ResourceLoadingDetector.checkFontLoaded('Noto Sans SC')
        },
        {
            name: 'OpenLayersCSS',
            check: () => ResourceLoadingDetector.checkCSSLoaded('ol.css')
        }
    ],

    // 检查所有资源的加载状态
    checkAllResources() {
        console.log('🔍 开始检查资源加载状态...');
        const results = [];

        for (const resource of this.requiredResources) {
            try {
                const isLoaded = resource.check();
                results.push({
                    name: resource.name,
                    loaded: isLoaded,
                    timestamp: new Date().toISOString()
                });
                console.log(`${isLoaded ? '✅' : '❌'} ${resource.name}: ${isLoaded ? '已加载' : '未加载'}`);
            } catch (error) {
                results.push({
                    name: resource.name,
                    loaded: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
                console.error(`❌ ${resource.name} 检查失败:`, error);
            }
        }

        console.log('🔍 资源检查完成，结果汇总:', results);
        return results;
    },

    // 检查字体是否已加载
    checkFontLoaded(fontFamily) {
        try {
            // 创建测试元素
            const testElement = document.createElement('div');
            testElement.style.fontFamily = `"${fontFamily}", Arial, sans-serif`;
            testElement.style.position = 'absolute';
            testElement.style.visibility = 'hidden';
            testElement.style.fontSize = '16px';
            testElement.style.left = '-9999px';
            testElement.textContent = '测试字体加载Test Font Loading';

            document.body.appendChild(testElement);

            // 获取计算后的样式
            const computedStyle = window.getComputedStyle(testElement);
            const actualFont = computedStyle.fontFamily;

            // 清理测试元素
            document.body.removeChild(testElement);

            // 检查是否包含目标字体
            const fontLoaded = actualFont.toLowerCase().includes(fontFamily.toLowerCase());
            console.log(`字体检查: ${fontFamily} -> 实际字体: ${actualFont} -> 已加载: ${fontLoaded}`);

            return fontLoaded;
        } catch (error) {
            console.error(`字体检查失败 (${fontFamily}):`, error);
            return false;
        }
    },

    // 检查CSS文件是否已加载
    checkCSSLoaded(cssFileName) {
        try {
            const styleSheets = Array.from(document.styleSheets);

            for (const sheet of styleSheets) {
                try {
                    // 检查href是否包含目标CSS文件名
                    if (sheet.href && sheet.href.includes(cssFileName)) {
                        // 尝试访问CSS规则来确认加载成功
                        const rules = sheet.cssRules || sheet.rules;
                        console.log(`CSS检查: ${cssFileName} -> 找到样式表，规则数: ${rules ? rules.length : 0}`);
                        return rules && rules.length > 0;
                    }
                } catch (e) {
                    // 跨域CSS可能无法访问规则，但存在href说明已加载
                    if (sheet.href && sheet.href.includes(cssFileName)) {
                        console.log(`CSS检查: ${cssFileName} -> 跨域CSS已加载`);
                        return true;
                    }
                }
            }

            console.log(`CSS检查: ${cssFileName} -> 未找到对应样式表`);
            return false;
        } catch (error) {
            console.error(`CSS检查失败 (${cssFileName}):`, error);
            return false;
        }
    },

    // 处理资源加载失败的情况
    handleResourceFailures(results) {
        const failedResources = results.filter(r => !r.loaded);

        if (failedResources.length > 0) {
            console.warn('⚠️ 部分资源加载失败:', failedResources.map(r => r.name));

            // 记录详细的失败信息
            console.error('资源加载失败详情:', {
                failedResources: failedResources,
                totalResources: results.length,
                failureRate: `${(failedResources.length / results.length * 100).toFixed(1)}%`,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent
            });

            // 显示用户友好的提示
            if (typeof showUserMessage === 'function') {
                const failedNames = failedResources.map(r => r.name).join(', ');
                showUserMessage(`部分功能可能受限 (${failedNames})，但系统仍可正常使用`, 'warning');
            } else {
                console.warn('showUserMessage函数不可用，无法显示用户提示');
            }

            // 提供降级处理建议
            this.provideFallbackSuggestions(failedResources);

            return false;
        }

        console.log('✅ 所有资源加载成功');
        return true;
    },

    // 提供降级处理建议
    provideFallbackSuggestions(failedResources) {
        const suggestions = [];

        for (const resource of failedResources) {
            switch (resource.name) {
                case 'OpenLayers':
                    suggestions.push('地图功能可能无法正常使用，请检查网络连接');
                    break;
                case 'GoogleFonts':
                    suggestions.push('字体显示可能使用系统默认字体');
                    break;
                case 'OpenLayersCSS':
                    suggestions.push('地图样式可能显示异常');
                    break;
                default:
                    suggestions.push(`${resource.name} 功能可能受影响`);
            }
        }

        console.warn('降级处理建议:', suggestions);

        // 如果OpenLayers相关资源失败，禁用地图功能
        const hasMapResourceFailure = failedResources.some(r =>
            r.name === 'OpenLayers' || r.name === 'OpenLayersCSS'
        );

        if (hasMapResourceFailure) {
            console.warn('⚠️ 地图相关资源加载失败，建议禁用地图功能');
            // 这里可以设置一个全局标志来禁用地图功能
            if (typeof window !== 'undefined') {
                window.MAP_DISABLED = true;
            }
        }
    },

    // 延迟检查资源（非阻塞方式）
    checkResourcesAsync(delay = 2000) {
        console.log(`🕐 将在 ${delay}ms 后进行资源检查...`);

        setTimeout(() => {
            try {
                const results = this.checkAllResources();
                this.handleResourceFailures(results);
            } catch (error) {
                console.error('❌ 资源检查过程中发生异常:', error);
            }
        }, delay);
    }
};

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.ResourceLoadingDetector = ResourceLoadingDetector;
}

// ============================================================================
// 降级界面管理器 - 最终安全网界面
// ============================================================================

const FallbackInterfaceManager = {
    // 显示降级界面
    show(errorInfo = null) {
        console.log('🚨 显示降级界面');
        console.log('错误信息:', errorInfo);

        try {
            // 清除现有内容
            document.body.innerHTML = '';

            // 创建降级界面
            const fallbackHTML = `
                <div style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100vh;
                    flex-direction: column;
                    font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif;
                    background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 50%, #90CAF9 100%);
                    color: #333;
                    text-align: center;
                    padding: 20px;
                    margin: 0;
                    box-sizing: border-box;
                ">
                    <div style="
                        background: white;
                        padding: 40px;
                        border-radius: 12px;
                        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                        max-width: 500px;
                        width: 90%;
                        box-sizing: border-box;
                    ">
                        <div style="
                            width: 64px;
                            height: 64px;
                            margin: 0 auto 20px auto;
                            background: #4A90E2;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                            font-size: 24px;
                            font-weight: bold;
                        ">智</div>

                        <h1 style="
                            margin: 0 0 20px 0;
                            color: #4A90E2;
                            font-size: 24px;
                            font-weight: 600;
                        ">智慧校园系统</h1>

                        <p style="
                            margin: 0 0 20px 0;
                            font-size: 16px;
                            line-height: 1.5;
                            color: #666;
                        ">
                            系统初始化遇到问题，正在尝试恢复...
                        </p>

                        ${errorInfo ? `
                            <div style="
                                margin: 0 0 20px 0;
                                padding: 12px;
                                background: #fff5f5;
                                border: 1px solid #fed7d7;
                                border-radius: 6px;
                                font-size: 14px;
                                color: #e53e3e;
                                text-align: left;
                            ">
                                <strong>错误详情:</strong><br>
                                ${this.formatErrorInfo(errorInfo)}
                            </div>
                        ` : ''}

                        <div style="
                            display: flex;
                            gap: 12px;
                            justify-content: center;
                            flex-wrap: wrap;
                        ">
                            <button onclick="location.reload()" style="
                                padding: 12px 24px;
                                background: #4A90E2;
                                color: white;
                                border: none;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 14px;
                                font-weight: 500;
                                transition: background-color 0.2s;
                                min-width: 100px;
                            " onmouseover="this.style.background='#357abd'" onmouseout="this.style.background='#4A90E2'">
                                🔄 重新加载
                            </button>

                            <button onclick="FallbackInterfaceManager.showDiagnostics()" style="
                                padding: 12px 24px;
                                background: #6c757d;
                                color: white;
                                border: none;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 14px;
                                font-weight: 500;
                                transition: background-color 0.2s;
                                min-width: 100px;
                            " onmouseover="this.style.background='#545b62'" onmouseout="this.style.background='#6c757d'">
                                🔍 诊断信息
                            </button>

                            <button onclick="FallbackInterfaceManager.showHelp()" style="
                                padding: 12px 24px;
                                background: #28a745;
                                color: white;
                                border: none;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 14px;
                                font-weight: 500;
                                transition: background-color 0.2s;
                                min-width: 100px;
                            " onmouseover="this.style.background='#218838'" onmouseout="this.style.background='#28a745'">
                                ❓ 帮助
                            </button>
                        </div>

                        <div style="
                            margin-top: 30px;
                            padding-top: 20px;
                            border-top: 1px solid #eee;
                            font-size: 12px;
                            color: #999;
                        ">
                            如果问题持续存在，请联系技术支持<br>
                            <span id="current-time"></span>
                        </div>
                    </div>
                </div>
            `;

            document.body.innerHTML = fallbackHTML;

            // 更新当前时间
            this.updateCurrentTime();

            console.log('✅ 降级界面显示成功');

        } catch (error) {
            console.error('❌ 显示降级界面失败:', error);
            // 最后的备用方案
            this.showMinimalInterface(errorInfo);
        }
    },

    // 格式化错误信息
    formatErrorInfo(errorInfo) {
        if (typeof errorInfo === 'string') {
            return errorInfo;
        } else if (typeof errorInfo === 'object') {
            return JSON.stringify(errorInfo, null, 2);
        } else {
            return String(errorInfo);
        }
    },

    // 显示诊断信息
    showDiagnostics() {
        try {
            const diagnostics = {
                '用户代理': navigator.userAgent,
                '时间戳': new Date().toISOString(),
                '当前URL': window.location.href,
                '来源页面': document.referrer || '直接访问',
                '屏幕尺寸': `${screen.width}x${screen.height}`,
                '视窗尺寸': `${window.innerWidth}x${window.innerHeight}`,
                '语言设置': navigator.language,
                '在线状态': navigator.onLine ? '在线' : '离线',
                'Cookie启用': navigator.cookieEnabled ? '是' : '否',
                '本地存储': typeof(Storage) !== "undefined" ? '支持' : '不支持'
            };

            const diagnosticsText = Object.entries(diagnostics)
                .map(([key, value]) => `${key}: ${value}`)
                .join('\n');

            // 创建模态对话框显示诊断信息
            this.showModal('系统诊断信息', `<pre style="text-align: left; font-size: 12px; line-height: 1.4; white-space: pre-wrap;">${diagnosticsText}</pre>`);

        } catch (error) {
            console.error('获取诊断信息失败:', error);
            alert('无法获取诊断信息，请检查浏览器控制台');
        }
    },

    // 显示帮助信息
    showHelp() {
        const helpContent = `
            <div style="text-align: left; line-height: 1.6;">
                <h3 style="color: #4A90E2; margin-top: 0;">常见问题解决方案</h3>

                <h4>🔧 基本排查步骤：</h4>
                <ol>
                    <li>检查网络连接是否正常</li>
                    <li>清除浏览器缓存和Cookie</li>
                    <li>尝试使用其他浏览器访问</li>
                    <li>关闭浏览器扩展程序</li>
                    <li>检查防火墙和安全软件设置</li>
                </ol>

                <h4>🌐 网络问题：</h4>
                <ul>
                    <li>确保能够访问外部网站</li>
                    <li>检查DNS设置</li>
                    <li>尝试使用移动网络</li>
                </ul>

                <h4>💻 浏览器兼容性：</h4>
                <ul>
                    <li>推荐使用Chrome、Firefox、Edge最新版本</li>
                    <li>启用JavaScript</li>
                    <li>允许弹出窗口</li>
                </ul>

                <h4>📞 技术支持：</h4>
                <p>如果问题仍然存在，请联系技术支持并提供诊断信息。</p>
            </div>
        `;

        this.showModal('帮助信息', helpContent);
    },

    // 显示模态对话框
    showModal(title, content) {
        const modalHTML = `
            <div id="fallback-modal" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            " onclick="if(event.target === this) this.remove()">
                <div style="
                    background: white;
                    padding: 30px;
                    border-radius: 8px;
                    max-width: 600px;
                    max-height: 80vh;
                    width: 90%;
                    overflow-y: auto;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
                " onclick="event.stopPropagation()">
                    <div style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 20px;
                        border-bottom: 1px solid #eee;
                        padding-bottom: 15px;
                    ">
                        <h2 style="margin: 0; color: #333; font-size: 18px;">${title}</h2>
                        <button onclick="document.getElementById('fallback-modal').remove()" style="
                            background: none;
                            border: none;
                            font-size: 24px;
                            cursor: pointer;
                            color: #999;
                            padding: 0;
                            width: 30px;
                            height: 30px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        ">×</button>
                    </div>
                    <div style="color: #666;">
                        ${content}
                    </div>
                    <div style="
                        margin-top: 20px;
                        text-align: right;
                        border-top: 1px solid #eee;
                        padding-top: 15px;
                    ">
                        <button onclick="document.getElementById('fallback-modal').remove()" style="
                            padding: 8px 16px;
                            background: #6c757d;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                        ">关闭</button>
                    </div>
                </div>
            </div>
        `;

        // 移除现有模态框
        const existingModal = document.getElementById('fallback-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    },

    // 更新当前时间
    updateCurrentTime() {
        try {
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                const now = new Date();
                timeElement.textContent = `当前时间: ${now.toLocaleString('zh-CN')}`;

                // 每秒更新时间
                setTimeout(() => this.updateCurrentTime(), 1000);
            }
        } catch (error) {
            console.error('更新时间失败:', error);
        }
    },

    // 显示最小化界面（最后的备用方案）
    showMinimalInterface(errorInfo) {
        try {
            console.log('🚨 显示最小化降级界面');

            document.body.innerHTML = `
                <div style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100vh;
                    font-family: Arial, sans-serif;
                    background: #f0f0f0;
                    color: #333;
                    text-align: center;
                    padding: 20px;
                ">
                    <div style="
                        background: white;
                        padding: 30px;
                        border-radius: 8px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        max-width: 400px;
                    ">
                        <h1 style="color: #4A90E2; margin-bottom: 20px;">智慧校园系统</h1>
                        <p style="margin-bottom: 20px;">系统遇到问题，请重新加载页面</p>
                        ${errorInfo ? `<p style="font-size: 12px; color: #666; margin-bottom: 20px;">错误: ${errorInfo}</p>` : ''}
                        <button onclick="location.reload()" style="
                            padding: 10px 20px;
                            background: #4A90E2;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                        ">重新加载</button>
                    </div>
                </div>
            `;

        } catch (error) {
            console.error('❌ 显示最小化界面也失败:', error);
            // 最后的最后，使用alert
            alert('系统初始化失败，请刷新页面重试。错误信息: ' + (errorInfo || '未知错误'));
        }
    },

    // 检查界面是否已显示
    isShowing() {
        return document.body.innerHTML.includes('智慧校园系统') &&
               document.body.innerHTML.includes('重新加载');
    }
};

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.FallbackInterfaceManager = FallbackInterfaceManager;
}