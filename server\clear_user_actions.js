#!/usr/bin/env node
// 清理user_actions表的脚本

const mysql = require('mysql2/promise');

// 数据库配置（与server.js保持一致）
const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: '005026...', // 请根据实际情况修改密码
    database: 'campus_map',
    charset: 'utf8mb4'
};

async function clearUserActions() {
    let connection;
    
    try {
        console.log('🔗 连接到数据库...');
        connection = await mysql.createConnection(dbConfig);
        
        console.log('📊 查询当前user_actions表记录数...');
        const [countResult] = await connection.execute('SELECT COUNT(*) as count FROM user_actions');
        const currentCount = countResult[0].count;
        console.log(`📈 当前记录数: ${currentCount}`);
        
        if (currentCount === 0) {
            console.log('✅ 表已经是空的，无需清理');
            return;
        }
        
        console.log('🗑️ 开始清理user_actions表...');
        
        // 方法1：使用DELETE（保留表结构和自增ID）
        // await connection.execute('DELETE FROM user_actions');
        
        // 方法2：使用TRUNCATE（更快，重置自增ID）
        await connection.execute('TRUNCATE TABLE user_actions');
        
        console.log('✅ user_actions表清理完成！');
        
        // 验证清理结果
        const [verifyResult] = await connection.execute('SELECT COUNT(*) as count FROM user_actions');
        const finalCount = verifyResult[0].count;
        console.log(`📊 清理后记录数: ${finalCount}`);
        
        console.log('🎯 建议：同时清理相关的会话数据');
        console.log('   如需清理user_sessions表，请手动执行：');
        console.log('   TRUNCATE TABLE user_sessions;');
        
    } catch (error) {
        console.error('❌ 清理失败:', error.message);
        
        if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('💡 请检查数据库用户名和密码');
        } else if (error.code === 'ER_BAD_DB_ERROR') {
            console.log('💡 请检查数据库名称是否正确');
        } else if (error.code === 'ECONNREFUSED') {
            console.log('💡 请确保MySQL服务正在运行');
        }
        
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔌 数据库连接已关闭');
        }
    }
}

// 执行清理
console.log('🚀 开始清理user_actions表数据...');
console.log('⚠️  警告：此操作将删除所有用户行为记录数据！');

clearUserActions().then(() => {
    console.log('🏁 脚本执行完成');
    process.exit(0);
}).catch((error) => {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
});
