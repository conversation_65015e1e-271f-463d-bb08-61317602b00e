<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="智慧校园地图系统 - 提供校园建筑物查询、路径规划等功能">
    <meta name="keywords" content="校园地图,建筑物查询,路径规划,智慧校园">
    <meta name="author" content="南通大学GIS亓福祥">
    <title data-i18n="page.title">智慧校园地图系统</title>

    <!-- 外部资源引入 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/ol@7.5.2/ol.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- 图标 -->
    <link rel="icon" href="icon.ico" type="image/x-icon">

    <!-- 完整CSS样式系统 -->
    <style>
        /* CSS变量定义 - 专业浅蓝色主题系统 */
        :root {
            --primary-color: #4A90E2;
            --primary-light: #87CEEB;
            --primary-dark: #2E86AB;
            --secondary-color: #4ecdc4;
            --secondary-light: #a8e6cf;
            --success-color: #95e1d3;
            --warning-color: #fce38a;
            --error-color: #ff8a95;
            --background-color: #fef7f0;
            --surface-color: #ffffff;
            --text-color: #2d3436;
            --text-secondary: #636e72;
            --border-color: #BBDEFB;
            --shadow-color: rgba(74, 144, 226, 0.15);
            --shadow-dark: rgba(74, 144, 226, 0.25);

            /* 专业的图层颜色 */
            --buildings-color: rgba(149, 225, 211, 0.8); /* 薄荷绿 - 建筑物 */
            --roads-color: #dda0dd; /* 淡紫色 - 道路 */
            --rivers-color: rgba(135, 206, 250, 0.8); /* 天空蓝 - 河流 */
            --waters-color: rgba(173, 216, 230, 0.7); /* 浅蓝色 - 水域 */
            --traffic-color: rgba(74, 144, 226, 0.8); /* 专业蓝 - 交通设施 */
            --boundary-color: #4A90E2; /* 专业蓝 - 校园边界 */
            --highlight-color: rgba(255, 223, 186, 0.9); /* 桃色 - 高亮 */

            /* 可爱的尺寸变量 */
            --border-radius: 12px;
            --border-radius-large: 20px;
            --border-radius-xl: 25px;
            --border-radius-full: 50px;
            --padding-small: 10px;
            --padding-normal: 15px;
            --padding-large: 20px;
            --padding-xl: 25px;
            --margin-small: 6px;
            --margin-normal: 12px;
            --margin-large: 20px;
            --margin-xl: 30px;

            /* 字体大小 */
            --font-small: 12px;
            --font-normal: 14px;
            --font-large: 16px;
            --font-xlarge: 18px;
            --font-title: 20px;

            /* 动画时长 */
            --animation-fast: 0.2s;
            --animation-normal: 0.3s;
            --animation-slow: 0.5s;
            --animation-slower: 1.0s;

            /* Z-index层级 */
            --z-loading: 9999;
            --z-modal: 1000;
            --z-panel: 100;
            --z-control: 50;

            /* 专业的交互变量 */
            --primary-hover: #2E86AB;
            --surface-hover: #E3F2FD;
            --text-muted: #b2bec3;
            --border-hover: #87CEEB;
            --shadow-hover: rgba(74, 144, 226, 0.2);
            --info-color: #74b9ff;
            --cute-pink: #87CEEB;
            --cute-blue: #54a0ff;
            --cute-green: #5f27cd;
            --cute-yellow: #feca57;

            /* 扩展阴影 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

            /* 专业的渐变色 */
            --gradient-primary: linear-gradient(135deg, #4A90E2 0%, #2E86AB 100%);
            --gradient-secondary: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            --gradient-success: linear-gradient(135deg, #95e1d3 0%, #a8e6cf 100%);
            --gradient-warning: linear-gradient(135deg, #fce38a 0%, #f8b500 100%);
            --gradient-error: linear-gradient(135deg, #ff8a95 0%, #f44336 100%);
            --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
            --gradient-cute: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 50%, #90CAF9 100%);
            --gradient-sky: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            --gradient-sunset: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }

        /* 现代化动画关键帧 */
        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromRight {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromTop {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes scaleIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }

        @keyframes shimmer {
            0% { background-position: -200px 0; }
            100% { background-position: calc(200px + 100%) 0; }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 5px var(--primary-color); }
            50% { box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color); }
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
            40%, 43% { transform: translate3d(0,-8px,0); }
            70% { transform: translate3d(0,-4px,0); }
            90% { transform: translate3d(0,-2px,0); }
        }

        /* 保留核心动画效果 */
        @keyframes heartbeat {
            0% { transform: scale(1); }
            14% { transform: scale(1.1); }
            28% { transform: scale(1); }
            42% { transform: scale(1.1); }
            70% { transform: scale(1); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* 全局样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            font-family: "Noto Sans SC", "Microsoft YaHei", "PingFang SC", sans-serif;
            font-size: var(--font-normal);
            color: var(--text-color);
            background-color: var(--background-color);
            overflow: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--background-color);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-secondary);
        }

        /* 通用工具类 */
        .hidden {
            display: none !important;
        }

        .fade-in {
            animation: fadeIn var(--animation-normal) ease-in-out;
        }

        .fade-out {
            animation: fadeOut var(--animation-normal) ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        /* 可爱的加载动画样式 */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gradient-cute);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: var(--z-loading);
            transition: opacity var(--animation-slow) ease-out;
        }

        .loading-content {
            text-align: center;
            color: white;
            z-index: 2;
            animation: float 2s ease-in-out infinite;
        }

        .loading-spinner {
            position: relative;
            width: 100px;
            height: 100px;
            margin: 0 auto 40px;
        }

        .spinner-ring {
            position: absolute;
            width: 100%;
            height: 100%;
            border: 4px solid transparent;
            border-top: 4px solid rgba(255, 255, 255, 0.9);
            border-right: 4px solid rgba(74, 144, 226, 0.7);
            border-bottom: 4px solid rgba(46, 134, 171, 0.5);
            border-radius: 50%;
            animation: spin 1.5s ease-in-out infinite;
        }

        .spinner-ring:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 10px;
            left: 10px;
            animation-delay: -0.4s;
        }

        .spinner-ring:nth-child(3) {
            width: 40px;
            height: 40px;
            top: 20px;
            left: 20px;
            animation-delay: -0.8s;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 加载页面校徽样式 */
        .loading-logo-container {
            text-align: center;
            margin-bottom: var(--margin-large);
            animation: float 3s ease-in-out infinite;
        }

        .loading-logo {
            width: 64px;
            height: 64px;
            filter: drop-shadow(0 4px 8px var(--shadow-color));
            transition: transform var(--animation-normal) ease;
            will-change: transform; /* 动画性能优化 */
        }

        .loading-logo:hover {
            transform: scale(1.1) translateZ(0); /* 使用transform3d优化 */
        }

        /* 校徽加载失败降级处理 */
        .loading-logo:error,
        .login-logo:error {
            display: none;
        }



        .loading-title {
            font-size: var(--font-title);
            font-weight: 500;
            margin-bottom: var(--margin-normal);
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .loading-text {
            font-size: var(--font-normal);
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .loading-progress {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
            margin: 0 auto;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #fff, rgba(255,255,255,0.8));
            border-radius: 2px;
            animation: progress 2s ease-in-out infinite;
        }

        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        /* 加载装饰元素 */
        .loading-decoration {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .decoration-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .circle-1 {
            width: 100px;
            height: 100px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .circle-2 {
            width: 60px;
            height: 60px;
            top: 60%;
            right: 15%;
            animation-delay: -2s;
        }

        .circle-3 {
            width: 80px;
            height: 80px;
            bottom: 20%;
            left: 20%;
            animation-delay: -4s;
        }

        /* float动画已在顶部定义，此处删除重复 */

        /* 登录界面样式 */
        .login-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: var(--z-modal);
        }

        .login-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f5f5f5;
            overflow: hidden;
        }

        .login-form-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            padding: var(--padding-large);
            z-index: 2;
        }

        .login-form {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* slideUp动画已删除，采用简洁设计 */

        .login-header {
            text-align: center;
            margin-bottom: 24px;
        }

        /* 登录页面校徽样式 */
        .login-logo-container {
            text-align: center;
            margin-bottom: 16px;
            animation: float 3s ease-in-out infinite;
        }

        .login-logo {
            width: 48px;
            height: 48px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
            transition: transform var(--animation-normal) ease;
            border-radius: 4px;
        }

        .login-logo:hover {
            transform: scale(1.05);
        }



        .login-title {
            font-size: 18px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
            margin-top: 0;
        }

        .login-subtitle {
            color: #666;
            font-size: 14px;
        }

        .login-form-content {
            width: 100%;
        }

        .input-group {
            margin-bottom: 16px;
        }

        .input-label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }

        .input-field {
            width: 100%;
            padding: 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            font-family: inherit;
            transition: border-color 0.2s ease;
            background: white;
        }

        .input-field:focus {
            outline: none;
            border-color: #4A90E2;
            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
        }

        .input-field::placeholder {
            color: var(--text-secondary);
        }

        .login-actions {
            margin-top: 20px;
        }

        .login-button {
            width: 100%;
            padding: 12px;
            background: #4A90E2;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .login-button:hover {
            background: #357abd;
        }

        .login-button:active {
            background: #2e6da4;
        }

        .login-button:disabled {
            background: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .button-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .login-error {
            margin-top: 16px;
            padding: 12px;
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 4px;
            color: #e53e3e;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .error-icon {
            font-size: 16px;
        }

        /* 主界面样式 */
        .main-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--background-color);
        }

        /* 地图容器样式 */
        .map-container {
            position: relative;
            width: 100%;
            height: 100%;
            padding-top: 70px; /* 为顶部按钮留出空间 */
        }

        .map {
            width: 100%;
            height: 100%;
            background: #f0f0f0;
        }

        .map-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--surface-color);
            padding: var(--padding-large);
            border-radius: var(--border-radius);
            box-shadow: 0 4px 12px var(--shadow-color);
            z-index: var(--z-control);
        }

        .map-loading-content {
            display: flex;
            align-items: center;
            gap: var(--margin-normal);
            color: var(--text-color);
        }

        .loading-spinner-small {
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* 鼠标位置显示样式 */
        .mouse-position {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 4px 8px;
            border-radius: var(--border-radius);
            font-size: var(--font-small);
            font-family: monospace;
            color: var(--text-color);
            border: 1px solid var(--border-color);
            box-shadow: 0 2px 4px var(--shadow-color);
            z-index: var(--z-control);
            pointer-events: none;
        }

        /* 控制面板样式 */
        .control-panel {
            position: absolute;
            top: var(--padding-large);
            left: var(--padding-large);
            z-index: var(--z-control);
            display: flex;
            flex-direction: column;
            gap: var(--margin-normal);
            max-width: 250px;
        }

        /* 可爱的搜索组件样式 */
        .search-container {
            position: relative;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .search-input-wrapper:hover {
            border-color: #4A90E2;
        }

        .search-input-wrapper:focus-within {
            border-color: #4A90E2;
            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
        }

        .search-input {
            width: 100%;
            padding: 8px 12px;
            padding-right: 60px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            font-family: inherit;
            background: transparent;
            color: #333;
        }

        .search-input:focus {
            outline: none;
        }

        .search-input::placeholder {
            color: #999;
        }

        .search-clear {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: transparent;
            border: none;
            color: #666;
            cursor: pointer;
            padding: 6px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .search-clear:hover {
            color: #333;
        }

        .search-results {
            position: absolute;
            top: calc(100% + 4px);
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            max-height: 320px;
            overflow-y: auto;
            z-index: 1000;
        }

        .search-results-header {
            padding: var(--padding-small) var(--padding-normal);
            background: var(--background-color);
            border-bottom: 1px solid var(--border-color);
            font-size: var(--font-small);
            color: var(--text-secondary);
            font-weight: 500;
        }

        .search-results-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .search-results-list li {
            padding: var(--padding-normal);
            cursor: pointer;
            border-bottom: 1px solid var(--border-color);
            transition: background-color var(--animation-fast) ease;
        }

        .search-results-list li:hover {
            background: var(--background-color);
        }

        .search-results-list li:last-child {
            border-bottom: none;
        }

        .search-no-results {
            padding: var(--padding-large);
            text-align: center;
            color: var(--text-secondary);
            font-style: italic;
        }

        /* 搜索结果项样式 - 现代化美化 */
        .search-result-item {
            padding: var(--padding-normal) var(--padding-large);
            cursor: pointer;
            border-bottom: 1px solid var(--border-color);
            transition: all var(--animation-normal) ease;
            position: relative;
            overflow: hidden;
        }

        .search-result-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 0;
            height: 100%;
            background: var(--gradient-primary);
            transition: width var(--animation-normal) ease;
            z-index: 0;
        }

        .search-result-item:hover::before,
        .search-result-item.highlighted::before {
            width: 4px;
        }

        .search-result-item:hover,
        .search-result-item.highlighted {
            background: var(--surface-hover);
            transform: translateX(4px);
            box-shadow: var(--shadow-md);
        }

        /* 搜索结果按钮样式 */
        .result-buttons {
            display: flex;
            gap: var(--margin-small);
            margin-top: var(--margin-small);
            padding-top: var(--padding-small);
            border-top: 1px solid var(--color-border);
        }

        .result-btn {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 6px 12px;
            border: 1px solid var(--color-border);
            border-radius: var(--border-radius-small);
            background: var(--color-bg);
            color: var(--color-text);
            font-size: var(--font-small);
            cursor: pointer;
            transition: all 0.2s ease;
            flex: 1;
            justify-content: center;
        }

        .result-btn:hover {
            background: var(--color-primary-light);
            border-color: var(--color-primary);
            color: var(--color-primary);
        }

        .result-btn-view {
            border-color: var(--color-info);
            color: var(--color-info);
        }

        .result-btn-view:hover {
            background: var(--color-info);
            color: white;
        }

        .result-btn-nav {
            border-color: var(--color-success);
            color: var(--color-success);
        }

        .result-btn-nav:hover {
            background: var(--color-success);
            color: white;
        }

        .result-btn .btn-icon {
            font-size: 14px;
        }

        .search-result-item:last-child {
            border-bottom: none;
            border-radius: 0 0 6px 6px; /* 更方正的搜索结果圆角 */
        }

        .search-result-item:first-child {
            border-radius: 6px 6px 0 0; /* 更方正的搜索结果圆角 */
        }

        .result-name {
            font-size: var(--font-normal);
            font-weight: 500;
            color: var(--text-color);
            margin-bottom: var(--margin-small);
        }

        .result-info {
            font-size: var(--font-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }

        /* 建筑物信息面板样式 - 右侧显示，避免遮挡建筑物，极致紧凑设计 */
        .building-info-panel {
            position: fixed;
            top: 40%; /* 进一步上移 */
            right: 15px; /* 稍微靠近边缘 */
            transform: translateY(-50%) translateX(100%);
            width: 220px; /* 极致缩小宽度 */
            max-width: 20vw; /* 极致缩小最大宽度 */
            height: auto; /* 自动高度 */
            max-height: 40vh; /* 极致缩小最大高度 */
            background: var(--surface-color);
            border: 1px solid #ccc; /* 简化边框 */
            border-radius: 3px; /* 极致减小圆角 */
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08); /* 极致减小阴影 */
            z-index: 2000;
            opacity: 0;
            visibility: hidden;
            transition: all var(--animation-slower) cubic-bezier(0.4, 0, 0.2, 1);
            overflow: visible; /* 确保不滚动 */
        }

        .building-info-panel.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(-50%) translateX(0);
        }

        .building-info-panel.hide {
            opacity: 0;
            visibility: hidden;
            transform: translateY(-50%) translateX(100%);
        }

        /* 响应式设计：小屏幕时移到底部 */
        @media (max-width: 768px) {
            .building-info-panel {
                top: auto;
                bottom: 20px;
                right: 20px;
                left: 20px;
                width: auto;
                max-width: none;
                transform: translateY(100%);
            }

            .building-info-panel.show {
                transform: translateY(0);
            }

            .building-info-panel.hide {
                transform: translateY(100%);
            }
        }

        .building-info-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 8px; /* 极致减小内边距 */
            background: var(--gradient-primary);
            color: white;
            border-radius: 3px 3px 0 0; /* 极致减小圆角 */
            position: relative;
        }

        .building-info-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-glass);
            backdrop-filter: blur(10px);
        }

        .building-info-header h3 {
            margin: 0;
            font-size: 13px; /* 极致减小字体大小 */
            font-weight: 500; /* 进一步减小字体粗细 */
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .building-info-close {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.2);
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: var(--border-radius-full);
            transition: all var(--animation-normal) ease;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1;
        }

        .building-info-close:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.4);
            transform: scale(1.1) rotate(90deg);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .building-info-content {
            padding: 6px; /* 极致减小内边距 */
            background: var(--surface-color);
            position: relative;
        }

        .building-info-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-glass);
            pointer-events: none;
        }

        .building-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center; /* 居中对齐 */
            margin-bottom: 3px; /* 极致减小间距 */
            padding: 3px 6px; /* 极致减小内边距 */
            border-bottom: 1px solid #eee; /* 简化边框 */
            border-radius: 2px; /* 极致减小圆角 */
            background: rgba(255, 255, 255, 0.6); /* 简化背景 */
            transition: all var(--animation-normal) ease;
            position: relative;
            z-index: 1;
        }

        .building-info-item:hover {
            background: var(--primary-light);
            transform: translateX(4px);
            box-shadow: var(--shadow-md);
        }

        .building-info-item:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }

        .building-info-label {
            font-weight: 500; /* 减小字体粗细 */
            color: var(--text-secondary);
            min-width: 60px; /* 减小最小宽度 */
            font-size: 11px; /* 极致减小字体大小 */
            position: relative;
            z-index: 1;
        }

        .building-info-value {
            color: var(--text-color);
            font-size: 11px; /* 极致减小字体大小 */
            text-align: right;
            flex: 1;
            margin-left: 8px; /* 减小间距 */
            word-break: break-word;
            font-weight: 400; /* 减小字体粗细 */
            position: relative;
            z-index: 1;
        }

        /* 高亮图层动画样式 */
        .highlight-layer {
            animation: highlightPulse 2s ease-in-out infinite;
        }

        @keyframes highlightPulse {
            0% {
                opacity: 0.8;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.02);
            }
            100% {
                opacity: 0.8;
                transform: scale(1);
            }
        }

        /* 渐入渐出动画 - 已移至顶部统一定义 */

        .fade-in {
            animation: fadeIn 1s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        .fade-out {
            animation: fadeOut 1s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        /* 简洁的功能按钮组样式 - 横向排列在顶部 */
        .function-buttons {
            position: fixed;
            top: 20px;
            left: 50%; /* 完全居中 */
            transform: translateX(-50%);
            display: flex;
            flex-direction: row;
            gap: 8px;
            background: rgba(255, 255, 255, 0.95);
            padding: 8px 12px;
            border-radius: 25px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            z-index: 1000;
            animation: slideInFromTop var(--animation-normal) ease-out;
        }

        .function-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 8px 12px;
            background: transparent;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 18px;
            color: #666;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
            min-width: auto;
            font-weight: 500;
        }

        .function-btn:hover {
            background: #f0f0f0;
            border-color: #ddd;
            color: #333;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .function-btn:active {
            transform: translateY(0);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }

        /* 搜索框内的设置按钮样式 */
        .search-settings-btn {
            position: absolute;
            right: 40px;
            top: 50%;
            transform: translateY(-50%);
            background: transparent;
            border: none;
            color: #666;
            cursor: pointer;
            padding: 6px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .search-settings-btn:hover {
            color: #333;
        }

        /* 路径规划提醒模态框样式 */
        .route-planning-reminder-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease;
        }

        .reminder-modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .reminder-modal-content {
            position: relative;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            animation: slideInUp 0.4s ease;
        }

        .reminder-header {
            padding: 24px 24px 16px;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
        }

        .reminder-icon {
            font-size: 48px;
            margin-bottom: 12px;
        }

        .reminder-title {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
        }

        .reminder-body {
            padding: 24px;
        }

        .reminder-message {
            font-size: 16px;
            color: #34495e;
            margin: 0 0 16px 0;
            text-align: center;
        }

        .reminder-instruction {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px;
            border-radius: 12px;
            margin: 16px 0;
            font-size: 14px;
            line-height: 1.5;
        }

        .reminder-steps {
            margin-top: 20px;
        }

        .step-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .step-item:hover {
            background: #e9ecef;
            transform: translateX(4px);
        }

        .step-number {
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .step-text {
            font-size: 14px;
            color: #495057;
            font-weight: 500;
        }

        .reminder-actions {
            padding: 16px 24px 24px;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .reminder-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .reminder-btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .reminder-btn-secondary:hover {
            background: #e9ecef;
            color: #495057;
        }

        .reminder-btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .reminder-btn-primary:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        /* fadeIn动画已在顶部定义，此处删除重复 */

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* 设置面板模态框样式 - 方正简洁设计 */
        .settings-modal-content {
            max-width: 400px;
            width: 90%;
            border-radius: 4px;
        }

        .settings-section {
            margin-bottom: 20px;
            padding: 16px;
            background: #ffffff;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .settings-section-title {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .settings-item {
            margin-bottom: 12px;
        }

        .settings-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .language-selector, .theme-selector {
            display: flex;
            gap: 8px;
        }

        .language-option, .theme-option {
            flex: 1;
            padding: 10px 14px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            font-size: 14px;
            font-weight: 500;
        }

        .language-option:hover, .theme-option:hover {
            border-color: #4A90E2;
            background: #f5f5f5;
        }

        .language-option.active, .theme-option.active {
            border-color: #4A90E2;
            background: #4A90E2;
            color: white;
        }

        .lang-flag, .theme-icon {
            font-size: 16px;
        }

        .lang-name, .theme-name {
            font-weight: 500;
        }

        .btn-icon {
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .function-btn:hover .btn-icon {
            transform: scale(1.1);
        }

        .btn-text {
            font-weight: 500;
            font-size: 13px;
            transition: all 0.2s ease;
        }

        .function-btn:hover .btn-text {
            color: #333;
        }

        /* 面板通用样式 - 简洁设计，全部放在左侧，优化尺寸避免突兀 */
        .route-planning-panel,
        .layer-control-panel,
        .weather-panel,
        .distance-measure-panel {
            position: absolute;
            left: 20px;
            width: 220px; /* 稍微增加宽度 */
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
            z-index: var(--z-panel);
            animation: slideInFromLeft 0.3s ease-out;
            overflow: visible;
        }

        /* 各面板垂直位置设置 - 向上移动，进一步压缩高度 */
        .route-planning-panel {
            top: 120px;
            height: auto;
            max-height: 120px; /* 进一步压缩高度 */
            width: 220px;
        }

        .layer-control-panel {
            top: 120px;
            height: auto;
            max-height: 130px; /* 进一步压缩高度 */
        }

        .weather-panel {
            top: 120px; /* 下移与其他面板平齐 */
            height: auto;
            max-height: 100px; /* 保持高度不变 */
            width: 240px; /* 增加宽度 */
        }

        .distance-measure-panel {
            top: 160px;
            height: auto;
            max-height: 110px; /* 进一步压缩高度 */
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .panel-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px; /* 进一步缩小内边距 */
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
            color: #333;
        }

        .panel-title {
            font-size: 12px; /* 进一步缩小字体 */
            font-weight: 500;
            color: #333;
            margin: 0;
        }

        .panel-close {
            background: transparent;
            border: 1px solid #ddd;
            font-size: 14px; /* 进一步缩小字体 */
            color: #666;
            cursor: pointer;
            padding: 3px; /* 进一步缩小内边距 */
            border-radius: 4px;
            transition: all 0.2s ease;
            width: 20px; /* 进一步缩小尺寸 */
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .panel-close:hover {
            background: #f0f0f0;
            border-color: #ccc;
            color: #333;
        }

        .panel-content {
            padding: 4px; /* 进一步减小内边距 */
            background: white;
            font-size: 10px; /* 进一步减小字体大小 */
        }

        /* 测距工具面板样式 */
        .distance-instructions {
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            color: #666;
            font-size: 14px;
        }

        .distance-controls {
            margin-bottom: 15px;
        }

        .distance-result {
            padding: 4px; /* 进一步缩小内边距 */
            background: #e8f4fd;
            border-radius: 2px; /* 更方正的圆角 */
            border-left: 2px solid #4A90E2; /* 进一步缩小边框 */
            font-size: 8px; /* 进一步缩小字体 */
            max-width: 160px; /* 限制最大宽度 */
            max-height: 80px; /* 限制最大高度 */
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .result-label {
            font-weight: 500;
            color: #333;
            font-size: 7px; /* 进一步缩小字体 */
        }

        .result-value {
            font-weight: 600;
            color: #4A90E2;
            font-size: 7px; /* 进一步缩小字体 */
        }

        .form-button {
            background: #4A90E2;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 8px;
            transition: all 0.2s ease;
        }

        .form-button:hover {
            background: #357abd;
        }

        .form-button.secondary {
            background: #6c757d;
        }

        .form-button.secondary:hover {
            background: #5a6268;
        }

        /* 天气日期样式 */
        .weather-date {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px; /* 进一步缩小间距 */
            padding: 4px 0; /* 进一步缩小内边距 */
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .weather-date-text {
            font-size: 9px; /* 进一步缩小日期字体 */
            font-weight: 500;
            color: #666666;
        }

        .weather-refresh-btn {
            background: rgba(116, 185, 255, 0.1);
            border: 1px solid rgba(116, 185, 255, 0.3);
            border-radius: 50%;
            width: 20px; /* 进一步缩小尺寸 */
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 9px; /* 进一步缩小字体 */
            transition: all 0.3s ease;
        }

        .weather-refresh-btn:hover {
            background: rgba(116, 185, 255, 0.2);
            border-color: rgba(116, 185, 255, 0.5);
            transform: rotate(180deg);
        }

        .weather-refresh-btn:active {
            transform: rotate(360deg);
        }

        /* 温度组样式 */
        .weather-temp-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 10px;
        }

        .weather-current-temp {
            font-size: 28px; /* 缩小主温度字体 */
            font-weight: bold;
            line-height: 1;
            margin-bottom: 5px;
            color: #333333;
        }

        .weather-temp-range {
            font-size: 13px; /* 缩小温度范围字体 */
            color: #666666;
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .temp-high {
            font-weight: 600;
            color: #d63031;
        }

        .temp-low {
            color: #0984e3;
        }

        .temp-separator {
            margin: 0 2px;
            color: #999999;
        }

        /* 温度图表样式 */
        .weather-chart {
            margin: 20px 0;
            padding: 15px;
            background: rgba(116, 185, 255, 0.05);
            border: 1px solid rgba(116, 185, 255, 0.2);
            border-radius: 8px;
        }

        .chart-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 10px;
            text-align: center;
            color: #333333;
        }

        .chart-container {
            display: flex;
            justify-content: center;
            align-items: center;
            background: rgba(116, 185, 255, 0.02);
            border-radius: 6px;
            padding: 10px;
        }

        #temperature-chart {
            border-radius: 4px;
        }

        /* 天气详情增强样式 */
        .weather-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-top: 15px;
        }

        .weather-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            background: rgba(116, 185, 255, 0.05);
            border-radius: 6px;
        }

        .weather-label {
            font-size: 10px; /* 缩小标签字体 */
            color: #0066FF;
            margin-bottom: 4px;
        }

        .weather-value {
            font-size: 12px; /* 缩小数值字体 */
            font-weight: 600;
            color: #0066FF;
        }

        .weather-update-time {
            grid-column: 1 / -1;
            text-align: center;
            margin-top: 8px;
            padding-top: 12px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        /* 天气加载状态 */
        .weather-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            text-align: center;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        .loading-spinner-small {
            width: 24px;
            height: 24px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 10px;
        }

        /* spin动画已在顶部定义，此处删除重复 */

        .loading-text {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }

        /* 天气主要内容 */
        .weather-main {
            display: flex;
            align-items: center;
            padding: 6px; /* 进一步减小内边距 */
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px; /* 进一步减小圆角 */
            margin-bottom: 6px; /* 进一步减小下边距 */
        }

        .weather-icon {
            font-size: 22px; /* 进一步缩小天气图标 */
            margin-right: 8px; /* 进一步减小右边距 */
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .weather-info {
            flex: 1;
        }

        .weather-temp {
            font-size: 18px; /* 进一步缩小温度字体 */
            font-weight: bold;
            margin-bottom: 2px; /* 进一步缩小间距 */
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }



        /* 天气详细信息 */
        .weather-details {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px; /* 进一步缩小圆角 */
            padding: 6px; /* 进一步缩小内边距 */
            margin-bottom: 8px; /* 进一步缩小下边距 */
        }

        .weather-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 3px 0; /* 进一步缩小内边距 */
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .weather-item:last-child {
            border-bottom: none;
        }

        .weather-label {
            font-size: 9px; /* 进一步缩小字体 */
            color: #0066FF;
        }

        .weather-value {
            font-size: 9px; /* 进一步缩小字体 */
            font-weight: 500;
            color: #0066FF;
        }

        /* 天气位置信息 */
        .weather-location {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            font-size: 14px;
        }

        .location-icon {
            margin-right: 8px;
            font-size: 16px;
        }

        .location-text {
            color: rgba(255, 255, 255, 0.9);
        }

        /* 天气错误状态 */
        .weather-error {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            text-align: center;
        }

        .error-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .error-text {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            margin-bottom: 20px;
        }

        .weather-retry-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .weather-retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-1px);
        }

        /* 天气状态指示和控制区域 */
        .weather-status-section {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .weather-status-info {
            margin-bottom: 12px;
        }

        .weather-countdown {
            font-size: 13px;
            color: #666666;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
        }

        .weather-countdown::before {
            content: "⏰";
            margin-right: 6px;
            font-size: 12px;
        }



        .weather-controls {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }



        /* 图层控制样式 */
        .layer-group-title {
            font-size: var(--font-normal);
            font-weight: 500;
            margin-bottom: var(--margin-normal);
            color: var(--text-color);
        }

        .layer-items {
            display: flex;
            flex-direction: column;
            gap: var(--margin-small);
        }

        .layer-item {
            display: flex;
            align-items: center;
            gap: var(--margin-normal);
            padding: var(--padding-normal);
            border-radius: var(--border-radius-lg);
            cursor: pointer;
            transition: all var(--animation-normal) ease;
            position: relative;
            z-index: 1;
            border: 2px solid transparent;
        }

        .layer-item:hover {
            background: var(--surface-hover);
            border-color: var(--primary-light);
            transform: translateX(4px);
            box-shadow: var(--shadow-md);
        }

        .layer-item input[type="checkbox"] {
            margin: 0;
        }

        .layer-name {
            flex: 1;
            font-size: var(--font-normal);
            color: var(--text-color);
        }

        .layer-color {
            width: 20px;
            height: 20px;
            border-radius: var(--border-radius-full);
            border: 2px solid white;
            box-shadow: var(--shadow-md);
            transition: all var(--animation-normal) ease;
            position: relative;
        }

        .layer-item:hover .layer-color {
            transform: scale(1.2);
            box-shadow: var(--shadow-lg);
        }

        .layer-color::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: var(--border-radius-full);
            border: 2px solid transparent;
            transition: border-color var(--animation-normal) ease;
        }

        .layer-item:hover .layer-color::after {
            border-color: var(--primary-color);
        }

        /* 图层颜色指示器 */
        .buildings-color { background: var(--buildings-color); }
        .roads-color { background: var(--roads-color); }
        .rivers-color { background: var(--rivers-color); }
        .waters-color { background: var(--waters-color); }
        .traffic-color { background: var(--traffic-color); }
        .boundary-color { background: var(--boundary-color); }

        /* 建筑物分类二级显示样式 */
        .layer-item-parent {
            background: var(--gradient-primary);
            color: white;
            font-weight: 600;
            border: 2px solid var(--primary-color);
        }

        .layer-item-parent:hover {
            background: var(--primary-dark);
            transform: translateX(4px);
            box-shadow: var(--shadow-md);
        }

        .layer-item-parent .layer-name {
            color: white;
        }

        .layer-toggle {
            margin-left: var(--margin-normal);
            font-size: var(--font-large);
            transition: transform var(--animation-normal) ease;
            color: white;
            cursor: pointer;
        }

        .layer-toggle.collapsed {
            transform: rotate(-90deg);
        }

        .layer-sub-items {
            margin-left: var(--margin-large);
            border-left: 2px solid var(--border-color);
            padding-left: var(--padding-normal);
            transition: all var(--animation-normal) ease;
            overflow: hidden;
            max-height: 1000px;
            opacity: 1;
        }

        .layer-sub-items.collapsed {
            max-height: 0;
            opacity: 0;
            margin: 0;
            padding: 0;
        }

        .layer-item-sub {
            background: var(--surface-color);
            border: 1px solid var(--border-light);
            margin: var(--margin-xs) 0;
            padding: var(--padding-xs) var(--padding-normal);
            font-size: var(--font-small);
        }

        .layer-item-sub:hover {
            background: var(--surface-hover);
            border-color: var(--primary-light);
            transform: translateX(4px);
        }

        .layer-item-sub .layer-name {
            font-size: var(--font-small);
            font-weight: 400;
        }

        .layer-item-sub .layer-color {
            width: 16px;
            height: 16px;
            border-width: 1px;
        }

        /* 🗑️ 已删除静态建筑物点击信息面板样式 */

        /* 路径提示样式 */
        .route-notice {
            display: flex;
            align-items: flex-start;
            gap: var(--margin-normal);
            margin-top: var(--margin-large);
            padding: var(--padding-large);
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #ffc107;
            border-radius: var(--border-radius-large);
            box-shadow: var(--shadow-md);
        }

        .route-notice .notice-icon {
            font-size: 24px;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .route-notice .notice-content {
            flex: 1;
        }

        .route-notice .notice-title {
            font-weight: 600;
            color: #856404;
            font-size: var(--font-normal);
            margin-bottom: var(--margin-small);
        }

        .route-notice .notice-text {
            color: #856404;
            font-size: var(--font-normal);
            line-height: 1.5;
            font-weight: 500;
        }

        /* 路径位置提示样式 */
        .route-location-notice {
            display: flex;
            align-items: center;
            gap: var(--margin-normal);
            margin-top: var(--margin-normal);
            padding: var(--padding-normal);
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border: 2px solid #4caf50;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
        }

        .route-location-notice .notice-icon {
            font-size: 20px;
            flex-shrink: 0;
        }

        .route-location-notice .notice-content {
            flex: 1;
        }

        .route-location-notice .notice-text {
            color: #2e7d32;
            font-size: var(--font-normal);
            font-weight: 600;
            margin: 0;
        }

        /* 导航提示对话框样式 */
        .navigation-prompt-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            animation: fadeIn 0.3s ease;
        }

        .navigation-prompt-content {
            background: var(--color-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            animation: slideIn 0.3s ease;
        }

        .prompt-header {
            display: flex;
            align-items: center;
            gap: var(--margin-normal);
            padding: var(--padding-large);
            border-bottom: 1px solid var(--color-border);
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }

        .prompt-icon {
            font-size: 24px;
            flex-shrink: 0;
        }

        .prompt-title {
            flex: 1;
            margin: 0;
            color: var(--color-text);
            font-size: var(--font-large);
            font-weight: 600;
        }

        .prompt-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--color-text-secondary);
            padding: 4px;
            border-radius: var(--border-radius-small);
            transition: all 0.2s ease;
        }

        .prompt-close:hover {
            background: rgba(0, 0, 0, 0.1);
            color: var(--color-text);
        }

        .prompt-body {
            padding: var(--padding-large);
        }

        .prompt-message {
            margin: 0 0 var(--margin-normal) 0;
            font-size: var(--font-normal);
            color: var(--color-text);
            font-weight: 500;
        }

        .prompt-instruction {
            margin: 0 0 var(--margin-large) 0;
            font-size: var(--font-normal);
            color: var(--color-text-secondary);
            line-height: 1.5;
        }

        .prompt-tips {
            display: flex;
            flex-direction: column;
            gap: var(--margin-small);
        }

        .tip-item {
            display: flex;
            align-items: center;
            gap: var(--margin-small);
            padding: var(--padding-small);
            background: var(--surface-hover);
            border-radius: var(--border-radius-small);
        }

        .tip-icon {
            font-size: 16px;
            flex-shrink: 0;
        }

        .tip-text {
            font-size: var(--font-small);
            color: var(--color-text-secondary);
        }

        .prompt-actions {
            display: flex;
            gap: var(--margin-normal);
            padding: var(--padding-large);
            border-top: 1px solid var(--color-border);
            justify-content: flex-end;
        }

        .prompt-btn {
            padding: var(--padding-normal) var(--padding-large);
            border: 1px solid var(--color-border);
            border-radius: var(--border-radius);
            background: var(--color-bg);
            color: var(--color-text);
            font-size: var(--font-normal);
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 100px;
        }

        .prompt-btn-cancel {
            border-color: var(--color-error);
            color: var(--color-error);
        }

        .prompt-btn-cancel:hover {
            background: var(--color-error);
            color: white;
        }

        .prompt-btn-confirm {
            border-color: var(--color-success);
            color: var(--color-success);
        }

        .prompt-btn-confirm:hover {
            background: var(--color-success);
            color: white;
        }

        /* 搜索消息提示样式 */
        .search-message {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            padding: var(--padding-normal);
            border-radius: var(--border-radius);
            margin-top: var(--margin-small);
            font-size: var(--font-small);
            z-index: 1000;
            animation: slideDown 0.3s ease;
        }

        .search-message-info {
            background: var(--color-info-light);
            color: var(--color-info);
            border: 1px solid var(--color-info);
        }

        .search-message-error {
            background: var(--color-error-light);
            color: var(--color-error);
            border: 1px solid var(--color-error);
        }

        /* fadeIn动画已在顶部定义，此处删除重复 */

        @keyframes slideIn {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes slideDown {
            from { transform: translateY(-10px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        /* Toast提示组件样式 */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: var(--margin-normal);
            pointer-events: none;
            max-width: 400px;
        }

        .toast {
            display: flex;
            align-items: center;
            gap: var(--margin-normal);
            padding: var(--padding-normal) var(--padding-large);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(20px);
            border: 2px solid transparent;
            pointer-events: auto;
            transform: translateX(100%);
            opacity: 0;
            transition: all var(--animation-normal) cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            min-width: 300px;
            max-width: 400px;
        }

        .toast::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left var(--animation-slow) ease;
        }

        .toast:hover::before {
            left: 100%;
        }

        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }

        .toast.hide {
            transform: translateX(100%);
            opacity: 0;
        }

        .toast-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-color: var(--info-color);
            color: #1565c0;
        }

        .toast-success {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-color: #4caf50;
            color: #2e7d32;
        }

        .toast-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-color: #ffc107;
            color: #856404;
        }

        .toast-error {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            border-color: #f44336;
            color: #c62828;
        }

        .toast-icon {
            font-size: 20px;
            flex-shrink: 0;
            animation: bounce 0.6s ease-out;
        }

        .toast-content {
            flex: 1;
            min-width: 0;
        }

        .toast-message {
            font-size: var(--font-normal);
            font-weight: 500;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .toast-close {
            background: none;
            border: none;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            color: inherit;
            opacity: 0.7;
            padding: 4px;
            border-radius: var(--border-radius);
            transition: all var(--animation-fast) ease;
            flex-shrink: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .toast-close:hover {
            opacity: 1;
            background: rgba(0, 0, 0, 0.1);
            transform: scale(1.1);
        }

        /* Toast动画效果 */
        @keyframes toastSlideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes toastSlideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .toast-container {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
            }

            .toast {
                min-width: auto;
                max-width: none;
            }
        }

        /* 指北针样式 */
        .compass-container {
            position: absolute;
            top: var(--margin-large);
            right: var(--margin-large);
            z-index: 1000;
            width: 80px;
            height: 80px;
            background: var(--color-bg);
            border-radius: 50%;
            box-shadow: var(--shadow-lg);
            border: 2px solid var(--color-border);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .compass-container:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-xl);
        }

        .compass-outer {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            position: relative;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid var(--color-border);
        }

        .compass-inner {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .compass-needle {
            position: absolute;
            width: 60%;
            height: 60%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.5s ease;
        }

        .needle-north {
            position: absolute;
            top: 8px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            font-weight: bold;
            color: var(--color-error);
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .needle-pointer {
            width: 3px;
            height: 50%;
            background: linear-gradient(to bottom, var(--color-error) 0%, var(--color-error) 50%, var(--color-text-secondary) 50%, var(--color-text-secondary) 100%);
            border-radius: 2px;
            position: relative;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }

        .needle-pointer::before {
            content: '';
            position: absolute;
            top: -4px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 8px solid var(--color-error);
        }

        .needle-pointer::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 8px solid var(--color-text-secondary);
        }

        .compass-directions {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .direction {
            position: absolute;
            font-size: 10px;
            font-weight: bold;
            color: var(--color-text);
            text-shadow: 0 1px 2px rgba(255,255,255,0.8);
        }

        .direction.north {
            top: 6px;
            left: 50%;
            transform: translateX(-50%);
            color: var(--color-error);
        }

        .direction.east {
            right: 6px;
            top: 50%;
            transform: translateY(-50%);
        }

        .direction.south {
            bottom: 6px;
            left: 50%;
            transform: translateX(-50%);
        }

        .direction.west {
            left: 6px;
            top: 50%;
            transform: translateY(-50%);
        }

        /* 指北针中心点 */
        .compass-inner::after {
            content: '';
            position: absolute;
            width: 8px;
            height: 8px;
            background: var(--color-primary);
            border-radius: 50%;
            border: 2px solid var(--color-bg);
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
            z-index: 10;
        }

        /* 路径规划样式 */
        .route-inputs {
            display: flex;
            flex-direction: column;
            gap: 6px; /* 缩小间距 */
            margin-bottom: 8px; /* 缩小下边距 */
        }

        /* 地图点击开关样式 */
        .map-click-toggle {
            margin: var(--margin-large) 0;
            padding: var(--padding-normal);
            background: var(--surface-hover);
            border-radius: var(--border-radius-lg);
            border: 2px solid var(--border-color);
            transition: all var(--animation-normal) ease;
        }

        .map-click-toggle:hover {
            border-color: var(--primary-light);
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
        }

        .toggle-group {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: var(--margin-normal);
        }

        .toggle-label {
            flex: 1;
            cursor: pointer;
        }

        .toggle-text {
            display: block;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--margin-xs);
            font-size: var(--font-normal);
        }

        .toggle-description {
            display: block;
            font-size: var(--font-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }

        .toggle-switch {
            position: relative;
            display: flex;
            align-items: center;
        }

        .toggle-input {
            display: none;
        }

        .toggle-slider {
            position: relative;
            width: 80px;
            height: 36px;
            background: var(--border-color);
            border-radius: 18px;
            cursor: pointer;
            transition: all var(--animation-normal) ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 8px;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .toggle-button {
            position: absolute;
            top: 3px;
            left: 3px;
            width: 30px;
            height: 30px;
            background: white;
            border-radius: 50%;
            transition: all var(--animation-normal) ease;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
            z-index: 2;
        }

        .toggle-on,
        .toggle-off {
            font-size: 10px;
            font-weight: 600;
            color: white;
            z-index: 1;
            transition: opacity var(--animation-normal) ease;
        }

        .toggle-on {
            opacity: 0;
        }

        .toggle-off {
            opacity: 1;
        }

        .toggle-input:checked + .toggle-slider {
            background: var(--success-color);
        }

        .toggle-input:checked + .toggle-slider .toggle-button {
            transform: translateX(44px);
        }

        .toggle-input:checked + .toggle-slider .toggle-on {
            opacity: 1;
        }

        .toggle-input:checked + .toggle-slider .toggle-off {
            opacity: 0;
        }

        .toggle-slider:hover {
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.15), 0 0 0 3px rgba(25, 118, 210, 0.1);
        }

        .toggle-input:checked + .toggle-slider:hover {
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.15), 0 0 0 3px rgba(76, 175, 80, 0.1);
        }

        .route-input-group {
            position: relative;
        }

        .route-label {
            display: block;
            margin-bottom: 3px; /* 缩小间距 */
            font-weight: 500;
            color: var(--text-color);
            font-size: 10px; /* 缩小字体 */
        }

        .route-input {
            width: 100%;
            padding: 6px; /* 缩小内边距 */
            padding-right: 25px; /* 缩小右边距 */
            border: 1px solid var(--border-color); /* 缩小边框 */
            border-radius: 4px; /* 缩小圆角 */
            font-size: 10px; /* 缩小字体 */
            font-family: inherit;
            transition: all var(--animation-fast) ease;
        }

        .route-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
        }

        .route-clear {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 4px;
            border-radius: 50%;
            transition: all var(--animation-fast) ease;
        }

        .route-clear:hover {
            background: var(--border-color);
            color: var(--text-color);
        }

        .route-actions {
            display: flex;
            gap: var(--margin-normal);
            margin-bottom: var(--margin-normal);
        }

        .route-btn {
            flex: 1;
            padding: 6px 8px; /* 缩小内边距 */
            border: 1px solid transparent; /* 缩小边框 */
            border-radius: 4px; /* 缩小圆角 */
            font-size: 9px; /* 缩小字体 */
            font-weight: 600;
            cursor: pointer;
            transition: all var(--animation-normal) ease;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .route-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left var(--animation-slow) ease;
        }

        .route-btn:hover::before {
            left: 100%;
        }

        .route-btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .route-btn-primary:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-light);
        }

        .route-btn-secondary {
            background: var(--surface-hover);
            color: var(--text-color);
            border-color: var(--border-hover);
            box-shadow: var(--shadow-sm);
        }

        .route-btn-secondary:hover {
            background: var(--gradient-warning);
            color: white;
            transform: translateY(-2px) scale(1.02);
            box-shadow: var(--shadow-lg);
        }

        .route-result {
            padding: 4px; /* 进一步缩小内边距 */
            background: var(--background-color);
            border-radius: 2px; /* 更方正的圆角 */
            border: 1px solid var(--border-color);
            transition: all var(--animation-normal) ease;
            animation: slideInFromBottom var(--animation-normal) ease-out;
            font-size: 8px; /* 进一步缩小字体 */
            max-width: 180px; /* 限制最大宽度 */
            max-height: 120px; /* 限制最大高度 */
            overflow-y: auto; /* 允许滚动 */
        }

        .route-result.result-success {
            border-color: var(--success-color);
            background: var(--surface-hover);
            box-shadow: var(--shadow-lg);
            transform: scale(1.02);
        }

        .route-info {
            display: flex;
            flex-direction: column;
            gap: 3px; /* 进一步缩小间距 */
        }

        .route-distance,
        .route-time {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .info-label {
            font-weight: 500;
            color: var(--text-color);
            font-size: 7px; /* 进一步缩小字体 */
        }

        .info-value {
            color: var(--primary-color);
            font-weight: 500;
            font-size: 7px; /* 进一步缩小字体 */
        }

        /* 路径选项样式 */
        .route-options {
            padding: 3px; /* 进一步缩小内边距 */
            background: var(--background-color);
            border-radius: 2px; /* 更方正的圆角 */
            border: 1px solid var(--border-color);
            margin-bottom: 4px; /* 进一步缩小下边距 */
            font-size: 7px; /* 进一步缩小字体 */
            max-width: 180px; /* 限制最大宽度 */
        }

        .option-group {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 3px; /* 进一步缩小间距 */
        }

        .option-group:last-child {
            margin-bottom: 0;
        }

        .option-label {
            font-weight: 500;
            color: var(--text-color);
            font-size: var(--font-small);
        }

        .route-select {
            padding: var(--padding-small);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background: var(--surface-color);
            color: var(--text-color);
            font-size: var(--font-small);
            min-width: 120px;
            cursor: pointer;
        }

        .route-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
        }

        /* 扩展的路径结果样式 */
        .route-segments,
        .route-type-result {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .route-actions-result {
            margin-top: var(--margin-normal);
            padding-top: var(--margin-normal);
            border-top: 1px solid var(--border-color);
            display: flex;
            gap: var(--margin-small);
        }

        .route-btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            position: relative;
            overflow: hidden;
        }

        .route-btn-outline::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--gradient-primary);
            transition: left var(--animation-normal) ease;
            z-index: 0;
        }

        .route-btn-outline:hover::before {
            left: 0;
        }

        .route-btn-outline:hover {
            color: white;
            border-color: var(--primary-hover);
            transform: translateY(-2px) scale(1.05);
            box-shadow: var(--shadow-lg);
        }

        .route-btn-outline span {
            position: relative;
            z-index: 1;
        }

        /* 美化的路径报告样式 */
        .route-summary {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: var(--border-radius);
            padding: var(--padding-normal);
            margin-bottom: var(--margin-normal);
        }

        .route-summary-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-normal);
            padding-bottom: var(--padding-small);
            border-bottom: 2px solid #dee2e6;
        }

        .route-title {
            margin: 0;
            color: var(--text-color);
            font-size: 16px;
            font-weight: 600;
        }

        .route-status {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }

        .status-indicator.success {
            background: #28a745;
        }

        .status-text {
            font-size: var(--font-small);
            color: #28a745;
            font-weight: 500;
        }

        /* pulse动画已在顶部定义，此处删除重复 */

        .route-main-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--margin-normal);
            margin-bottom: var(--margin-normal);
        }

        .route-distance-card,
        .route-time-card {
            background: white;
            border-radius: var(--border-radius);
            padding: var(--padding-normal);
            display: flex;
            align-items: center;
            gap: var(--margin-small);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary-color);
        }

        .info-icon {
            font-size: 24px;
            opacity: 0.8;
        }

        .info-content {
            flex: 1;
        }

        .info-content .info-label {
            font-size: var(--font-small);
            color: #6c757d;
            margin-bottom: 4px;
            display: block;
        }

        .info-content .info-value.primary {
            font-size: 18px;
            font-weight: 600;
            color: var(--primary-color);
            margin: 0;
        }

        .route-details {
            background: white;
            border-radius: var(--border-radius);
            padding: var(--padding-normal);
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: var(--margin-small);
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-icon {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .detail-label {
            font-size: var(--font-small);
            color: #6c757d;
            min-width: 80px;
        }

        .detail-value {
            font-weight: 500;
            color: var(--text-color);
            margin-left: auto;
        }

        .route-actions-result .route-btn {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-icon {
            font-size: 14px;
        }

        /* 路径报告确认对话框样式 */
        .route-confirm-content {
            padding: var(--padding-normal);
        }

        .confirm-message {
            font-size: var(--font-normal);
            color: var(--text-color);
            margin-bottom: var(--margin-large);
            text-align: center;
            line-height: 1.5;
        }

        .route-preview {
            background: var(--background-color);
            border-radius: var(--border-radius);
            padding: var(--padding-normal);
            margin-bottom: var(--margin-large);
        }

        .preview-item {
            display: flex;
            align-items: center;
            gap: var(--margin-small);
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .preview-item:last-child {
            border-bottom: none;
        }

        .preview-icon {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .preview-label {
            font-size: var(--font-small);
            color: var(--text-secondary);
            min-width: 60px;
        }

        .preview-value {
            font-weight: 500;
            color: var(--text-color);
            margin-left: auto;
        }

        .route-confirm-actions {
            display: flex;
            gap: var(--margin-normal);
            justify-content: center;
        }

        .btn {
            padding: var(--padding-normal) var(--padding-large);
            border: none;
            border-radius: var(--border-radius);
            font-size: var(--font-normal);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--animation-fast) ease;
            display: flex;
            align-items: center;
            gap: 6px;
            min-width: 120px;
            justify-content: center;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1565c0;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3);
        }

        .btn-secondary {
            background: var(--border-color);
            color: var(--text-color);
        }

        .btn-secondary:hover {
            background: var(--text-secondary);
            color: white;
        }

        /* 路径报告显示模态框样式 */
        .route-report-content {
            max-width: 800px;
            width: 90%;
        }

        #route-report-display {
            max-height: 70vh;
            overflow-y: auto;
        }

        /* 模态框样式 - 方正简洁设计 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: var(--z-modal);
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn var(--animation-normal) ease-out;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            position: relative;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            max-width: 500px;
            width: 90%;
            max-height: 85vh; /* 增加最大高度 */
            overflow-y: auto; /* 允许垂直滚动 */
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            border-bottom: 1px solid #ddd;
            background: #f8f9fa;
        }

        .modal-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .modal-close {
            background: none;
            border: 1px solid #ccc;
            font-size: 16px;
            color: #666;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            background: #f0f0f0;
            border-color: #999;
            color: #333;
        }

        .modal-body {
            padding: 16px;
            max-height: calc(85vh - 120px); /* 减去头部和底部的高度 */
            overflow-y: auto; /* 允许内容滚动 */
        }

        /* 建筑物详情样式 */
        .building-details {
            margin-bottom: var(--margin-large);
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--padding-small) 0;
            border-bottom: 1px solid var(--border-color);
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 500;
            color: var(--text-color);
        }

        .detail-value {
            color: var(--text-secondary);
        }

        .modal-actions {
            display: flex;
            gap: var(--margin-normal);
            justify-content: flex-end;
        }

        .modal-btn {
            padding: var(--padding-normal) var(--padding-large);
            border: none;
            border-radius: var(--border-radius);
            font-size: var(--font-normal);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--animation-fast) ease;
        }

        .modal-btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .modal-btn-primary:hover {
            background: #1565c0;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3);
        }

        .modal-btn-secondary {
            background: var(--border-color);
            color: var(--text-color);
        }

        .modal-btn-secondary:hover {
            background: var(--text-secondary);
            color: white;
        }

        /* 系统信息样式 */
        .system-info h4 {
            font-size: var(--font-large);
            margin-bottom: 12px; /* 减小间距 */
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 6px; /* 减小内边距 */
        }

        .system-info h5 {
            font-size: var(--font-normal);
            margin-top: 16px; /* 减小上边距 */
            margin-bottom: 8px; /* 减小下边距 */
            color: var(--text-color);
            font-weight: 600;
        }

        .system-info p {
            margin-bottom: 8px; /* 减小间距 */
            color: var(--text-secondary);
            line-height: 1.4; /* 减小行高 */
        }

        .system-info ul {
            margin-bottom: 12px; /* 减小间距 */
            padding-left: 20px;
        }

        .system-info li {
            margin-bottom: 2px; /* 减小间距 */
            color: var(--text-secondary);
            line-height: 1.3; /* 减小行高 */
        }

        .system-info strong {
            color: var(--text-color);
        }

        .info-stats {
            margin-top: 16px; /* 减小上边距 */
            padding-top: 12px; /* 减小内边距 */
            border-top: 1px solid var(--border-color);
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0; /* 减小内边距 */
        }

        .stat-label {
            color: var(--text-color);
        }

        .stat-value {
            font-weight: 500;
            color: var(--primary-color);
        }


        /* 响应式设计 */
        @media (max-width: 768px) {
            .control-panel {
                position: relative;
                top: 0;
                left: 0;
                max-width: none;
                width: 100%;
                padding: var(--padding-normal);
                background: var(--surface-color);
                border-bottom: 1px solid var(--border-color);
                box-shadow: 0 2px 4px var(--shadow-color);
            }

            .function-buttons {
                top: 10px;
                left: 10px;
                right: 10px;
                transform: none;
                padding: 6px 8px;
                gap: 6px;
                overflow-x: auto;
                scrollbar-width: none;
                -ms-overflow-style: none;
            }

            .function-buttons::-webkit-scrollbar {
                display: none;
            }

            .function-btn {
                min-width: 60px;
                flex-shrink: 0;
                padding: 6px 10px;
                font-size: 12px;
            }

            .btn-icon {
                font-size: 14px;
            }

            .btn-text {
                font-size: 12px;
            }

            .route-planning-panel,
            .layer-control-panel,
            .weather-panel,
            .distance-measure-panel {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border-radius: 0;
                animation: slideInUp var(--animation-normal) ease-out;
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(100%);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .modal-content {
                width: 95%;
                margin: var(--margin-normal);
            }

            .login-form {
                padding: var(--padding-large);
                margin: var(--margin-normal);
            }

            /* 校徽响应式设计 - 平板端 */
            .loading-logo {
                width: 48px;
                height: 48px;
            }

            .login-logo {
                width: 42px;
                height: 42px;
            }
        }

        @media (max-width: 480px) {
            .search-input {
                font-size: var(--font-small);
            }

            .function-buttons {
                top: 8px;
                left: 8px;
                right: 8px;
                padding: 4px 6px;
                gap: 4px;
            }

            .function-btn {
                padding: 6px 8px;
                min-width: 50px;
                gap: 4px;
            }

            .btn-icon {
                font-size: 12px;
            }

            .btn-text {
                font-size: 11px;
            }

            .modal-content {
                width: 98%;
                margin: var(--margin-small);
            }

            .modal-header,
            .modal-body {
                padding: var(--padding-normal);
            }

            .login-form {
                padding: var(--padding-normal);
            }

            /* 校徽响应式设计 - 手机端 */
            .loading-logo {
                width: 40px;
                height: 40px;
            }

            .login-logo {
                width: 36px;
                height: 36px;
            }
        }

        /* 打印样式 */
        @media print {
            .loading-screen,
            .login-container,
            .control-panel,
            .layer-control-panel,
            .route-planning-panel,
            .weather-panel,
            .modal {
                display: none !important;
            }

            .main-container {
                position: static;
            }

            .map-container {
                height: 100vh;
                padding-top: 60px; /* 移动端调整顶部边距 */
            }
        }

        /* 高对比度模式支持 */
        @media (prefers-contrast: high) {
            :root {
                --border-color: #000000;
                --text-secondary: #000000;
                --shadow-color: rgba(0,0,0,0.5);
            }
        }

        /* 减少动画模式支持 */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body>
    <!-- 装饰元素已删除以减少冗余 -->

    <!-- 加载动画容器 -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo-container">
                <img src="ntu.ico" class="loading-logo" alt="南通大学校徽">
            </div>
            <div class="loading-spinner">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
            </div>
            <h2 class="loading-title" data-i18n="page.title">智慧校园地图系统</h2>
            <p class="loading-text" data-i18n="page.loading">正在加载地图数据...</p>
            <div class="loading-progress">
                <div class="progress-bar"></div>
            </div>
        </div>
        <!-- 装饰元素 -->
        <div class="loading-decoration">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
        </div>
    </div>

    <!-- 登录界面容器 -->
    <div id="login-container" class="login-container" style="display: none;">
        <div class="login-background"></div>

        <div class="login-form-wrapper">
            <div class="login-form">
                <div class="login-header">
                    <div class="login-logo-container">
                        <img src="ntu.ico" class="login-logo" alt="南通大学校徽">
                    </div>
                    <h1 class="login-title" data-i18n="page.title">智慧校园地图系统</h1>
                    <p class="login-subtitle" data-i18n="page.loginPrompt">请登录以访问地图功能</p>
                </div>

                <form id="login-form" class="login-form-content">
                    <div class="input-group">
                        <label for="username" class="input-label" data-i18n="login.username">用户名</label>
                        <input type="text" id="username" name="username" class="input-field"
                               placeholder="请输入用户名" data-i18n="login.username" required autocomplete="username">
                    </div>

                    <div class="input-group">
                        <label for="password" class="input-label" data-i18n="login.password">密码</label>
                        <input type="password" id="password" name="password" class="input-field"
                               placeholder="请输入密码" data-i18n="login.password" required autocomplete="current-password">
                    </div>

                    <div class="login-actions">
                        <button type="submit" id="login-btn" class="login-button">
                            <span class="button-text" data-i18n="login.loginBtn">登录</span>
                            <span class="button-loading" style="display: none;">登录中...</span>
                        </button>
                    </div>

                    <div id="login-error" class="login-error" style="display: none;">
                        <span class="error-icon">⚠</span>
                        <span class="error-text">用户名或密码错误</span>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 主地图界面容器 -->
    <div id="main-container" class="main-container" style="display: none;">
        <!-- 地图容器 -->
        <div id="map-container" class="map-container">
            <div id="map" class="map"></div>

            <!-- 指北针 -->
            <div id="compass" class="compass-container" title="指北针">
                <div class="compass-outer">
                    <div class="compass-inner">
                        <div class="compass-needle">
                            <div class="needle-north">N</div>
                            <div class="needle-pointer"></div>
                        </div>
                        <div class="compass-directions">
                            <span class="direction north">N</span>
                            <span class="direction east">E</span>
                            <span class="direction south">S</span>
                            <span class="direction west">W</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 地图加载提示 -->
            <div id="map-loading" class="map-loading" style="display: none;">
                <div class="map-loading-content">
                    <div class="loading-spinner-small"></div>
                    <span>加载地图数据中...</span>
                </div>
            </div>

            <!-- 鼠标位置显示 -->
            <div id="mouse-position" class="mouse-position"></div>
        </div>

        <!-- 控制面板 -->
        <div id="control-panel" class="control-panel">
            <!-- 搜索控件 -->
            <div class="search-container">
                <div class="search-input-wrapper">
                    <input type="text" id="search-input" class="search-input"
                           placeholder="搜索建筑物..." data-i18n="search.placeholder" autocomplete="off">
                    <button id="settings-btn" class="search-settings-btn" title="设置">
                        <span>⚙</span>
                    </button>
                    <button id="search-clear" class="search-clear" style="display: none;">
                        <span>×</span>
                    </button>
                </div>

                <!-- 搜索结果下拉列表 -->
                <div id="search-results" class="search-results" style="display: none;">
                    <div class="search-results-header">
                        <span class="results-count">搜索结果</span>
                    </div>
                    <ul id="search-results-list" class="search-results-list">
                        <!-- 搜索结果将动态插入这里 -->
                    </ul>
                    <div class="search-no-results" style="display: none;">
                        <span>未找到相关建筑物</span>
                    </div>
                </div>
            </div>

            <!-- 建筑物信息显示面板 -->
            <div id="building-info-panel" class="building-info-panel">
                <div class="building-info-header">
                    <h3 id="building-info-title">建筑物信息</h3>
                    <button id="building-info-close" class="building-info-close" title="关闭">×</button>
                </div>
                <div class="building-info-content">
                    <div class="building-info-item">
                        <span class="building-info-label">名称:</span>
                        <span id="building-info-name" class="building-info-value">-</span>
                    </div>
                    <div class="building-info-item">
                        <span class="building-info-label">类型:</span>
                        <span id="building-info-type" class="building-info-value">-</span>
                    </div>
                    <div class="building-info-item">
                        <span class="building-info-label">面积:</span>
                        <span id="building-info-area" class="building-info-value">-</span>
                    </div>
                    <div class="building-info-item">
                        <span class="building-info-label">坐标:</span>
                        <span id="building-info-coordinates" class="building-info-value">-</span>
                    </div>
                    <div class="building-info-item">
                        <span class="building-info-label">楼层:</span>
                        <span id="building-info-floors" class="building-info-value">-</span>
                    </div>
                    <div class="building-info-item">
                        <span class="building-info-label">建造年份:</span>
                        <span id="building-info-year" class="building-info-value">-</span>
                    </div>
                    <div class="building-info-item" id="building-info-description-item">
                        <span class="building-info-label">描述:</span>
                        <span id="building-info-description" class="building-info-value">-</span>
                    </div>
                </div>
            </div>

            <!-- 功能按钮组 -->
            <div class="function-buttons">
                <button id="route-planning-btn" class="function-btn" title="路径规划">
                    <span class="btn-text" data-i18n="buttons.routePlanning">路径规划</span>
                </button>

                <button id="layer-control-btn" class="function-btn" title="图层控制">
                    <span class="btn-text" data-i18n="buttons.layerControl">图层控制</span>
                </button>

                <button id="weather-btn" class="function-btn" title="天气查询">
                    <span class="btn-text" data-i18n="buttons.weather">天气查询</span>
                </button>

                <button id="info-btn" class="function-btn" title="系统信息">
                    <span class="btn-text" data-i18n="buttons.systemInfo">系统信息</span>
                </button>

                <button id="distance-measure-btn" class="function-btn" title="测距工具">
                    <span class="btn-text" data-i18n="buttons.distanceMeasure">测距工具</span>
                </button>

            </div>
        </div>

        <!-- 图层控制面板 -->
        <div id="layer-control-panel" class="layer-control-panel" style="display: none;">
            <div class="panel-header">
                <h3 class="panel-title">图层控制</h3>
                <button id="layer-panel-close" class="panel-close">×</button>
            </div>

            <div class="panel-content">
                <div class="layer-group">
                    <h4 class="layer-group-title">地图图层</h4>
                    <div class="layer-items">
                        <label class="layer-item">
                            <input type="checkbox" id="layer-roads" checked>
                            <span class="layer-name">道路</span>
                            <span class="layer-color roads-color"></span>
                        </label>

                        <label class="layer-item">
                            <input type="checkbox" id="layer-waters" checked>
                            <span class="layer-name">水域</span>
                            <span class="layer-color waters-color"></span>
                        </label>

                        <label class="layer-item">
                            <input type="checkbox" id="layer-rivers" checked>
                            <span class="layer-name">河流</span>
                            <span class="layer-color rivers-color"></span>
                        </label>



                        <label class="layer-item">
                            <input type="checkbox" id="layer-boundary">
                            <span class="layer-name">校园边界</span>
                            <span class="layer-color boundary-color"></span>
                        </label>
                    </div>
                </div>

                <!-- 建筑物分类图层（二级显示） -->
                <div class="layer-group">
                    <h4 class="layer-group-title">建筑物分类</h4>
                    <div class="layer-items">
                        <label class="layer-item layer-item-parent">
                            <input type="checkbox" id="layer-buildings" checked>
                            <span class="layer-name">建筑物总控制</span>
                            <span class="layer-toggle">▼</span>
                        </label>

                        <!-- 建筑物子分类 -->
                        <div class="layer-sub-items" id="building-sub-layers">
                            <label class="layer-item layer-item-sub">
                                <input type="checkbox" id="layer-building-teaching" checked>
                                <span class="layer-name">🏫 教学建筑</span>
                                <span class="layer-color" style="background-color: rgba(74, 144, 226, 0.8);"></span>
                            </label>

                            <label class="layer-item layer-item-sub">
                                <input type="checkbox" id="layer-building-public" checked>
                                <span class="layer-name">🏛️ 公共建筑</span>
                                <span class="layer-color" style="background-color: rgba(173, 216, 230, 0.8);"></span>
                            </label>

                            <label class="layer-item layer-item-sub">
                                <input type="checkbox" id="layer-building-dormitory" checked>
                                <span class="layer-name">🏠 宿舍建筑</span>
                                <span class="layer-color" style="background-color: rgba(255, 223, 186, 0.8);"></span>
                            </label>

                            <label class="layer-item layer-item-sub">
                                <input type="checkbox" id="layer-building-sports" checked>
                                <span class="layer-name">🏟️ 体育建筑</span>
                                <span class="layer-color" style="background-color: rgba(149, 225, 211, 0.8);"></span>
                            </label>

                            <label class="layer-item layer-item-sub">
                                <input type="checkbox" id="layer-building-admin" checked>
                                <span class="layer-name">🏢 行政建筑</span>
                                <span class="layer-color" style="background-color: rgba(221, 160, 221, 0.8);"></span>
                            </label>

                            <label class="layer-item layer-item-sub">
                                <input type="checkbox" id="layer-building-dining" checked>
                                <span class="layer-name">🍽️ 食堂</span>
                                <span class="layer-color" style="background-color: rgba(255, 218, 185, 0.8);"></span>
                            </label>

                            <label class="layer-item layer-item-sub">
                                <input type="checkbox" id="layer-building-service" checked>
                                <span class="layer-name">🏪 服务建筑</span>
                                <span class="layer-color" style="background-color: rgba(186, 220, 255, 0.8);"></span>
                            </label>

                            <label class="layer-item layer-item-sub">
                                <input type="checkbox" id="layer-building-college" checked>
                                <span class="layer-name">🎓 学院建筑</span>
                                <span class="layer-color" style="background-color: rgba(255, 206, 84, 0.8);"></span>
                            </label>

                            <label class="layer-item layer-item-sub">
                                <input type="checkbox" id="layer-building-parking" checked>
                                <span class="layer-name">🅿️ 公共停车场</span>
                                <span class="layer-color" style="background-color: rgba(255, 69, 58, 0.8);"></span>
                            </label>

                            <label class="layer-item layer-item-sub">
                                <input type="checkbox" id="layer-building-unknown" checked>
                                <span class="layer-name">❓ 未知建筑</span>
                                <span class="layer-color" style="background-color: rgba(178, 190, 195, 0.7);"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 路径规划面板 -->
        <div id="route-planning-panel" class="route-planning-panel" style="display: none;">
            <div class="panel-header">
                <h3 class="panel-title" data-i18n="route.title">路径规划</h3>
                <button id="route-panel-close" class="panel-close">×</button>
            </div>

            <div class="panel-content">
                <div class="route-inputs">
                    <div class="route-input-group">
                        <label for="route-start" class="route-label" data-i18n="route.startPoint">起点</label>
                        <input type="text" id="route-start" class="route-input"
                               placeholder="点击地图选择起点或输入建筑物名称" data-i18n="route.startPlaceholder">
                        <button id="route-start-clear" class="route-clear">×</button>
                    </div>

                    <div class="route-input-group">
                        <label for="route-end" class="route-label" data-i18n="route.endPoint">终点</label>
                        <input type="text" id="route-end" class="route-input"
                               placeholder="点击地图选择终点或输入建筑物名称" data-i18n="route.endPlaceholder">
                        <button id="route-end-clear" class="route-clear">×</button>
                    </div>
                </div>

                <!-- 地图点击开关 -->
                <div class="map-click-toggle">
                    <div class="toggle-group">
                        <label class="toggle-label">
                            <span class="toggle-text">🖱️ 地图点击设置</span>
                            <span class="toggle-description">开启后可点击地图设置起点和终点</span>
                        </label>
                        <div class="toggle-switch">
                            <input type="checkbox" id="map-click-enabled" class="toggle-input" checked>
                            <label for="map-click-enabled" class="toggle-slider">
                                <span class="toggle-button"></span>
                                <span class="toggle-on">开启</span>
                                <span class="toggle-off">关闭</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 路径选项 -->
                <div class="route-options">
                    <div class="option-group">
                        <label class="option-label" data-i18n="route.routeType">路径类型：</label>
                        <select id="route-type" class="route-select">
                            <option value="shortest" data-i18n="route.shortest">最短路径</option>
                        </select>
                    </div>

                    <div class="option-group">
                        <label class="option-label" data-i18n="route.transportMode">交通方式：</label>
                        <select id="transport-mode" class="route-select">
                            <option value="walking" data-i18n="route.walking">步行</option>
                            <option value="cycling" data-i18n="route.cycling">骑行</option>
                        </select>
                    </div>
                </div>

                <div class="route-actions">
                    <button id="route-calculate" class="route-btn route-btn-primary">
                        <span data-i18n="route.calculate">计算路径</span>
                    </button>
                    <button id="route-clear" class="route-btn route-btn-secondary">
                        <span data-i18n="route.clear">清除路径</span>
                    </button>
                </div>

                <div id="route-result" class="route-result" style="display: none;">
                    <div class="route-summary">
                        <div class="route-summary-header">
                            <h4 class="route-title">🗺️ 路径规划结果</h4>
                            <div class="route-status">
                                <span class="status-indicator success"></span>
                                <span class="status-text">路径计算成功</span>
                            </div>
                        </div>

                        <div class="route-main-info">
                            <div class="route-distance-card">
                                <div class="info-icon">📏</div>
                                <div class="info-content">
                                    <div class="info-label">总距离</div>
                                    <div id="route-distance-value" class="info-value primary">--</div>
                                </div>
                            </div>

                            <div class="route-time-card">
                                <div class="info-icon">⏱️</div>
                                <div class="info-content">
                                    <div class="info-label">预计时间</div>
                                    <div id="route-time-value" class="info-value primary">--</div>
                                </div>
                            </div>
                        </div>

                        <div class="route-details">
                            <div class="detail-item">
                                <span class="detail-icon">🚶</span>
                                <span class="detail-label">交通方式：</span>
                                <span id="route-mode-value" class="detail-value">步行</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-icon">⚡</span>
                                <span class="detail-label">路径类型：</span>
                                <span id="route-type-value" class="detail-value">最短路径</span>
                            </div>
                        </div>
                    </div>

                    <div class="route-actions-result">
                        <button id="route-export" class="route-btn route-btn-outline">
                            <span class="btn-icon">📤</span>
                            <span>导出路径</span>
                        </button>
                        <button id="route-share" class="route-btn route-btn-outline">
                            <span class="btn-icon">🔗</span>
                            <span>分享路径</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 天气查询面板 -->
        <div id="weather-panel" class="weather-panel" style="display: none;">
            <div class="panel-header">
                <h3 class="panel-title">天气查询</h3>
                <button id="weather-panel-close" class="panel-close">×</button>
            </div>
            <div class="panel-content">
                <div class="weather-loading" style="display: none;">
                    <div class="loading-spinner-small"></div>
                    <span>正在获取天气信息...</span>
                </div>
                <div class="weather-content">
                    <!-- 天气日期和刷新按钮 -->
                    <div class="weather-date">
                        <span class="weather-date-text">2025年6月16日</span>
                        <button class="weather-refresh-btn" title="刷新天气数据">🔄</button>
                    </div>

                    <!-- 主要天气信息 -->
                    <div class="weather-main">
                        <div class="weather-icon">☀️</div>
                        <div class="weather-temp-group">
                            <div class="weather-current-temp">24°C</div>
                            <div class="weather-temp-range">
                                <span class="temp-high">33°</span>
                                <span class="temp-separator">/</span>
                                <span class="temp-low">22°</span>
                            </div>
                        </div>
                    </div>

                    <!-- 温度变化图表 -->
                    <div class="weather-chart">
                        <div class="chart-title">今日温度变化</div>
                        <div class="chart-container">
                            <canvas id="temperature-chart" width="240" height="80"></canvas>
                        </div>
                    </div>

                    <!-- 详细信息 -->
                    <div class="weather-details">
                        <div class="weather-item">
                            <span class="weather-label">湿度</span>
                            <span class="weather-value">75%</span>
                        </div>
                        <div class="weather-item">
                            <span class="weather-label">气压</span>
                            <span class="weather-value">1002 hPa</span>
                        </div>
                        <div class="weather-item">
                            <span class="weather-label">风速</span>
                            <span class="weather-value">28 km/h</span>
                        </div>
                        <div class="weather-item">
                            <span class="weather-label">紫外线</span>
                            <span class="weather-value">3</span>
                        </div>
                        <div class="weather-item weather-update-time">
                            <span class="weather-label">更新时间</span>
                            <span class="weather-value">刚刚</span>
                        </div>
                    </div>

                    <!-- 天气状态指示和控制 -->
                    <div class="weather-status-section">
                        <!-- 更新状态指示 -->
                        <div class="weather-status-info">
                            <div id="weather-countdown" class="weather-countdown">下次更新：60分钟后</div>

                        </div>


                    </div>
                </div>
                <div class="weather-error" style="display: none;">
                    <span class="error-icon">⚠️</span>
                    <span class="error-text">天气信息获取失败</span>
                    <button class="weather-retry-btn">重试</button>
                </div>
            </div>
        </div>

        <!-- 测距工具面板 -->
        <div id="distance-measure-panel" class="distance-measure-panel" style="display: none;">
            <div class="panel-header">
                <h3 class="panel-title">测距工具</h3>
                <button id="distance-panel-close" class="panel-close">×</button>
            </div>
            <div class="panel-content">
                <div class="distance-instructions">
                    <p>点击地图上的两个点来测量距离</p>
                </div>
                <div class="distance-controls">
                    <button id="start-distance-measure" class="form-button">开始测距</button>
                    <button id="clear-distance-measure" class="form-button secondary">清除测距</button>
                </div>
                <div id="distance-result" class="distance-result" style="display: none;">
                    <div class="result-item">
                        <span class="result-label">直线距离：</span>
                        <span id="distance-value" class="result-value">--</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 路径报告确认对话框 -->
    <div id="route-report-confirm-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">路径规划完成</h2>
                <button class="modal-close" id="route-report-confirm-close">×</button>
            </div>
            <div class="modal-body">
                <div class="route-confirm-content">
                    <div class="route-confirm-info">
                        <p class="confirm-message">路径计算已完成，是否生成详细的路径报告？</p>
                        <div class="route-preview">
                            <div class="preview-item">
                                <span class="preview-icon">📍</span>
                                <span class="preview-label">起点：</span>
                                <span id="confirm-start-location" class="preview-value">--</span>
                            </div>
                            <div class="preview-item">
                                <span class="preview-icon">🎯</span>
                                <span class="preview-label">终点：</span>
                                <span id="confirm-end-location" class="preview-value">--</span>
                            </div>
                            <div class="preview-item">
                                <span class="preview-icon">📏</span>
                                <span class="preview-label">距离：</span>
                                <span id="confirm-distance" class="preview-value">--</span>
                            </div>
                            <div class="preview-item">
                                <span class="preview-icon">⏱️</span>
                                <span class="preview-label">时间：</span>
                                <span id="confirm-time" class="preview-value">--</span>
                            </div>
                        </div>
                    </div>
                    <div class="route-confirm-actions">
                        <button id="route-report-generate" class="btn btn-primary">
                            <span class="btn-icon">📊</span>
                            <span>生成报告</span>
                        </button>
                        <button id="route-report-cancel" class="btn btn-secondary">
                            <span class="btn-icon">❌</span>
                            <span>取消</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置面板模态框 -->
    <div id="settings-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-content settings-modal-content">
            <div class="modal-header">
                <h2 class="modal-title" data-i18n="settings.title">系统设置</h2>
                <button class="modal-close" id="settings-close">×</button>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h3 class="settings-section-title" data-i18n="settings.language.title">语言设置</h3>
                    <div class="settings-item">
                        <label class="settings-label" data-i18n="settings.language.label">界面语言：</label>
                        <div class="language-selector">
                            <button id="lang-zh" class="language-option active" data-lang="zh">
                                <span class="lang-name" data-i18n="settings.language.chinese">中文</span>
                            </button>
                            <button id="lang-en" class="language-option" data-lang="en">
                                <span class="lang-name" data-i18n="settings.language.english">English</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 路径报告显示模态框 -->
    <div id="route-report-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-content route-report-content">
            <div class="modal-header">
                <h2 class="modal-title">路径规划报告</h2>
                <button class="modal-close" id="route-report-close">×</button>
            </div>
            <div class="modal-body">
                <div id="route-report-display">
                    <!-- 路径报告内容将在这里动态生成 -->
                </div>
                <div class="modal-actions" style="margin-top: 20px; text-align: center;">
                    <button id="route-report-save" class="btn btn-primary">
                        <span class="btn-icon">💾</span>
                        <span>保存为TXT文件</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 建筑物信息弹窗 -->
    <div id="building-info-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="building-name" class="modal-title">建筑物信息</h3>
                <button id="modal-close" class="modal-close">×</button>
            </div>

            <div class="modal-body">
                <div class="building-details">
                    <div class="detail-item">
                        <span class="detail-label">名称：</span>
                        <span id="building-name-detail" class="detail-value">--</span>
                    </div>

                    <div class="detail-item">
                        <span class="detail-label">面积：</span>
                        <span id="building-area" class="detail-value">--</span>
                    </div>

                    <div class="detail-item">
                        <span class="detail-label">类型：</span>
                        <span id="building-type" class="detail-value">建筑物</span>
                    </div>
                </div>

                <div class="modal-actions">
                    <button id="set-as-start" class="modal-btn modal-btn-primary">
                        设为起点
                    </button>
                    <button id="set-as-end" class="modal-btn modal-btn-secondary">
                        设为终点
                    </button>
                </div>
            </div>
        </div>
    </div>


    <!-- LICENSE许可证模态框 -->
    <div id="license-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-content" style="max-width: 800px; width: 90%;">
            <div class="modal-header">
                <h3 class="modal-title">许可证信息</h3>
                <button class="modal-close">×</button>
            </div>
            <div class="modal-body">
                <div class="license-content" style="max-height: 70vh; overflow-y: auto; padding: 20px; background: #f8f9fa; border-radius: 8px; font-family: 'Courier New', monospace; font-size: 12px; line-height: 1.6; white-space: pre-wrap;" id="license-text">
                    <!-- LICENSE内容将通过JavaScript动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-primary" onclick="document.getElementById('license-modal').style.display='none'">关闭</button>
            </div>
        </div>
    </div>

    <!-- 系统信息模态框 -->
    <div id="info-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">系统信息</h3>
                <button class="modal-close">×</button>
            </div>

            <div class="modal-body">
                <div class="system-info">
                    <h4>智慧校园系统 (Smart Campus System)</h4>
                    <p><strong>版本:</strong> 2.0.0</p>
                    <p><strong>开发时间:</strong> 2025年</p>
                    <p><strong>投影坐标系:</strong> EPSG:3857</p>

                    <h5>项目描述</h5>
                    <p>本软件是一个基于Electron开发的智慧校园地图系统，专为南通大学设计。系统提供了完整的校园地图浏览、建筑物查询、路径规划、天气查询等功能。</p>

                    <h5>系统要求</h5>
                    <ul>
                        <li>Windows 10/11 (x64)</li>
                        <li>内存: 最低 4GB RAM</li>
                        <li>硬盘: 最低 500MB 可用空间</li>
                        <li>网络: 可选(天气查询功能需要)</li>
                    </ul>

                    <h5>使用许可</h5>
                    <p>本软件不可商用</p>

                    <h5>版权信息</h5>
                    <p><strong>Copyright (c) 2025 QiFuXiang of GIS</strong></p>
                    <p>软件版权归开发团队所有，南通大学相关标识和名称归南通大学所有。</p>

                    <h5>联系信息</h5>
                    <p><strong>技术支持:</strong> 15064717930</p>
                    <p><strong>反馈邮箱:</strong> <EMAIL></p>

                    <div class="info-stats">
                        <div class="stat-item">
                            <span class="stat-label">建筑物数量：</span>
                            <span id="buildings-count" class="stat-value">--</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">已加载图层：</span>
                            <span id="layers-count" class="stat-value">--</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">当前缩放级别：</span>
                            <span id="current-zoom" class="stat-value">--</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">地图引擎：</span>
                            <span class="stat-value">OpenLayers 7.5.2</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">系统状态：</span>
                            <span id="system-status" class="stat-value">运行正常</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript引入 -->
    <script src="https://cdn.jsdelivr.net/npm/ol@7.5.2/dist/ol.js"></script>

    <!-- 引入GeoJSON数据文件 -->
    <script src="data/buildings.js"></script>

    <!-- 引入国际化配置 -->
    <script src="i18n.js"></script>

    <!-- 错误处理模块（优先加载） -->
    <script src="js/error-handler.js"></script>

    <!-- 引入模块化主脚本（新架构） -->
    <!-- <script src="js/main.js"></script> -->

    <!-- 备用：原始脚本（如需回退可取消注释） -->
    <script src="script.js"></script>
</body>
</html>