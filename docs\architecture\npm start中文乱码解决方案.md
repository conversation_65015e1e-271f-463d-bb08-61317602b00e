# Windows控制台中文乱码问题解决方案

## 📋 文档信息
- **文档版本**: v1.0
- **创建时间**: 2025-01-23
- **负责人**: Alex (工程师)
- **问题**: Windows控制台显示中文乱码

## 🎯 问题分析

### 乱码原因：
```
Windows控制台编码: GBK (代码页936)
main.js文件编码: UTF-8
结果: 中文字符显示为乱码
```

### 实际表现：
```
正常显示: 🚀 Electron应用程序启动
乱码显示: 馃摫 Electron涓昏繘绋嬪凡鍔犺浇
```

## 🔧 解决方案

### 方案1: 临时修改控制台编码（推荐）

**步骤1**: 在启动应用前执行
```cmd
chcp 65001
```

**步骤2**: 然后启动应用
```cmd
npm start
```

**完整命令序列**:
```cmd
chcp 65001 && npm start
```

### 方案2: 修改package.json启动脚本（永久解决）

**编辑package.json**:
```json
{
  "scripts": {
    "start": "chcp 65001 && electron .",
    "start-utf8": "chcp 65001 && electron .",
    "start-gbk": "electron ."
  }
}
```

**使用方法**:
```cmd
npm run start-utf8
```

### 方案3: 创建批处理文件（最便捷）

**创建文件**: `start-app.bat`
```batch
@echo off
chcp 65001 > nul
echo 设置控制台编码为UTF-8
npm start
pause
```

**使用方法**: 双击 `start-app.bat` 文件

### 方案4: 修改main.js输出编码（代码层面）

**在main.js开头添加**:
```javascript
// 设置控制台输出编码
if (process.platform === 'win32') {
    process.stdout.setEncoding('utf8');
    process.stderr.setEncoding('utf8');
}
```

## 🎯 推荐解决方案

### 立即解决（临时）:
```cmd
chcp 65001 && npm start
```

### 长期解决（永久）:
1. 修改package.json添加UTF-8启动脚本
2. 创建start-app.bat批处理文件
3. 团队成员统一使用UTF-8启动方式

## 📊 编码对照表

| 代码页 | 编码 | 说明 |
|--------|------|------|
| 936 | GBK | Windows中文默认编码 |
| 65001 | UTF-8 | 国际标准编码 |
| 437 | ASCII | 英文编码 |

## 🔍 验证方法

**检查当前编码**:
```cmd
chcp
```

**正确输出应该是**:
```
Active code page: 65001
```

**测试中文显示**:
```cmd
echo 测试中文显示
```

## ⚠️ 注意事项

1. **临时性**: `chcp 65001`只对当前控制台会话有效
2. **兼容性**: 某些老版本Windows可能不完全支持UTF-8
3. **字体**: 确保控制台使用支持中文的字体
4. **IDE设置**: 确保IDE（如VSCode）也使用UTF-8编码

## 🎯 最佳实践

### 开发环境配置：
1. **VSCode设置**: 文件 → 首选项 → 设置 → 搜索"encoding" → 设为UTF-8
2. **Git配置**: `git config --global core.quotepath false`
3. **控制台配置**: 默认使用UTF-8编码启动

### 团队协作规范：
1. 所有源代码文件统一使用UTF-8编码
2. 提供标准化的启动脚本
3. 文档中说明编码要求

---

**技术负责人**: Alex (工程师)  
**解决状态**: 已提供完整解决方案  
**建议**: 使用方案2（修改package.json）+ 方案3（批处理文件）组合