{"name": "smart-campus-system", "version": "2.0.0", "description": "智慧校园地图系统", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build:win": "electron-builder --win", "pack": "electron-builder --dir"}, "author": "智慧校园开发团队", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.13.3"}, "build": {"appId": "com.campus.smart-system", "productName": "智慧校园系统", "copyright": "Copyright © 2025 智慧校园开发团队", "buildVersion": "2.0.0", "electronVersion": "28.0.0", "nodeGypRebuild": false, "buildDependenciesFromSource": false, "npmRebuild": false, "forceCodeSigning": false, "icon": "icon.ico", "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "icon.ico", "requestedExecutionLevel": "asInvoker", "sign": null, "certificateFile": null, "certificatePassword": null, "verifyUpdateCodeSignature": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "智慧校园系统"}, "directories": {"output": "dist"}, "files": ["main.js", "index.html", "script.js", "i18n.js", "icon.ico", "ntu.ico", "LICENSE.txt", "data/**/*", "geojson2_data/**/*", "!node_modules/**/*", "!server/**/*", "!dist/**/*", "!docs/**/*", "!md文档/**/*", "!map_data2/**/*", "!南通大学校徽.ico", "!南通大学校徽.png", "!个人理解.txt", "!项目打包完整指南.md", "!智慧校园系统安装包.exe", "!summary.txt", "!summary.md", "!export_sqlite_data.py", "!generate_buildings_js.py", "!buildings_data.json", "!buildings_sample.json"]}, "dependencies": {"sqlite3": "^5.1.7"}}