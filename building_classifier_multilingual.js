// 多语言建筑物分类器扩展
// 扩展原有的BuildingClassifier以支持英文建筑物名称分类

if (typeof BuildingClassifier !== 'undefined') {
    // 保存原始分类规则
    BuildingClassifier.originalClassificationRules = { ...BuildingClassifier.classificationRules };
    
    // 扩展分类规则，添加英文关键词
    BuildingClassifier.classificationRules = {
        '教学建筑': [
            // 中文关键词
            '教学楼', '教学', '实验楼', '实验', '工程训练中心', '学院',
            '机械工程学院', '交通与土木工程学院', '电气工程学院', '纺化楼',
            // 英文关键词
            'Teaching Building', 'Teaching', 'Laboratory', 'Lab', 'Engineering Training',
            'School of', 'College of', 'Engineering', 'Science', 'Technology'
        ],
        '公共建筑': [
            // 中文关键词
            '图书馆', '范曾艺术馆', '艺术馆', '综合楼', '校园服务中心',
            // 英文关键词
            'Library', 'Art Museum', 'Museum', 'Art', 'Service Center', 'Campus Service'
        ],
        '宿舍建筑': [
            // 中文关键词
            '公寓', '宿舍', '学生公寓', '研究生公寓', '留学生公寓', '青年教师公寓',
            // 英文关键词
            'Dormitory', 'Student Dormitory', 'Graduate', 'International Student',
            'Faculty Apartment', 'Young Faculty', 'Apartment'
        ],
        '体育建筑': [
            // 中文关键词
            '体育', '操场', '看台', '体育馆', '击剑馆', '足球场', '篮球场', '排球场',
            // 英文关键词
            'Sports', 'Gymnasium', 'Stadium', 'Field', 'Sports Center', 'Sports Building'
        ],
        '行政建筑': [
            // 中文关键词
            '行政', '办公', '校医院', '医院', '校园服务中心',
            // 英文关键词
            'Administrative', 'Office', 'Hospital', 'Medical', 'Service Center'
        ],
        '食堂': [
            // 中文关键词
            '食堂', '餐厅',
            // 英文关键词
            'Dining Hall', 'Dining', 'Cafeteria', 'Restaurant'
        ],
        '服务建筑': [
            // 中文关键词
            '服务', '驿站', '顺丰', '数码', '供电站', '交通职业适应性评价中心', '工程训练中心',
            // 英文关键词
            'Service', 'Training Center', 'Engineering Training', 'University Hospital'
        ]
    };
    
    // 保存原始分类方法
    BuildingClassifier.originalClassifyBuilding = BuildingClassifier.classifyBuilding;
    
    // 重写分类方法以支持多语言
    BuildingClassifier.classifyBuilding = function(buildingName) {
        if (!buildingName || typeof buildingName !== 'string') {
            return '未知';
        }

        // 遍历分类规则
        for (const [category, keywords] of Object.entries(this.classificationRules)) {
            for (const keyword of keywords) {
                if (buildingName.includes(keyword)) {
                    return category;
                }
            }
        }

        return '未知';
    };
    
    // 添加英文分类方法
    BuildingClassifier.classifyBuildingEn = function(buildingName) {
        const chineseCategory = this.classifyBuilding(buildingName);
        
        // 中文类型到英文类型的映射
        const categoryMapping = {
            '教学建筑': 'Teaching Building',
            '公共建筑': 'Public Building',
            '宿舍建筑': 'Dormitory Building',
            '体育建筑': 'Sports Building',
            '行政建筑': 'Administrative Building',
            '食堂': 'Dining Hall',
            '服务建筑': 'Service Building',
            '未知': 'Unknown'
        };
        
        return categoryMapping[chineseCategory] || 'Unknown';
    };
    
    // 保存原始样式获取方法
    BuildingClassifier.originalGetBuildingStyle = BuildingClassifier.getBuildingStyle;
    
    // 重写样式获取方法以支持多语言
    BuildingClassifier.getBuildingStyle = function(buildingName, language = 'zh') {
        const category = this.classifyBuilding(buildingName);
        const styleConfig = CONFIG.styles.buildingTypes[category];
        
        if (styleConfig && language === 'en') {
            // 为英文模式创建样式配置副本
            const englishStyleConfig = { ...styleConfig };
            englishStyleConfig.name = this.classifyBuildingEn(buildingName);
            return englishStyleConfig;
        }
        
        return styleConfig || CONFIG.styles.buildingTypes['未知'];
    };
    
    // 添加获取本地化类型的方法
    BuildingClassifier.getLocalizedType = function(buildingName, language = 'zh') {
        if (language === 'en') {
            return this.classifyBuildingEn(buildingName);
        } else {
            return this.classifyBuilding(buildingName);
        }
    };
    
    console.log('✅ 多语言建筑物分类器已加载');
    console.log('📊 支持的分类类型:', Object.keys(this.classificationRules));
}

// 扩展CONFIG.styles.buildingTypes以支持英文类型
if (typeof CONFIG !== 'undefined' && CONFIG.styles && CONFIG.styles.buildingTypes) {
    // 添加英文建筑物类型样式
    const englishBuildingTypes = {
        'Teaching Building': {
            fill: 'rgba(74, 144, 226, 0.8)',
            stroke: '#4A90E2',
            strokeWidth: 2,
            icon: '🏫',
            name: 'Teaching Building'
        },
        'Public Building': {
            fill: 'rgba(149, 225, 211, 0.8)',
            stroke: '#95e1d3',
            strokeWidth: 2,
            icon: '🏛️',
            name: 'Public Building'
        },
        'Dormitory Building': {
            fill: 'rgba(255, 182, 193, 0.8)',
            stroke: '#ffb6c1',
            strokeWidth: 2,
            icon: '🏠',
            name: 'Dormitory Building'
        },
        'Sports Building': {
            fill: 'rgba(255, 165, 0, 0.8)',
            stroke: '#ffa500',
            strokeWidth: 2,
            icon: '🏟️',
            name: 'Sports Building'
        },
        'Administrative Building': {
            fill: 'rgba(128, 128, 128, 0.8)',
            stroke: '#808080',
            strokeWidth: 2,
            icon: '🏢',
            name: 'Administrative Building'
        },
        'Dining Hall': {
            fill: 'rgba(255, 223, 186, 0.8)',
            stroke: '#ffdeba',
            strokeWidth: 2,
            icon: '🍽️',
            name: 'Dining Hall'
        },
        'Service Building': {
            fill: 'rgba(221, 160, 221, 0.8)',
            stroke: '#dda0dd',
            strokeWidth: 2,
            icon: '🏪',
            name: 'Service Building'
        },
        'Unknown': {
            fill: 'rgba(169, 169, 169, 0.8)',
            stroke: '#a9a9a9',
            strokeWidth: 2,
            icon: '❓',
            name: 'Unknown'
        }
    };
    
    // 合并英文类型到现有配置
    Object.assign(CONFIG.styles.buildingTypes, englishBuildingTypes);
    
    console.log('✅ 英文建筑物类型样式已添加');
}