const express = require('express');
const mysql = require('mysql2/promise');
const sqlite3 = require('sqlite3').verbose();
const cors = require('cors');
const path = require('path');
const https = require('https');

const app = express();
const PORT = 3001;

// 中间件配置
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 数据库配置
const dbConfig = {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: '005026...',
    database: 'campus_map',
    charset: 'utf8mb4'
};

// SQLite配置
const sqliteConfig = {
    path: path.join(__dirname, 'campus_map.db')
};

// 数据库适配器类
class DatabaseAdapter {
    constructor(type = 'mysql') {
        this.type = type;

        if (type === 'sqlite') {
            this.db = new sqlite3.Database(sqliteConfig.path, (err) => {
                if (err) {
                    console.error('❌ SQLite连接失败:', err.message);
                } else {
                    console.log('✅ SQLite数据库连接成功');
                    // 启用外键约束
                    this.db.run('PRAGMA foreign_keys = ON');
                }
            });
        } else {
            this.pool = mysql.createPool({
                ...dbConfig,
                waitForConnections: true,
                connectionLimit: 10,
                queueLimit: 0
            });
        }
    }

    async query(sql, params = []) {
        if (this.type === 'sqlite') {
            return new Promise((resolve, reject) => {
                this.db.all(sql, params, (err, rows) => {
                    if (err) {
                        reject(err);
                    } else {
                        // 返回与MySQL相同的格式 [rows, fields]
                        resolve([rows]);
                    }
                });
            });
        } else {
            return this.pool.execute(sql, params);
        }
    }

    async execute(sql, params = []) {
        return this.query(sql, params);
    }

    async close() {
        if (this.type === 'sqlite') {
            return new Promise((resolve) => {
                this.db.close((err) => {
                    if (err) {
                        console.error('SQLite关闭错误:', err.message);
                    }
                    resolve();
                });
            });
        } else {
            await this.pool.end();
        }
    }
}

// 根据环境变量选择数据库类型
const dbType = process.env.NODE_ENV === 'production' ? 'sqlite' : 'mysql';
console.log(`🔧 使用数据库类型: ${dbType}`);

// 创建数据库适配器实例
const db = new DatabaseAdapter(dbType);

// 测试数据库连接
async function testConnection() {
    try {
        if (dbType === 'sqlite') {
            // SQLite连接测试
            const [rows] = await db.query('SELECT 1 as test');
            console.log('✅ SQLite数据库连接测试成功');
            return true;
        } else {
            // MySQL连接测试
            const connection = await db.pool.getConnection();
            console.log('✅ MySQL数据库连接成功');
            connection.release();
            return true;
        }
    } catch (error) {
        console.error('❌ 数据库连接失败:', error.message);
        return false;
    }
}

// 初始化用户行为追踪表
async function initUserTrackingTables() {
    try {
        // 注意：SQLite数据库已经包含所有表，这里主要用于MySQL环境
        if (dbType === 'mysql') {
            const connection = await db.pool.getConnection();

        // 创建用户会话表
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id VARCHAR(255) UNIQUE NOT NULL,
                user_ip VARCHAR(45),
                user_agent TEXT,
                start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                page_views INT DEFAULT 0,
                actions_count INT DEFAULT 0,
                duration_seconds INT DEFAULT 0,
                status ENUM('active', 'ended') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

        // 创建用户行为记录表
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS user_actions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id VARCHAR(255) NOT NULL,
                action_type ENUM('page_visit', 'search', 'route_plan', 'weather_check', 'layer_toggle', 'building_click', 'map_click', 'export', 'other') NOT NULL,
                action_name VARCHAR(255) NOT NULL,
                action_data JSON,
                page_url VARCHAR(500),
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_session_id (session_id),
                INDEX idx_action_type (action_type),
                INDEX idx_timestamp (timestamp),
                FOREIGN KEY (session_id) REFERENCES user_sessions(session_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

        // 创建系统统计表
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS system_statistics (
                id INT AUTO_INCREMENT PRIMARY KEY,
                stat_date DATE NOT NULL,
                total_visits INT DEFAULT 0,
                unique_sessions INT DEFAULT 0,
                total_actions INT DEFAULT 0,
                avg_session_duration DECIMAL(10,2) DEFAULT 0,
                popular_features JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_date (stat_date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

            connection.release();
            console.log('✅ MySQL用户行为追踪表初始化成功');
        } else {
            console.log('✅ SQLite数据库已包含所有表，跳过初始化');
        }
        return true;
    } catch (error) {
        console.error('❌ 用户行为追踪表初始化失败:', error.message);
        return false;
    }
}

// 静态文件服务
app.use(express.static(path.join(__dirname, '../')));

// 健康检查接口
app.get('/api/health', async (req, res) => {
    const dbStatus = await testConnection();
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        database: dbStatus ? 'connected' : 'disconnected'
    });
});

// 用户行为追踪API

// 创建或获取用户会话
app.post('/api/tracking/session', async (req, res) => {
    try {
        const { sessionId, userAgent } = req.body;
        const userIp = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];

        // 检查会话是否已存在
        const [existingSessions] = await db.execute(
            'SELECT * FROM user_sessions WHERE session_id = ?',
            [sessionId]
        );

        if (existingSessions.length > 0) {
            // 更新现有会话的最后活动时间
            await db.execute(
                'UPDATE user_sessions SET last_activity = CURRENT_TIMESTAMP WHERE session_id = ?',
                [sessionId]
            );

            res.json({
                success: true,
                message: '会话已更新',
                sessionId: sessionId
            });
        } else {
            // 创建新会话
            await db.execute(
                'INSERT INTO user_sessions (session_id, user_ip, user_agent) VALUES (?, ?, ?)',
                [sessionId, userIp, userAgent]
            );

            res.json({
                success: true,
                message: '新会话已创建',
                sessionId: sessionId
            });
        }
    } catch (error) {
        console.error('创建/更新用户会话失败:', error);
        res.status(500).json({
            success: false,
            message: '会话处理失败',
            error: error.message
        });
    }
});

// 记录用户行为
app.post('/api/tracking/action', async (req, res) => {
    try {
        const { sessionId, actionType, actionName, actionData, pageUrl } = req.body;

        // 验证必需参数
        if (!sessionId || !actionType || !actionName) {
            return res.status(400).json({
                success: false,
                message: '缺少必需参数: sessionId, actionType, actionName'
            });
        }

        // 记录用户行为
        await db.execute(
            'INSERT INTO user_actions (session_id, action_type, action_name, action_data, page_url) VALUES (?, ?, ?, ?, ?)',
            [sessionId, actionType, actionName, JSON.stringify(actionData || {}), pageUrl]
        );

        // 更新会话统计
        await db.execute(
            'UPDATE user_sessions SET actions_count = actions_count + 1, last_activity = CURRENT_TIMESTAMP WHERE session_id = ?',
            [sessionId]
        );

        res.json({
            success: true,
            message: '用户行为已记录'
        });
    } catch (error) {
        console.error('记录用户行为失败:', error);
        res.status(500).json({
            success: false,
            message: '记录用户行为失败',
            error: error.message
        });
    }
});

// 获取所有建筑物
app.get('/api/buildings', async (req, res) => {
    try {
        const { type, search } = req.query;
        
        let sql = 'SELECT * FROM buildings WHERE status = "active"';
        let params = [];
        
        if (type && type !== 'all') {
            sql += ' AND type = ?';
            params.push(type);
        }
        
        if (search) {
            sql += ' AND name LIKE ?';
            params.push(`%${search}%`);
        }
        
        sql += ' ORDER BY name';
        
        const [rows] = await db.execute(sql, params);
        
        res.json({
            success: true,
            data: rows,
            count: rows.length
        });
    } catch (error) {
        console.error('查询建筑物失败:', error);
        res.status(500).json({
            success: false,
            message: '查询建筑物失败',
            error: error.message
        });
    }
});

// 根据ID获取建筑物详情
app.get('/api/buildings/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        const sql = 'SELECT * FROM buildings WHERE id = ? AND status = "active"';
        const [rows] = await db.execute(sql, [id]);
        
        if (rows.length > 0) {
            res.json({
                success: true,
                data: rows[0]
            });
        } else {
            res.status(404).json({
                success: false,
                message: '建筑物不存在'
            });
        }
    } catch (error) {
        console.error('查询建筑物详情失败:', error);
        res.status(500).json({
            success: false,
            message: '查询建筑物详情失败',
            error: error.message
        });
    }
});

// 获取天气信息
app.get('/api/weather', async (req, res) => {
    try {
        // 尝试从真实天气API获取数据
        let weatherData;

        try {
            // 使用免费的天气API - wttr.in
            const apiData = await fetchWeatherData('https://wttr.in/南通?format=j1&lang=zh');

                // 转换API数据格式
                const currentCondition = apiData.current_condition?.[0] || {};
                const todayWeather = apiData.weather?.[0] || {};

                // 获取中文天气描述
                const weatherDesc = currentCondition.lang_zh?.[0]?.value ||
                                   currentCondition.weatherDesc?.[0]?.value || '晴朗';

                weatherData = {
                    city: '南通',
                    weather: weatherDesc,
                    currentTemp: parseInt(currentCondition.temp_C || '22'),
                    maxTemp: parseInt(todayWeather.maxtempC || '28'),
                    minTemp: parseInt(todayWeather.mintempC || '18'),
                    humidity: (currentCondition.humidity || '65') + '%',
                    pressure: (currentCondition.pressure || '1013') + ' hPa',
                    windSpeed: (currentCondition.windspeedKmph || '15') + ' km/h',
                    uvIndex: currentCondition.uvIndex || '3',
                    icon: getWeatherIcon(weatherDesc),
                    updateTime: new Date().toLocaleString('zh-CN'),
                    timestamp: Date.now()
                };

                console.log('成功获取真实天气数据:', weatherData);
        } catch (apiError) {
            console.warn('天气API调用失败，使用默认数据:', apiError.message);

            // API失败时使用默认数据
            weatherData = {
                city: '南通',
                weather: '晴朗',
                currentTemp: 22,
                maxTemp: 28,
                minTemp: 18,
                humidity: '65%',
                pressure: '1013 hPa',
                windSpeed: '15 km/h',
                uvIndex: '3',
                icon: '☀️',
                updateTime: new Date().toLocaleString('zh-CN'),
                timestamp: Date.now()
            };
        }

        res.json({
            success: true,
            data: weatherData,
            cached: false
        });
    } catch (error) {
        console.error('获取天气信息失败:', error);
        res.status(500).json({
            success: false,
            message: '获取天气信息失败',
            error: error.message
        });
    }
});

// 获取天气数据的辅助函数
function fetchWeatherData(url) {
    return new Promise((resolve, reject) => {
        https.get(url, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve(jsonData);
                } catch (error) {
                    reject(new Error('解析天气数据失败: ' + error.message));
                }
            });
        }).on('error', (error) => {
            reject(new Error('天气API请求失败: ' + error.message));
        });
    });
}

// 天气图标映射函数
function getWeatherIcon(weatherDesc) {
    if (!weatherDesc) return '🌤️';

    const desc = weatherDesc.toString().toLowerCase();

    // 晴天相关
    if (desc.includes('晴')) return '☀️';

    // 云相关
    if (desc.includes('多云')) return '⛅';
    if (desc.includes('阴')) return '☁️';

    // 雨相关
    if (desc.includes('雨')) {
        if (desc.includes('大雨') || desc.includes('暴雨')) return '🌧️';
        if (desc.includes('小雨') || desc.includes('毛毛雨')) return '🌦️';
        return '🌧️';
    }

    // 雪相关
    if (desc.includes('雪')) return '❄️';

    // 雾霾相关
    if (desc.includes('雾') || desc.includes('霾')) return '🌫️';

    // 雷电相关
    if (desc.includes('雷') || desc.includes('电')) return '⛈️';

    // 风相关
    if (desc.includes('风')) return '💨';

    // 默认
    return '🌤️';
}

// 保存路径规划历史
app.post('/api/routes/history', async (req, res) => {
    try {
        const { 
            user_id = 'anonymous',
            start_building_id, 
            end_building_id, 
            start_coordinates, 
            end_coordinates, 
            route_data, 
            distance, 
            estimated_time, 
            transport_mode = 'walking'
        } = req.body;
        
        const sql = `
            INSERT INTO route_history 
            (user_id, start_building_id, end_building_id, start_coordinates, 
             end_coordinates, route_data, distance, estimated_time, transport_mode)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        
        const params = [
            user_id, start_building_id, end_building_id, 
            JSON.stringify(start_coordinates), JSON.stringify(end_coordinates),
            JSON.stringify(route_data), distance, estimated_time, transport_mode
        ];
        
        const [result] = await db.execute(sql, params);
        
        res.status(201).json({
            success: true,
            message: '路径历史保存成功',
            data: { id: result.insertId }
        });
    } catch (error) {
        console.error('保存路径历史失败:', error);
        res.status(500).json({
            success: false,
            message: '保存路径历史失败',
            error: error.message
        });
    }
});

// 404错误处理
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: '接口不存在',
        path: req.originalUrl
    });
});

// 全局错误处理
app.use((error, req, res, next) => {
    console.error('服务器错误:', error);
    res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: error.message
    });
});

// 结束用户会话
app.post('/api/tracking/session/end', async (req, res) => {
    try {
        const { sessionId } = req.body;

        if (!sessionId) {
            return res.status(400).json({
                success: false,
                message: '缺少sessionId参数'
            });
        }

        // 计算会话持续时间
        const [sessions] = await db.execute(
            'SELECT start_time, last_activity FROM user_sessions WHERE session_id = ?',
            [sessionId]
        );

        if (sessions.length > 0) {
            const session = sessions[0];
            const duration = Math.floor((new Date(session.last_activity) - new Date(session.start_time)) / 1000);

            // 更新会话状态和持续时间
            await db.execute(
                'UPDATE user_sessions SET status = "ended", duration_seconds = ? WHERE session_id = ?',
                [duration, sessionId]
            );
        }

        res.json({
            success: true,
            message: '会话已结束'
        });
    } catch (error) {
        console.error('结束用户会话失败:', error);
        res.status(500).json({
            success: false,
            message: '结束会话失败',
            error: error.message
        });
    }
});

// 获取系统统计数据
app.get('/api/tracking/statistics', async (req, res) => {
    try {
        const { date, period = 'today' } = req.query;

        let dateCondition = '';
        let params = [];

        if (period === 'today') {
            dateCondition = 'DATE(timestamp) = CURDATE()';
        } else if (period === 'week') {
            dateCondition = 'timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        } else if (period === 'month') {
            dateCondition = 'timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
        } else if (date) {
            dateCondition = 'DATE(timestamp) = ?';
            params.push(date);
        }

        // 获取基本统计
        const [totalSessions] = await db.execute(
            `SELECT COUNT(DISTINCT session_id) as count FROM user_sessions WHERE ${dateCondition.replace('timestamp', 'start_time')}`,
            params
        );

        const [totalActions] = await db.execute(
            `SELECT COUNT(*) as count FROM user_actions WHERE ${dateCondition}`,
            params
        );

        const [avgDuration] = await db.execute(
            `SELECT AVG(duration_seconds) as avg_duration FROM user_sessions WHERE status = 'ended' AND ${dateCondition.replace('timestamp', 'start_time')}`,
            params
        );

        // 获取热门功能
        const [popularActions] = await db.execute(
            `SELECT action_type, action_name, COUNT(*) as count
             FROM user_actions
             WHERE ${dateCondition}
             GROUP BY action_type, action_name
             ORDER BY count DESC
             LIMIT 10`,
            params
        );

        res.json({
            success: true,
            data: {
                totalSessions: totalSessions[0].count,
                totalActions: totalActions[0].count,
                avgDuration: Math.round(avgDuration[0].avg_duration || 0),
                popularActions: popularActions
            }
        });
    } catch (error) {
        console.error('获取统计数据失败:', error);
        res.status(500).json({
            success: false,
            message: '获取统计数据失败',
            error: error.message
        });
    }
});

// 启动服务器
async function startServer() {
    try {
        // 测试数据库连接
        const dbConnected = await testConnection();
        if (!dbConnected) {
            console.error('❌ 数据库连接失败，服务器启动中止');
            process.exit(1);
        }

        // 初始化用户行为追踪表
        const trackingInitialized = await initUserTrackingTables();
        if (!trackingInitialized) {
            console.error('❌ 用户行为追踪表初始化失败');
            process.exit(1);
        }

        app.listen(PORT, () => {
            console.log('🚀 服务器启动成功!');
            console.log(`📍 地址: http://localhost:${PORT}`);
            console.log(`🗄️ 数据库: ${dbConfig.database}`);
            console.log(`📊 用户行为追踪: 已启用`);
            console.log('=' * 50);
        });

    } catch (error) {
        console.error('❌ 服务器启动失败:', error);
        process.exit(1);
    }
}

// 优雅关闭处理
process.on('SIGINT', async () => {
    console.log('\n🔄 正在关闭服务器...');
    try {
        await db.close();
        console.log('✅ 数据库连接已关闭');
        process.exit(0);
    } catch (error) {
        console.error('❌ 关闭数据库连接失败:', error);
        process.exit(1);
    }
});

process.on('SIGTERM', async () => {
    console.log('\n🔄 正在关闭服务器...');
    try {
        await db.close();
        console.log('✅ 数据库连接已关闭');
        process.exit(0);
    } catch (error) {
        console.error('❌ 关闭数据库连接失败:', error);
        process.exit(1);
    }
});

startServer();