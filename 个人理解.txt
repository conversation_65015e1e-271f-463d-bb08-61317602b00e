1、package-lock.json这里面是显示改项目使用到的依赖的版本
2、package.json这里面是运行项目需要安装的依赖，如mysql2：作用：Node.js 连接 MySQL 数据库的驱动程序
功能：
建立与 MySQL 数据库的连接
执行 SQL 查询语句
处理数据库返回的结果
管理连接池
3、接口	数据库表	前端调用	实际使用
/api/buildings	✅ buildings	✅ ApiModule	✅ 正在使用
/api/weather	❌ 无表	❌ 静态数据	❌ 未使用
/api/routes/history	⚠️ 表存在但无数据	❌ 未调用	❌ 未使用
/api/health	❌ 无表	✅ 状态检查	⚠️ 仅状态检查
因为好多接口需要的数据在数据库里没有，接口自然没用到。
4、完整数据流程
(1)
用户操作 → 在搜索框输入"图书馆"
前端处理 →  script.js 的 SearchModule 捕获输入
API调用 → ApiModule.searchBuildings('图书馆') 被调用
HTTP请求 → 发送 GET localhost:3001/api/buildings?search=图书馆
服务器接收 → server.js 接收并解析请求
数据库查询 → 执行 SQL: SELECT * FROM buildings WHERE name LIKE '%图书馆%'
数据库响应 → MySQL 返回图书馆的详细信息
服务器处理 → server.js 格式化数据为 JSON 格式
HTTP响应 → 返回 {success: true, data: [...], count: 1}
前端接收 → ApiModule 接收数据
结果显示 → SearchModule 显示搜索结果
用户交互 → 用户点击"查看详情"
信息展示 → 右侧面板显示完整的建筑物信息
(2)
npm start = 启动 server.js 服务器
server.js = 后端API服务器，提供数据接口
ApiModule = 前端API调用模块，负责与服务器通信
SearchModule = 前端搜索功能模块
MapModule = 前端地图和信息显示模块

3、用户点击图层，在数据库查找点击图层json数据的名字，如果没找到按照json数据返回显示。如果找到返回数据库的信息。
4、用户搜索西操场，会高亮显示json数据中名字里有"西操场"的图层，因为数据库里没有图层，要高亮显示，只能找到原始数据匹配搜索名字的图层！。