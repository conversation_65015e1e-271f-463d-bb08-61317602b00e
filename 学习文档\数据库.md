# 智慧校园系统数据库完整指南

## 📋 目录
1. [项目架构概述](#项目架构概述)
2. [数据库环境搭建](#数据库环境搭建)
3. [依赖包管理详解](#依赖包管理详解)
4. [接口使用状况分析](#接口使用状况分析)
5. [完整数据流程](#完整数据流程)
6. [核心模块说明](#核心模块说明)
7. [故障排除指南](#故障排除指南)

---

## 🎯 项目架构概述

智慧校园系统采用**前后端分离**架构，通过数据库实现动态数据管理：

```
前端 (index.html + script.js)
    ↕ HTTP API 调用
后端 (server.js + Express)
    ↕ SQL 查询
数据库 (MySQL + campus_map)
```

### 核心特点
- ✅ **动态数据**：建筑物信息存储在数据库中，可实时更新
- ✅ **API驱动**：前后端通过RESTful API通信
- ✅ **模块化设计**：前端功能模块化，后端接口标准化

### 🔍 RESTful API 通信详解

#### 📖 基本概念

**REST** = **RE**presentational **S**tate **T**ransfer（表现层状态转移）
**API** = **A**pplication **P**rogramming **I**nterface（应用程序编程接口）

**RESTful API** 就是遵循REST设计原则的API接口。

#### 🎯 在本项目中的具体体现

**什么是"通信"？**
前端（浏览器中的JavaScript）和后端（服务器上的Node.js）之间的数据交换：

```
前端 (script.js)  ←→  后端 (server.js)
     浏览器              服务器
```

**RESTful API的特点：**

**🌐 使用HTTP协议**
```javascript
// 前端发送HTTP请求
fetch('http://localhost:3001/api/buildings?search=图书馆')

// 后端接收HTTP请求
app.get('/api/buildings', (req, res) => {
    // 处理请求
});
```

**📍 资源导向的URL设计**
```
/api/buildings          # 建筑物资源
/api/buildings/11       # ID为11的建筑物
/api/weather            # 天气资源
/api/health             # 健康检查资源
```

**🔧 使用标准HTTP方法**
```javascript
GET    /api/buildings     # 获取建筑物列表
GET    /api/buildings/11  # 获取特定建筑物
POST   /api/buildings     # 创建新建筑物
PUT    /api/buildings/11  # 更新建筑物
DELETE /api/buildings/11  # 删除建筑物
```

**📦 JSON数据格式**
```javascript
// 请求数据
{
    "search": "图书馆",
    "type": "公共建筑"
}

// 响应数据
{
    "success": true,
    "data": [
        {
            "id": 11,
            "name": "图书馆",
            "type": "公共建筑"
        }
    ],
    "count": 1
}
```

#### 🔄 实际通信过程示例

**第1步：前端发起请求**
```javascript
// script.js 中的 ApiModule
async searchBuildings(query) {
    const response = await fetch(`${this.baseUrl}/buildings?search=${query}`, {
        method: 'GET',                    // HTTP方法
        headers: {
            'Content-Type': 'application/json'  // 数据格式
        }
    });
    return await response.json();
}
```

**第2步：后端接收处理**
```javascript
// server.js 中的路由处理
app.get('/api/buildings', async (req, res) => {
    const { search } = req.query;        // 获取查询参数

    // 查询数据库
    const sql = 'SELECT * FROM buildings WHERE name LIKE ?';
    const [rows] = await pool.execute(sql, [`%${search}%`]);

    // 返回JSON响应
    res.json({
        success: true,
        data: rows,
        count: rows.length
    });
});
```

**第3步：前端接收响应**
```javascript
// 前端处理返回的数据
const result = await ApiModule.searchBuildings('图书馆');
if (result.success) {
    console.log('找到建筑物:', result.data);
    // 显示搜索结果
}
```

#### 💡 RESTful API的优势

- **标准化**：使用HTTP标准协议，统一的请求/响应格式
- **无状态**：每个请求都是独立的，便于扩展和负载均衡
- **可缓存**：GET请求可以被缓存，提高性能
- **跨平台**：任何支持HTTP的设备都可以调用，不限制编程语言

#### 🔍 用户搜索"图书馆"的完整RESTful通信过程

```
1. 前端构造请求：
   GET http://localhost:3001/api/buildings?search=图书馆

2. 后端解析请求：
   - 路径: /api/buildings (建筑物资源)
   - 方法: GET (获取数据)
   - 参数: search=图书馆 (查询条件)

3. 后端处理并响应：
   {
     "success": true,
     "data": [
       {
         "id": 11,
         "name": "图书馆",
         "type": "公共建筑",
         "description": "南通大学主图书馆，学习研究中心"
       }
     ],
     "count": 1
   }

4. 前端接收并显示：
   - 解析JSON数据
   - 渲染搜索结果
   - 显示在用户界面
```

**总结**："前后端通过RESTful API通信" 意味着前端通过发送HTTP请求到特定的URL（如 `/api/buildings`），后端接收请求并返回JSON格式的数据。这种方式让前端和后端可以**独立开发**、**独立部署**，只要约定好API接口格式就可以协同工作！

---

## 🛠️ 数据库环境搭建

### 1️⃣ 安装必要软件

**MySQL数据库**
- 下载：https://dev.mysql.com/downloads/mysql/
- 安装时记住设置的root密码

**Node.js运行环境**
- 下载：https://nodejs.org/
- 选择LTS版本（推荐）

### 2️⃣ 创建数据库

```bash
# 连接MySQL
mysql -u root -p
```

```sql
-- 创建数据库
CREATE DATABASE campus_map;
USE campus_map;

-- 创建建筑物表(按照实际信息，以下为演示)
CREATE TABLE buildings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    description TEXT,
    longitude DECIMAL(10, 7) NOT NULL,
    latitude DECIMAL(10, 7) NOT NULL,
    floor_count INT DEFAULT 1,
    area DECIMAL(10, 2),
    build_year YEAR,
    status ENUM('active', 'maintenance', 'closed') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入示例数据
INSERT INTO buildings (name, type, description, longitude, latitude, floor_count, area, build_year) VALUES
('图书馆', '公共建筑', '南通大学主图书馆，学习研究中心', 120.904569, 31.975849, 12, 14844.00, 2010),
('第一教学楼', '教学建筑', '主要的教学楼', 120.907800, 31.975200, 5, 8000.00, 2008),
('学生食堂', '食堂', '学生用餐场所', 120.909200, 31.974800, 3, 3000.00, 2009);
```

### 3️⃣ 配置后端服务

**修改数据库连接配置**
编辑 `后端服务示例/server.js` 第19行：
```javascript
password: '你的MySQL密码',  // 替换为实际密码
```

**安装依赖包**
```bash
cd 后端服务示例
npm install
```

**启动服务器**
```bash
npm start
```

---

## 📦 依赖包管理详解

### package.json - 依赖声明文件
定义项目运行所需的依赖包及其版本范围：

#### 🔗 核心依赖包

**1. mysql2 - 数据库驱动**
```json
"mysql2": "^3.6.0"
```
- **作用**：Node.js连接MySQL数据库的核心驱动程序
- **功能**：
  - 建立与MySQL数据库的连接
  - 执行SQL查询语句
  - 处理数据库返回的结果
  - 管理连接池，提高性能
- **使用示例**：
  ```javascript
  const mysql = require('mysql2/promise');
  const pool = mysql.createPool(dbConfig);
  const [rows] = await pool.execute(sql, params);
  ```

**2. express - Web服务器框架**
```json
"express": "^4.18.2"
```
- **作用**：创建HTTP服务器，提供API接口
- **功能**：
  - 处理HTTP请求和响应
  - 路由管理（/api/buildings等）
  - 中间件支持
  - 静态文件服务

**3. cors - 跨域资源共享**
```json
"cors": "^2.8.5"
```
- **作用**：解决浏览器跨域访问限制
- **功能**：
  - 允许前端页面访问后端API
  - 配置跨域请求头
  - 处理预检请求

**4. dotenv - 环境变量管理**
```json
"dotenv": "^16.3.1"
```
- **作用**：安全管理敏感配置信息
- **功能**：
  - 从.env文件读取配置
  - 保护数据库密码等敏感信息
  - 支持不同环境配置

#### 🛠️ 开发依赖

**nodemon - 开发工具**
```json
"nodemon": "^3.0.1"
```
- **作用**：开发时自动重启服务器
- **使用**：`npm run dev` 启动开发模式

### package-lock.json - 版本锁定文件
- **作用**：锁定所有依赖包的确切版本号
- **功能**：
  - 确保团队成员使用相同版本的依赖
  - 记录完整的依赖关系树
  - 提高安装速度和稳定性
- **重要性**：应该提交到版本控制系统

---

## 📊 接口使用状况分析

### 当前接口状态对比表

| 接口路径 | 数据库表 | 前端调用 | 实际使用状态 | 说明 |
|----------|----------|----------|--------------|------|
| `/api/buildings` | ✅ buildings | ✅ ApiModule | ✅ **正在使用** | 核心功能，建筑物搜索和信息显示 |
| `/api/weather` | ❌ 无对应表 | ❌ 使用静态数据 | ❌ **未使用** | 返回硬编码的模拟天气数据 |
| `/api/routes/history` | ⚠️ 表存在但无数据 | ❌ 未调用 | ❌ **未使用** | 路径规划历史记录功能未实现 |
| `/api/health` | ❌ 无对应表 | ✅ 状态检查 | ⚠️ **仅状态检查** | 用于检查服务器和数据库连接状态 |

### 详细分析

**✅ 正在使用的接口**
- **建筑物接口**：完整的CRUD操作，支持搜索、详情查询
- **健康检查接口**：用于系统状态监控

**❌ 预留但未使用的接口**

- **天气接口**：缺少真实天气数据源
- **路径历史接口**：前端未实现历史记录功能

**💡 设计说明**
这是典型的**MVP（最小可行产品）**设计模式：
- 优先实现核心功能（建筑物搜索）
- 预留扩展接口，便于后续开发
- 保持代码结构的可扩展性

---

## 🔄 完整数据流程

### 用户搜索建筑物的完整流程

```mermaid
graph TD
    A[用户在搜索框输入'图书馆'] --> B[前端 script.js]
    B --> C[SearchModule 捕获输入]
    C --> D[ApiModule.searchBuildings调用]
    D --> E[HTTP GET 请求<br/>localhost:3001/api/buildings?search=图书馆]
    E --> F[server.js 接收请求]
    F --> G[解析查询参数 search='图书馆']
    G --> H[构建 SQL 查询<br/>SELECT * FROM buildings WHERE name LIKE '%图书馆%']
    H --> I[MySQL 数据库执行查询]
    I --> J[返回查询结果<br/>{id: 11, name: '图书馆', type: '公共建筑'...}]
    J --> K[server.js 处理和格式化数据]
    K --> L[HTTP JSON 响应<br/>{success: true, data: [...], count: 1}]
    L --> M[ApiModule 接收数据]
    M --> N[SearchModule 处理搜索结果]
    N --> O[显示搜索结果列表]
    O --> P[用户点击'查看详情']
    P --> Q[SearchModule.selectBuilding调用]
    Q --> R[MapModule.showBuildingInfo显示]
    R --> S[右侧信息面板显示完整信息]
```

### 详细步骤说明

1. **用户操作** → 在搜索框输入"图书馆"
2. **前端处理** → `script.js` 的 `SearchModule` 捕获输入
3. **API调用** → `ApiModule.searchBuildings('图书馆')` 被调用
4. **HTTP请求** → 发送 `GET localhost:3001/api/buildings?search=图书馆`
5. **服务器接收** → `server.js` 接收并解析请求参数
6. **数据库查询** → 执行 SQL: `SELECT * FROM buildings WHERE name LIKE '%图书馆%'`
7. **数据库响应** → MySQL 返回图书馆的详细信息
8. **服务器处理** → `server.js` 格式化数据为标准 JSON 格式
9. **HTTP响应** → 返回 `{success: true, data: [...], count: 1}`
10. **前端接收** → `ApiModule` 接收并验证数据
11. **结果显示** → `SearchModule` 渲染搜索结果列表
12. **用户交互** → 用户点击"查看详情"按钮
13. **信息展示** → 右侧面板显示完整的建筑物信息

---

## 🧩 核心模块说明

### 后端核心

**server.js** - 后端API服务器
- **作用**：提供数据接口，连接前端和数据库
- **功能**：
  - 创建HTTP服务器（端口3001）
  - 管理数据库连接池
  - 处理API请求和响应
  - 错误处理和日志记录

### 前端核心模块

**ApiModule** - 前端API调用模块
- **作用**：负责与后端服务器通信
- **功能**：
  - 封装HTTP请求
  - 处理响应数据
  - 错误处理和重试机制

**SearchModule** - 前端搜索功能模块
- **作用**：处理建筑物搜索和结果显示
- **功能**：
  - 搜索输入处理
  - 结果列表渲染
  - 用户交互处理

**MapModule** - 前端地图和信息显示模块
- **作用**：地图显示和建筑物信息面板
- **功能**：
  - 地图渲染和交互
  - 建筑物高亮显示
  - 信息面板管理

### 启动命令说明

**npm start** = 启动 `server.js` 服务器
- 等同于 `node server.js`
- 启动后端API服务
- 监听3001端口

---

## 🚨 故障排除指南

### 常见错误及解决方案

**1. 数据库连接失败**
```
❌ 数据库连接失败: connect ECONNREFUSED
```
**解决方案**：
- 检查MySQL服务是否启动
- 验证数据库密码是否正确
- 确认数据库名称 `campus_map` 是否存在

**2. 端口被占用**
```
❌ Error: listen EADDRINUSE: address already in use :::3001
```
**解决方案**：
- 关闭占用3001端口的其他程序
- 或修改 `server.js` 中的 `PORT` 为其他端口（如3002）

**3. 依赖安装失败**
```
❌ npm install 失败
```
**解决方案**：
```bash
npm cache clean --force
npm install
```

**4. 前端无法获取数据**
**检查项目**：
- 后端服务是否正常启动
- 数据库连接是否成功
- 浏览器控制台是否有CORS错误
- API接口地址是否正确

### 成功运行标志

✅ **后端启动成功**：
```
🚀 服务器启动成功!
📍 地址: http://localhost:3001
🗄️ 数据库: campus_map
✅ 数据库连接成功
```

✅ **前端功能正常**：
- 页面正常显示地图
- 搜索"图书馆"能找到结果
- 点击建筑物能看到详细信息
- 右侧信息面板显示数据库信息

---

## 💡 系统优势

### 使用数据库的好处
- 🔄 **动态更新**：数据可以实时修改，无需重新部署
- 🔍 **高效搜索**：数据库索引提供快速查询
- 📊 **数据统计**：可以添加访问统计、热门建筑等功能
- 👥 **多用户支持**：支持并发访问和数据一致性
- 🚀 **可扩展性**：为未来功能扩展打下基础

### 架构优势
- 🏗️ **模块化设计**：前后端职责分离，便于维护
- 🔌 **接口标准化**：RESTful API设计，易于集成
- 🛡️ **安全性**：数据库连接池、参数化查询防止SQL注入
- ⚡ **性能优化**：连接池管理、缓存机制

---

## 📞 技术支持

如果遇到问题，请提供以下信息：
1. 具体的错误信息
2. 操作系统类型
3. Node.js和MySQL版本
4. 执行的具体步骤

这样可以更快速地定位和解决问题！
