directories:
  output: dist
  buildResources: build
appId: com.campus.smart-system
productName: 智慧校园系统
copyright: Copyright © 2025 智慧校园开发团队
buildVersion: 2.0.0
electronVersion: 28.0.0
nodeGypRebuild: false
buildDependenciesFromSource: false
npmRebuild: false
forceCodeSigning: false
icon: icon.ico
win:
  target:
    - target: nsis
      arch:
        - x64
  icon: icon.ico
  requestedExecutionLevel: asInvoker
  sign: null
  certificateFile: null
  certificatePassword: <stripped sensitive data>
  verifyUpdateCodeSignature: false
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: 智慧校园系统
files:
  - filter:
      - main.js
      - index.html
      - script.js
      - i18n.js
      - icon.ico
      - ntu.ico
      - LICENSE.txt
      - data/**/*
      - geojson2_data/**/*
      - '!node_modules/**/*'
      - '!server/**/*'
      - '!dist/**/*'
      - '!docs/**/*'
      - '!md文档/**/*'
      - '!map_data2/**/*'
      - '!南通大学校徽.ico'
      - '!南通大学校徽.png'
      - '!个人理解.txt'
      - '!项目打包完整指南.md'
      - '!智慧校园系统安装包.exe'
      - '!summary.txt'
      - '!summary.md'
      - '!export_sqlite_data.py'
      - '!generate_buildings_js.py'
      - '!buildings_data.json'
      - '!buildings_sample.json'
