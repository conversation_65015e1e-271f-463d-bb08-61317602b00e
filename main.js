const { app, BrowserWindow, dialog, shell, Menu } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');
const os = require('os');

// 全局变量
let mainWindow;
let expressServer;
let isQuitting = false;

// 设置应用程序名称（中文支持）
app.setName('智慧校园系统');

// 确保单实例运行
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    console.log('应用程序已在运行，退出当前实例');
    app.quit();
} else {
    app.on('second-instance', () => {
        // 当运行第二个实例时，将焦点放在主窗口上
        if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.focus();
        }
    });
}

/**
 * 查找图标文件 - 支持多路径检查（包括打包后的路径）
 */
function findIconFile() {
    const isPackaged = app.isPackaged;
    console.log(`查找图标文件，打包状态: ${isPackaged ? '已打包' : '开发环境'}`);

    let possiblePaths;

    if (isPackaged) {
        // 打包后的路径 - 参考成功项目的路径结构
        possiblePaths = [
            // 优先在应用程序根目录查找（与exe同级）- 优先使用icon.ico作为窗口图标
            path.join(path.dirname(process.execPath), 'icon.ico'),
            path.join(path.dirname(process.execPath), '南通大学校徽.ico'),
            // 然后在resources目录查找
            path.join(process.resourcesPath, 'icon.ico'),
            path.join(process.resourcesPath, '南通大学校徽.ico'),
            // 最后在app.asar内部查找
            path.join(process.resourcesPath, 'app.asar', 'icon.ico'),
            path.join(process.resourcesPath, 'app.asar', '南通大学校徽.ico'),
            // 备用路径
            path.join(__dirname, 'icon.ico'),
            path.join(__dirname, '南通大学校徽.ico')
        ];
    } else {
        // 开发环境路径 - 优先使用icon.ico作为窗口图标
        possiblePaths = [
            path.join(__dirname, 'icon.ico'),
            path.join(__dirname, '南通大学校徽.ico')
        ];
    }

    console.log('正在搜索图标文件...');
    for (const iconPath of possiblePaths) {
        console.log(`检查图标路径: ${iconPath}`);
        if (fs.existsSync(iconPath)) {
            console.log(`✅ 找到图标文件: ${iconPath}`);
            return iconPath;
        }
        console.log(`❌ 图标文件不存在: ${iconPath}`);
    }

    console.log('⚠️ 未找到图标文件，使用默认图标');
    return null; // 返回null，让Electron使用默认图标
}

/**
 * 查找数据库文件 - 支持多路径检查（包括打包后的路径）
 */
function findDatabaseFile() {
    // 检测是否为打包环境
    const isPackaged = app.isPackaged;
    console.log(`应用程序打包状态: ${isPackaged ? '已打包' : '开发环境'}`);

    let possiblePaths;

    if (isPackaged) {
        // 打包后的路径（优先使用asar内部路径）
        possiblePaths = [
            path.join(__dirname, 'server', 'campus_map.db'),  // asar内部路径
            path.join(process.resourcesPath, 'app.asar', 'server', 'campus_map.db'),
            path.join(process.resourcesPath, 'server', 'campus_map.db'),
            path.join(__dirname, 'campus_map.db'),
            path.join(process.resourcesPath, 'campus_map.db')
        ];
    } else {
        // 开发环境路径
        possiblePaths = [
            path.join(__dirname, 'server', 'campus_map.db'),
            path.join(__dirname, 'campus_map.db')
        ];
    }

    console.log('正在搜索数据库文件...');
    for (const dbPath of possiblePaths) {
        console.log(`检查路径: ${dbPath}`);
        if (fs.existsSync(dbPath)) {
            console.log(`✅ 找到数据库文件: ${dbPath}`);
            return dbPath;
        }
        console.log(`❌ 数据库文件不存在: ${dbPath}`);
    }

    console.warn('⚠️ 未找到数据库文件，将使用静态数据模式');
    return null; // 返回null而不是抛出错误，让应用继续运行
}

/**
 * 创建空数据库 - 备用方案
 */
function createEmptyDatabase(targetPath) {
    console.log('🔧 创建空数据库作为备用方案...');

    try {
        // 创建一个基本的SQLite数据库文件
        const fs = require('fs');

        // 创建空文件
        fs.writeFileSync(targetPath, '');

        console.log('✅ 空数据库文件创建完成');
        console.log('⚠️ 注意：使用的是空数据库，部分功能可能受限');

        return targetPath;
    } catch (error) {
        console.error('❌ 创建空数据库失败:', error.message);
        throw error;
    }
}

/**
 * 初始化数据库 - 支持中文路径和错误恢复
 */
async function initDatabase() {
    try {
        console.log('📊 开始初始化数据库...');

        // 获取用户数据目录（支持中文用户名）
        const userDataPath = app.getPath('userData');
        const targetDb = path.join(userDataPath, 'campus_map.db');

        console.log(`用户数据目录: ${userDataPath}`);
        console.log(`目标数据库路径: ${targetDb}`);

        // 确保用户数据目录存在
        if (!fs.existsSync(userDataPath)) {
            fs.mkdirSync(userDataPath, { recursive: true });
            console.log('✅ 用户数据目录创建成功');
        }

        // 检查数据库是否已存在
        if (!fs.existsSync(targetDb)) {
            try {
                // 尝试找到源数据库文件
                const sourceDb = findDatabaseFile();

                if (!sourceDb) {
                    throw new Error('源数据库文件不存在');
                }

                console.log(`源数据库路径: ${sourceDb}`);

                // 复制数据库文件
                fs.copyFileSync(sourceDb, targetDb);
                console.log('✅ 数据库复制完成');

                // 验证复制成功
                const sourceStats = fs.statSync(sourceDb);
                const targetStats = fs.statSync(targetDb);

                if (sourceStats.size === targetStats.size) {
                    console.log(`✅ 数据库复制验证成功 (${targetStats.size} bytes)`);
                } else {
                    throw new Error('数据库复制验证失败：文件大小不匹配');
                }

            } catch (copyError) {
                console.warn('⚠️ 数据库复制失败，尝试创建空数据库:', copyError.message);

                try {
                    createEmptyDatabase(targetDb);
                    console.log('✅ 使用空数据库作为备用方案');
                } catch (createError) {
                    console.error('❌ 创建空数据库也失败:', createError.message);
                    // 不抛出错误，而是返回null，让应用继续运行
                    console.log('🔄 将使用静态数据模式运行');
                    return null;
                }
            }
        } else {
            console.log('✅ 数据库已存在，跳过初始化');
        }

        return targetDb;
    } catch (error) {
        console.error('❌ 数据库初始化失败:', error.message);
        console.log('🔄 将使用静态数据模式运行');
        return null;

        // 显示更友好的错误对话框
        dialog.showErrorBox('数据库初始化失败',
            `数据库初始化遇到问题：${error.message}\n\n` +
            `可能的解决方案：\n` +
            `1. 重新安装应用程序\n` +
            `2. 检查磁盘空间是否充足\n` +
            `3. 确保有足够的文件访问权限\n` +
            `4. 关闭杀毒软件后重试\n\n` +
            `如果问题持续存在，请联系技术支持。`);

        throw error;
    }
}

/**
 * 启动HTTP服务器 - 使用Node.js内置模块
 */
function startExpressServer() {
    return new Promise((resolve, reject) => {
        try {
            console.log('🚀 启动HTTP服务器...');

            // 使用Node.js内置HTTP服务器（无外部依赖）
            console.log('🚀 启动内置HTTP服务器');
            try {
                const http = require('http');
                const fs = require('fs');
                const path = require('path');
                const url = require('url');

                // 创建HTTP服务器
                const server = http.createServer((req, res) => {
                    // 设置CORS头
                    res.setHeader('Access-Control-Allow-Origin', '*');
                    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
                    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

                    if (req.method === 'OPTIONS') {
                        res.writeHead(200);
                        res.end();
                        return;
                    }

                    const parsedUrl = url.parse(req.url, true);
                    const pathname = parsedUrl.pathname;

                    // 静态文件服务
                    if (pathname === '/' || pathname === '/index.html') {
                        const indexPath = path.join(__dirname, 'index.html');
                        if (fs.existsSync(indexPath)) {
                            const content = fs.readFileSync(indexPath, 'utf8');
                            res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
                            res.end(content);
                        } else {
                            res.writeHead(404, { 'Content-Type': 'text/plain' });
                            res.end('Index file not found');
                        }
                        return;
                    }

                    // 其他静态文件
                    if (!pathname.startsWith('/api/')) {
                        const filePath = path.join(__dirname, pathname);
                        if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
                            const ext = path.extname(filePath);
                            const contentTypes = {
                                '.html': 'text/html; charset=utf-8',
                                '.js': 'application/javascript',
                                '.css': 'text/css',
                                '.json': 'application/json',
                                '.png': 'image/png',
                                '.jpg': 'image/jpeg',
                                '.gif': 'image/gif',
                                '.ico': 'image/x-icon'
                            };
                            const contentType = contentTypes[ext] || 'application/octet-stream';

                            const content = fs.readFileSync(filePath);
                            res.writeHead(200, { 'Content-Type': contentType });
                            res.end(content);
                        } else {
                            res.writeHead(404, { 'Content-Type': 'text/plain' });
                            res.end('File not found');
                        }
                        return;
                    }

                    // 健康检查接口
                    if (pathname === '/api/health') {
                        res.writeHead(200, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({
                            status: 'ok',
                            timestamp: new Date().toISOString(),
                            database: 'static_data'
                        }));
                        return;
                    }

                    // 服务器配置接口 - 返回当前端口等信息
                    if (pathname === '/api/config') {
                        res.writeHead(200, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({
                            port: global.serverPort || 3002,
                            baseUrl: `http://localhost:${global.serverPort || 3002}`,
                            timestamp: new Date().toISOString()
                        }));
                        return;
                    }

                    // 建筑物查询接口 - 使用SQLite数据库
                    if (pathname === '/api/buildings') {
                        console.log('🏢 收到建筑物查询请求');

                        // 备用数据函数
                        function sendFallbackBuildingData() {
                            console.log('� 使用备用建筑物数据');
                            const buildings = [
                                { id: 1, name: '教学楼1号', type: '教学建筑', description: '第一教学楼，主要用于基础课程教学', coordinates: [120.9078, 31.9752], area: 4500, floor_count: 6, build_year: 1995, status: '正常使用', longitude: 120.9078, latitude: 31.9752 },
                                { id: 2, name: '教学楼2号', type: '教学建筑', description: '第二教学楼，主要用于专业课程教学', coordinates: [120.9082, 31.975], area: 4200, floor_count: 6, build_year: 1996, status: '正常使用', longitude: 120.9082, latitude: 31.975 },
                                { id: 3, name: '教学楼3号', type: '教学建筑', description: '第三教学楼，多媒体教学中心', coordinates: [120.9075, 31.9748], area: 3800, floor_count: 5, build_year: 1998, status: '正常使用', longitude: 120.9075, latitude: 31.9748 },
                                { id: 4, name: '教学楼4号、公共教学楼', type: '教学建筑', description: '第四教学楼，公共教学楼', coordinates: [120.908, 31.9755], area: 5200, floor_count: 7, build_year: 2001, status: '正常使用', longitude: 120.908, latitude: 31.9755 },
                                { id: 5, name: '教学楼5号', type: '教学建筑', description: '第五教学楼，大型阶梯教室', coordinates: [120.9085, 31.9753], area: 4800, floor_count: 6, build_year: 2003, status: '正常使用', longitude: 120.9085, latitude: 31.9753 },
                                { id: 11, name: '图书馆', type: '公共建筑', description: '南通大学主图书馆，学习研究中心', coordinates: [120.9085, 31.9745], area: 12000, floor_count: 10, build_year: 2004, status: '正常使用', longitude: 120.9085, latitude: 31.9745 }
                            ];

                            const { search, type } = parsedUrl.query;
                            let filteredBuildings = buildings;

                            // 按类型筛选
                            if (type && type !== 'all') {
                                filteredBuildings = filteredBuildings.filter(b => b.type === type);
                            }

                            // 按搜索关键词筛选
                            if (search) {
                                filteredBuildings = filteredBuildings.filter(b =>
                                    b.name.includes(search) || b.description.includes(search)
                                );
                            }

                            res.writeHead(200, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({
                                success: true,
                                data: filteredBuildings,
                                total: filteredBuildings.length,
                                source: 'fallback'
                            }));
                        }

                        try {
                            // 尝试从SQLite数据库获取数据
                            console.log('📊 尝试加载SQLite模块...');
                            const sqlite3 = require('sqlite3').verbose();
                            console.log('✅ SQLite模块加载成功');

                            console.log('🔍 查找数据库文件...');
                            const dbPath = findDatabaseFile();
                            console.log('📍 数据库路径:', dbPath);

                            // 使用同步方式打开数据库
                            const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY);

                            const { search, type } = parsedUrl.query;
                            let sql = 'SELECT * FROM buildings';
                            let params = [];

                            console.log('🔍 查询参数 - search:', search, 'type:', type);

                            // 构建查询条件
                            const conditions = [];
                            if (type && type !== 'all') {
                                conditions.push('type = ?');
                                params.push(type);
                            }
                            if (search) {
                                conditions.push('(name LIKE ? OR description LIKE ?)');
                                params.push(`%${search}%`, `%${search}%`);
                            }

                            if (conditions.length > 0) {
                                sql += ' WHERE ' + conditions.join(' AND ');
                            }

                            console.log('📝 执行SQL:', sql);
                            console.log('📝 参数:', params);

                            db.all(sql, params, (err, rows) => {
                                db.close();

                                if (err) {
                                    console.error('❌ 数据库查询失败:', err.message);
                                    console.error('❌ SQL:', sql);
                                    console.error('❌ 参数:', params);
                                    sendFallbackBuildingData();
                                    return;
                                }

                                console.log(`✅ 从数据库获取到 ${rows.length} 个建筑物`);

                                // 打印第一个建筑物的详细信息用于调试
                                if (rows.length > 0) {
                                    console.log('🏢 第一个建筑物数据:', {
                                        id: rows[0].id,
                                        name: rows[0].name,
                                        floor_count: rows[0].floor_count,
                                        build_year: rows[0].build_year,
                                        area: rows[0].area
                                    });
                                }

                                // 转换数据格式，确保coordinates字段正确
                                const buildings = rows.map(row => ({
                                    id: row.id,
                                    name: row.name,
                                    type: row.type,
                                    description: row.description,
                                    coordinates: [row.longitude, row.latitude],
                                    area: row.area,
                                    floor_count: row.floor_count,
                                    build_year: row.build_year,
                                    status: row.status,
                                    longitude: row.longitude,
                                    latitude: row.latitude
                                }));

                                console.log('📤 返回数据示例:', buildings.length > 0 ? {
                                    id: buildings[0].id,
                                    name: buildings[0].name,
                                    floor_count: buildings[0].floor_count,
                                    build_year: buildings[0].build_year
                                } : '无数据');

                                res.writeHead(200, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({
                                    success: true,
                                    data: buildings,
                                    total: buildings.length,
                                    source: 'database'
                                }));
                            });

                        } catch (error) {
                            console.error('❌ 数据库操作异常:', error.message);
                            sendFallbackBuildingData();
                        }
                        return;
                    }

                    // 建筑物类型查询接口
                    if (pathname === '/api/building-types') {
                    const types = [
                        { id: 'all', name: '全部', count: 34 },
                        { id: '教学建筑', name: '教学建筑', count: 15 },
                        { id: '公共建筑', name: '公共建筑', count: 3 },
                        { id: '宿舍建筑', name: '宿舍建筑', count: 7 },
                        { id: '食堂', name: '食堂', count: 3 },
                        { id: '体育建筑', name: '体育建筑', count: 3 },
                        { id: '服务建筑', name: '服务建筑', count: 2 },
                        { id: '行政建筑', name: '行政建筑', count: 1 }
                    ];

                        res.writeHead(200, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({
                            success: true,
                            data: types
                        }));
                        return;
                    }

                    // 天气查询API接口
                    if (pathname === '/api/weather') {
                        console.log('收到天气查询请求');

                        // 尝试获取真实天气数据
                        try {
                            const https = require('https');
                            const weatherApiUrl = 'https://wttr.in/南通?format=j1';

                            const weatherReq = https.get(weatherApiUrl, {
                                headers: {
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                                },
                                timeout: 8000
                            }, (weatherRes) => {
                                let data = '';

                                weatherRes.on('data', (chunk) => {
                                    data += chunk;
                                });

                                weatherRes.on('end', () => {
                                    try {
                                        const weatherData = JSON.parse(data);
                                        console.log('✅ 天气API数据获取成功');

                                        // 转换数据格式
                                        const currentCondition = weatherData.current_condition?.[0] || {};
                                        const nearestArea = weatherData.nearest_area?.[0] || {};
                                        const todayWeather = weatherData.weather?.[0] || {};

                                        const transformedData = {
                                            city: nearestArea.areaName?.[0]?.value || '南通',
                                            weather: currentCondition.lang_zh?.[0]?.value || currentCondition.weatherDesc?.[0]?.value || '晴朗',
                                            currentTemp: parseInt(currentCondition.temp_C || '22'),
                                            maxTemp: parseInt(todayWeather.maxtempC || '28'),
                                            minTemp: parseInt(todayWeather.mintempC || '18'),
                                            humidity: (currentCondition.humidity || '65') + '%',
                                            pressure: (currentCondition.pressure || '1013') + ' hPa',
                                            windSpeed: (currentCondition.windspeedKmph || '15') + ' km/h',
                                            uvIndex: currentCondition.uvIndex || '3',
                                            date: todayWeather.date || new Date().toISOString().split('T')[0],
                                            updateTime: new Date().toLocaleString('zh-CN'),
                                            timestamp: Date.now()
                                        };

                                        res.writeHead(200, { 'Content-Type': 'application/json' });
                                        res.end(JSON.stringify({
                                            success: true,
                                            data: transformedData
                                        }));
                                    } catch (parseError) {
                                        console.error('❌ 天气数据解析失败:', parseError.message);
                                        sendDefaultWeather();
                                    }
                                });
                            });

                            weatherReq.on('error', (error) => {
                                console.error('❌ 天气API请求失败:', error.message);
                                sendDefaultWeather();
                            });

                            weatherReq.on('timeout', () => {
                                console.error('❌ 天气API请求超时');
                                weatherReq.destroy();
                                sendDefaultWeather();
                            });

                            // 默认天气数据发送函数
                            function sendDefaultWeather() {
                                const defaultWeather = {
                                    city: '南通',
                                    weather: '晴朗',
                                    currentTemp: 22,
                                    maxTemp: 28,
                                    minTemp: 18,
                                    humidity: '65%',
                                    pressure: '1013 hPa',
                                    windSpeed: '15 km/h',
                                    uvIndex: '3',
                                    date: new Date().toISOString().split('T')[0],
                                    updateTime: new Date().toLocaleString('zh-CN'),
                                    timestamp: Date.now()
                                };

                                res.writeHead(200, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({
                                    success: true,
                                    data: defaultWeather,
                                    note: '使用默认天气数据（API暂时不可用）'
                                }));
                            }

                        } catch (error) {
                            console.error('❌ 天气API处理失败:', error.message);

                            // 返回默认天气数据
                            const defaultWeather = {
                                city: '南通',
                                weather: '晴朗',
                                currentTemp: 22,
                                maxTemp: 28,
                                minTemp: 18,
                                humidity: '65%',
                                pressure: '1013 hPa',
                                windSpeed: '15 km/h',
                                uvIndex: '3',
                                date: new Date().toISOString().split('T')[0],
                                updateTime: new Date().toLocaleString('zh-CN'),
                                timestamp: Date.now()
                            };

                            res.writeHead(200, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({
                                success: true,
                                data: defaultWeather,
                                note: '使用默认天气数据（API处理异常）'
                            }));
                        }
                        return;
                    }

                    // 404处理
                    res.writeHead(404, { 'Content-Type': 'text/plain' });
                    res.end('API not found');
                });

                // 启动服务器 - 自动寻找可用端口
                const startPort = 3002;
                const maxPort = 3010;

                const tryPort = (port) => {
                    server.listen(port, () => {
                        console.log(`✅ HTTP服务器启动成功 (端口: ${port})`);
                        global.serverPort = port; // 保存实际使用的端口
                        resolve();
                    });

                    server.on('error', (err) => {
                        if (err.code === 'EADDRINUSE' && port < maxPort) {
                            console.log(`⚠️ 端口 ${port} 被占用，尝试端口 ${port + 1}`);
                            server.removeAllListeners('error');
                            tryPort(port + 1);
                        } else {
                            console.error(`❌ 启动HTTP服务器失败 (端口: ${port}):`, err.message);
                            reject(err);
                        }
                    });
                };

                tryPort(startPort);

                // 保存服务器引用以便后续关闭
                global.expressServerInstance = server;

            } catch (error) {
                console.error('❌ 启动HTTP服务器失败:', error.message);
                reject(error);
            }
        } catch (error) {
            console.error('❌ 启动服务器失败:', error.message);
            reject(error);
        }
    });
}

/**
 * 创建应用程序菜单 - 包含Help菜单和LICENSE显示
 */
function createApplicationMenu() {
    console.log('🔧 创建应用程序菜单...');

    const template = [
        {
            label: 'Help',
            submenu: [
                {
                    label: '关于智慧校园系统',
                    click: () => showAboutDialog()
                },
                {
                    type: 'separator'
                },
                {
                    label: '软件许可证',
                    click: () => showLicenseDialog()
                }
            ]
        }
    ];

    // Windows平台需要添加默认菜单项
    if (process.platform === 'win32') {
        template.unshift({
            label: 'Menu',
            submenu: [
                {
                    label: 'Quit',
                    accelerator: 'Alt+F4',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        });
    }

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);

    console.log('✅ 应用程序菜单创建完成');
}

/**
 * 显示关于对话框
 */
function showAboutDialog() {
    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '关于智慧校园系统',
        message: '智慧校园系统 v2.0.0',
        detail: '基于Electron开发的智慧校园地图系统\n\n' +
                '主要功能：\n' +
                '• 交互式校园地图浏览\n' +
                '• 34个真实建筑物信息查询\n' +
                '• 智能路径规划系统\n' +
                '• 实时天气信息查询\n' +
                '• 地图图层控制\n' +
                '• 距离测量工具\n' +
                '• 多语言支持(中文/英文)\n\n' +
                '开发团队：Q OF GIS\n' +
                '版权所有 © 2025',
        buttons: ['确定'],
        defaultId: 0
    });
}

/**
 * 显示LICENSE许可证对话框
 */
function showLicenseDialog() {
    try {
        console.log('📄 显示软件许可证...');

        const licensePath = path.join(__dirname, 'LICENSE.txt');
        console.log(`许可证文件路径: ${licensePath}`);

        // 检查文件是否存在
        if (!fs.existsSync(licensePath)) {
            console.error('❌ LICENSE.txt文件不存在');
            dialog.showErrorBox('文件不存在', 'LICENSE.txt文件未找到，请检查应用程序完整性。');
            return;
        }

        // 读取LICENSE文件内容
        const licenseContent = fs.readFileSync(licensePath, 'utf8');
        console.log(`✅ 成功读取LICENSE文件，长度: ${licenseContent.length}字符`);

        // 显示LICENSE内容对话框
        dialog.showMessageBox(mainWindow, {
            type: 'info',
            title: '软件许可证',
            message: '系统许可',
            detail: licenseContent,
            buttons: ['确定'],
            defaultId: 0,
            // 设置对话框大小 - 调整为更紧凑的尺寸
            width: 200,
            height: 200
        });

        console.log('✅ LICENSE对话框已显示');

    } catch (error) {
        console.error('❌ 显示LICENSE失败:', error.message);
        dialog.showErrorBox('显示许可证失败',
            `无法显示软件许可证：${error.message}\n\n请检查应用程序完整性。`);
    }
}

/**
 * 创建主窗口 - 支持中文标题和内容
 */
function createWindow() {
    console.log('🖥️ 创建主窗口...');

    // 查找图标文件
    const iconPath = findIconFile();

    // 创建浏览器窗口
    const windowOptions = {
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        title: '智慧校园系统', // 中文标题
        ...(iconPath && { icon: iconPath }), // 动态设置图标
        show: false, // 初始隐藏，等待加载完成
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            webSecurity: true,
            // 允许加载本地文件（用于地图数据）
            allowRunningInsecureContent: false,
            experimentalFeatures: false
        },
        // Windows特定设置
        ...(process.platform === 'win32' && {
            titleBarStyle: 'default'
        }),
        // macOS特定设置
        ...(process.platform === 'darwin' && {
            titleBarStyle: 'hiddenInset'
        })
    };

    mainWindow = new BrowserWindow(windowOptions);
    
    // 加载主页面（通过Express服务器）- 使用动态端口
    const serverPort = global.serverPort || 3002;
    const indexUrl = `http://localhost:${serverPort}/index.html`;
    console.log(`加载主页面: ${indexUrl}`);

    mainWindow.loadURL(indexUrl).then(() => {
        console.log('✅ 主页面加载成功');
        mainWindow.show(); // 加载完成后显示窗口
    }).catch((error) => {
        console.error('❌ 主页面加载失败:', error);
        dialog.showErrorBox('页面加载失败', 
            `无法加载主页面：${error.message}\n\n请检查应用程序完整性。`);
    });
    
    // 窗口事件处理
    mainWindow.on('ready-to-show', () => {
        console.log('✅ 窗口准备就绪');
        mainWindow.show();
        
        // 开发环境下打开开发者工具
        if (process.env.NODE_ENV === 'development') {
            mainWindow.webContents.openDevTools();
        }
    });
    
    mainWindow.on('closed', () => {
        console.log('🔄 主窗口已关闭');
        mainWindow = null;
    });
    
    // 处理外部链接
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });
    
    // 阻止导航到外部页面
    mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        
        if (parsedUrl.origin !== 'file://') {
            event.preventDefault();
            shell.openExternal(navigationUrl);
        }
    });
}

/**
 * 清理资源
 */
async function cleanup() {
    console.log('🔄 开始清理资源...');
    isQuitting = true;
    
    // 关闭Express服务器
    if (global.expressServerInstance) {
        console.log('🔄 关闭内置Express服务器...');
        try {
            global.expressServerInstance.close(() => {
                console.log('✅ 内置Express服务器已关闭');
            });
        } catch (error) {
            console.error('❌ 关闭内置Express服务器失败:', error.message);
        }
    } else if (expressServer && !expressServer.killed) {
        console.log('🔄 关闭Express服务器进程...');

        try {
            // 优雅关闭
            expressServer.kill('SIGTERM');

            // 等待进程退出
            await new Promise((resolve) => {
                const timeout = setTimeout(() => {
                    console.log('⚠️ Express服务器关闭超时，强制终止');
                    expressServer.kill('SIGKILL');
                    resolve();
                }, 5000);

                expressServer.on('exit', () => {
                    clearTimeout(timeout);
                    console.log('✅ Express服务器已关闭');
                    resolve();
                });
            });
        } catch (error) {
            console.error('❌ 关闭Express服务器失败:', error.message);
        }
    }
    
    console.log('✅ 资源清理完成');
}

// 应用程序事件处理
app.whenReady().then(async () => {
    console.log('🚀 Electron应用程序启动');
    console.log(`操作系统: ${os.type()} ${os.release()}`);
    console.log(`Node.js版本: ${process.version}`);
    console.log(`Electron版本: ${process.versions.electron}`);
    
    try {
        // 1. 初始化数据库（允许失败，不阻止应用启动）
        console.log('📊 开始数据库初始化...');
        const dbPath = await initDatabase();
        if (dbPath) {
            console.log('✅ 数据库初始化成功');
        } else {
            console.log('⚠️ 数据库初始化失败，将使用静态数据模式');
        }
    } catch (dbError) {
        console.warn('⚠️ 数据库初始化异常，继续使用静态数据模式:', dbError.message);
    }

    try {

        // 2. 启动Express服务器
        await startExpressServer();

        // 3. 创建应用程序菜单
        createApplicationMenu();

        // 4. 创建主窗口
        createWindow();
        
        console.log('✅ 应用程序初始化完成');
        
    } catch (error) {
        console.error('❌ 应用程序初始化失败:', error.message);
        
        dialog.showErrorBox('应用程序启动失败', 
            `应用程序无法正常启动：${error.message}\n\n请重新安装应用程序。`);
        
        app.quit();
    }
});

// 所有窗口关闭时的处理
app.on('window-all-closed', async () => {
    console.log('🔄 所有窗口已关闭');
    
    // 在macOS上，除非用户明确退出，否则应用程序保持活动状态
    if (process.platform !== 'darwin') {
        await cleanup();
        app.quit();
    }
});

// 应用程序激活时的处理（macOS）
app.on('activate', () => {
    console.log('🔄 应用程序被激活');
    
    // 在macOS上，当点击dock图标且没有其他窗口打开时，重新创建窗口
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// 应用程序退出前的处理
app.on('before-quit', async (event) => {
    console.log('🔄 应用程序准备退出');
    
    if (!isQuitting) {
        event.preventDefault();
        await cleanup();
        app.quit();
    }
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    console.error('❌ 未捕获的异常:', error);
    
    dialog.showErrorBox('应用程序错误', 
        `发生未预期的错误：${error.message}\n\n应用程序将退出。`);
    
    app.quit();
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
    console.error('Promise:', promise);
});

console.log('📱 Electron主进程已加载');
