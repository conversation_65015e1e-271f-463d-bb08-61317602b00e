/**
 * 校园建筑物数据
 * 从SQLite数据库导出的建筑物信息
 * 格式：JavaScript数组，可转换为GeoJSON
 */

// 建筑物数据（从SQLite导出）
window.buildingsData = [
  {
    "id": 1,
    "name": "教学楼1号",
    "type": "教学建筑",
    "description": "第一教学楼，主要用于基础课程教学",
    "longitude": 120.9078,
    "latitude": 31.9752,
    "floor_count": 5,
    "area": 2278,
    "build_year": 2008,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 2,
    "name": "教学楼2号",
    "type": "教学建筑",
    "description": "第二教学楼，主要用于专业课程教学",
    "longitude": 120.9082,
    "latitude": 31.975,
    "floor_count": 6,
    "area": 2966,
    "build_year": 2009,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 3,
    "name": "教学楼3号",
    "type": "教学建筑",
    "description": "第三教学楼，多媒体教学中心",
    "longitude": 120.9075,
    "latitude": 31.9748,
    "floor_count": 7,
    "area": 3586,
    "build_year": 2010,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 4,
    "name": "教学楼4号、公共教学楼",
    "type": "教学建筑",
    "description": "第四教学楼，公共教学楼",
    "longitude": 120.908,
    "latitude": 31.9755,
    "floor_count": 6,
    "area": 2902,
    "build_year": 2011,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 5,
    "name": "教学楼5号",
    "type": "教学建筑",
    "description": "第五教学楼，大型阶梯教室",
    "longitude": 120.9085,
    "latitude": 31.9753,
    "floor_count": 8,
    "area": 5960,
    "build_year": 2012,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 6,
    "name": "教学楼6号",
    "type": "教学建筑",
    "description": "第六教学楼，实验教学楼",
    "longitude": 120.9088,
    "latitude": 31.9751,
    "floor_count": 5,
    "area": 2290,
    "build_year": 2013,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 7,
    "name": "教学楼7号",
    "type": "教学建筑",
    "description": "第七教学楼，语言教学中心",
    "longitude": 120.9072,
    "latitude": 31.9754,
    "floor_count": 6,
    "area": 2698,
    "build_year": 2014,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 8,
    "name": "教学楼8号",
    "type": "教学建筑",
    "description": "第八教学楼，计算机教学中心",
    "longitude": 120.909,
    "latitude": 31.9749,
    "floor_count": 7,
    "area": 3405,
    "build_year": 2015,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 9,
    "name": "教学楼9号",
    "type": "教学建筑",
    "description": "第九教学楼，艺术教学楼",
    "longitude": 120.9076,
    "latitude": 31.9756,
    "floor_count": 8,
    "area": 3955,
    "build_year": 2016,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 10,
    "name": "科技实验楼",
    "type": "教学建筑",
    "description": "科技实验楼，科研实验中心",
    "longitude": 120.9092,
    "latitude": 31.9747,
    "floor_count": 6,
    "area": 2528,
    "build_year": 2017,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 11,
    "name": "图书馆",
    "type": "公共建筑",
    "description": "南通大学主图书馆，学习研究中心",
    "longitude": 120.9085,
    "latitude": 31.9745,
    "floor_count": 12,
    "area": 14844,
    "build_year": 2010,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 12,
    "name": "范曾艺术馆",
    "type": "公共建筑",
    "description": "范曾艺术馆，艺术展览中心",
    "longitude": 120.907,
    "latitude": 31.974,
    "floor_count": 4,
    "area": 3211,
    "build_year": 2018,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 13,
    "name": "地理科学学院、艺术学院",
    "type": "公共建筑",
    "description": "地理科学学院、艺术学院综合楼",
    "longitude": 120.9095,
    "latitude": 31.9742,
    "floor_count": 6,
    "area": 8709,
    "build_year": 2019,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 14,
    "name": "学生公寓1号楼",
    "type": "宿舍建筑",
    "description": "学生公寓第一栋，本科生宿舍",
    "longitude": 120.91,
    "latitude": 31.976,
    "floor_count": 6,
    "area": 3216,
    "build_year": 2015,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 15,
    "name": "学生公寓2号楼",
    "type": "宿舍建筑",
    "description": "学生公寓第二栋，本科生宿舍",
    "longitude": 120.9102,
    "latitude": 31.9762,
    "floor_count": 6,
    "area": 3215,
    "build_year": 2015,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 16,
    "name": "学生公寓3号楼",
    "type": "宿舍建筑",
    "description": "学生公寓第三栋，本科生宿舍",
    "longitude": 120.9104,
    "latitude": 31.9764,
    "floor_count": 6,
    "area": 3215,
    "build_year": 2015,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 17,
    "name": "学生公寓32号楼",
    "type": "宿舍建筑",
    "description": "学生公寓32号楼，研究生宿舍",
    "longitude": 120.9098,
    "latitude": 31.9758,
    "floor_count": 6,
    "area": 2511,
    "build_year": 2020,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 18,
    "name": "学生公寓33号楼",
    "type": "宿舍建筑",
    "description": "学生公寓33号楼，研究生宿舍",
    "longitude": 120.9096,
    "latitude": 31.9756,
    "floor_count": 6,
    "area": 2467,
    "build_year": 2020,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 19,
    "name": "研究生公寓、留学生公寓",
    "type": "宿舍建筑",
    "description": "研究生公寓、留学生公寓",
    "longitude": 120.911,
    "latitude": 31.9765,
    "floor_count": 8,
    "area": 3174,
    "build_year": 2021,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 20,
    "name": "青年教师公寓",
    "type": "宿舍建筑",
    "description": "青年教师公寓，教师住宿",
    "longitude": 120.9108,
    "latitude": 31.9768,
    "floor_count": 6,
    "area": 2506,
    "build_year": 2019,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 21,
    "name": "一食堂",
    "type": "食堂",
    "description": "第一学生食堂，主要用餐场所",
    "longitude": 120.9092,
    "latitude": 31.9748,
    "floor_count": 3,
    "area": 4852,
    "build_year": 2009,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 22,
    "name": "二食堂",
    "type": "食堂",
    "description": "第二学生食堂，师生用餐场所",
    "longitude": 120.9088,
    "latitude": 31.9746,
    "floor_count": 3,
    "area": 7320,
    "build_year": 2010,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 23,
    "name": "三食堂",
    "type": "食堂",
    "description": "第三学生食堂，特色餐饮",
    "longitude": 120.9094,
    "latitude": 31.975,
    "floor_count": 3,
    "area": 7223,
    "build_year": 2011,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 24,
    "name": "体育馆",
    "type": "体育建筑",
    "description": "综合性体育场馆，室内运动中心",
    "longitude": 120.9065,
    "latitude": 31.9735,
    "floor_count": 3,
    "area": 13922,
    "build_year": 2012,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 25,
    "name": "体育中心、操场看台",
    "type": "体育建筑",
    "description": "体育中心，操场看台",
    "longitude": 120.906,
    "latitude": 31.973,
    "floor_count": 2,
    "area": 3038,
    "build_year": 2013,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 26,
    "name": "机械工程学院",
    "type": "教学建筑",
    "description": "机械工程学院教学楼",
    "longitude": 120.9068,
    "latitude": 31.9758,
    "floor_count": 6,
    "area": 2932,
    "build_year": 2016,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 27,
    "name": "电气工程学院",
    "type": "教学建筑",
    "description": "电气工程学院教学楼",
    "longitude": 120.9072,
    "latitude": 31.976,
    "floor_count": 8,
    "area": 4365,
    "build_year": 2017,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 28,
    "name": "交通与土木工程学院",
    "type": "教学建筑",
    "description": "交通与土木工程学院",
    "longitude": 120.9074,
    "latitude": 31.9762,
    "floor_count": 5,
    "area": 2070,
    "build_year": 2018,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 29,
    "name": "纺化楼",
    "type": "教学建筑",
    "description": "纺织化学工程学院",
    "longitude": 120.9076,
    "latitude": 31.9764,
    "floor_count": 8,
    "area": 4671,
    "build_year": 2014,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 30,
    "name": "校医院",
    "type": "服务建筑",
    "description": "校医院，医疗服务中心",
    "longitude": 120.9086,
    "latitude": 31.9741,
    "floor_count": 2,
    "area": 661,
    "build_year": 2008,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 31,
    "name": "工程训练中心",
    "type": "服务建筑",
    "description": "工程训练中心，实践教学基地",
    "longitude": 120.9058,
    "latitude": 31.9738,
    "floor_count": 4,
    "area": 8249,
    "build_year": 2015,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 32,
    "name": "校园服务中心",
    "type": "行政建筑",
    "description": "校园服务中心，综合服务大厅",
    "longitude": 120.9083,
    "latitude": 31.9743,
    "floor_count": 3,
    "area": 1404,
    "build_year": 2019,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 33,
    "name": "东操场",
    "type": "体育建筑",
    "description": "东操场，田径运动场",
    "longitude": 120.9105,
    "latitude": 31.975,
    "floor_count": 1,
    "area": 20727,
    "build_year": 2008,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 34,
    "name": "西操场",
    "type": "体育建筑",
    "description": "西操场，足球场",
    "longitude": 120.9055,
    "latitude": 31.9732,
    "floor_count": 1,
    "area": 21488,
    "build_year": 2008,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  }
];

// 将数据转换为GeoJSON格式的函数
window.convertToGeoJSON = function(buildingsArray) {
    return {
        "type": "FeatureCollection",
        "features": buildingsArray.map(building => ({
            "type": "Feature",
            "geometry": {
                "type": "Point",
                "coordinates": [building.longitude, building.latitude]
            },
            "properties": {
                "id": building.id,
                "name": building.name,
                "type": building.type,
                "description": building.description,
                "floor_count": building.floor_count,
                "area": building.area,
                "build_year": building.build_year,
                "status": building.status,
                "created_at": building.created_at
            }
        }))
    };
};

// 获取建筑物GeoJSON数据
window.getBuildingsGeoJSON = function() {
    return window.convertToGeoJSON(window.buildingsData);
};

// 根据类型筛选建筑物
window.getBuildingsByType = function(type) {
    return window.buildingsData.filter(building => building.type === type);
};

// 根据ID查找建筑物
window.getBuildingById = function(id) {
    return window.buildingsData.find(building => building.id === id);
};

// 搜索建筑物（按名称）
window.searchBuildings = function(keyword) {
    if (!keyword) return window.buildingsData;
    const lowerKeyword = keyword.toLowerCase();
    return window.buildingsData.filter(building => 
        building.name.toLowerCase().includes(lowerKeyword) ||
        building.description.toLowerCase().includes(lowerKeyword)
    );
};

// 获取建筑物类型统计
window.getBuildingTypeStats = function() {
    const stats = {};
    window.buildingsData.forEach(building => {
        stats[building.type] = (stats[building.type] || 0) + 1;
    });
    return stats;
};

console.log('✅ 建筑物数据已加载:', window.buildingsData.length, '个建筑物');
console.log('📊 建筑物类型统计:', window.getBuildingTypeStats());