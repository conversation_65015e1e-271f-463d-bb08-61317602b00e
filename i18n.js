// ============================================================================
// 国际化配置文件 - i18n.js
// ============================================================================

const I18N = {
    // 当前语言
    currentLanguage: 'zh',
    
    // 语言配置
    languages: {
        zh: {
            name: '中文',
            code: 'zh'
        },
        en: {
            name: 'English',
            code: 'en'
        }
    },

    // 翻译文本配置
    translations: {
        zh: {
            // 语言切换
            language: {
                current: '中文',
                switch: '切换语言'
            },
            
            // 页面标题和基本信息
            page: {
                title: '智慧校园地图系统',
                subtitle: '南通大学校园导航系统',
                loading: '正在加载地图数据...',
                loginPrompt: '请登录以访问地图功能'
            },
            
            // 登录相关
            login: {
                username: '用户名',
                password: '密码',
                loginBtn: '登录',
                loginSuccess: '登录成功',
                loginFailed: '登录失败'
            },
            
            // 功能按钮
            buttons: {
                routePlanning: '🌈 路径规划',
                layerControl: '✨ 图层控制',
                weather: '☀️ 天气查询',
                systemInfo: '🌟 系统信息',
                distanceMeasure: '📐 测距工具'
            },

            // 测距工具
            distance: {
                title: '测距工具',
                panelTitle: '📏 测距工具',
                resultTitle: '📏 测距结果',
                clickMapStart: '点击地图设置起点',
                clickMapEnd: '点击地图设置终点',
                measureComplete: '测量完成，点击地图重新测量',
                startPoint: '起点',
                endPoint: '终点',
                distance: '距离',
                straightLine: '直线距离',
                startCoord: '起点坐标',
                endCoord: '终点坐标',
                measureTime: '测量时间',
                clearMeasure: '清除测量',
                exportResult: '导出结果',
                newMeasure: '重新测距',
                exportSuccess: '测距结果已导出',
                exportFailed: '导出失败',
                noData: '没有可导出的测量数据',
                activated: '测距模式已启动，请在地图上点击两个点进行测距',
                helpText1: '📍 点击地图上两个点进行测距',
                helpText2: '📏 支持直线距离测量',
                meters: '米',
                kilometers: '公里',
                reportTitle: '智慧校园测距结果',
                measureType: '测量类型',
                system: '智慧校园地图系统'
            },
            
            // 搜索
            search: {
                placeholder: '🔍 搜索可爱的建筑物...',
                noResults: '未找到相关建筑物',
                searching: '搜索中...',
                navigateHere: '导航至此'
            },
            
            // 路径规划
            route: {
                title: '路径规划',
                startPoint: '起点',
                endPoint: '终点',
                startPlaceholder: '点击地图选择起点或输入建筑物名称',
                endPlaceholder: '点击地图选择终点或输入建筑物名称',
                routeType: '路径类型：',
                transportMode: '交通方式：',
                shortest: '最短路径',
                walking: '步行',
                cycling: '骑行',
                calculate: '计算路径',
                clear: '清除路径',
                calculating: '正在计算路径...',
                success: '路径计算成功',
                failed: '路径计算失败',
                distance: '总距离',
                time: '预计时间',
                export: '导出路径',
                share: '分享路径'
            },
            
            // 路径报告
            report: {
                title: '路径规划报告',
                confirmTitle: '🗺️ 路径规划完成',
                confirmMessage: '路径计算已完成，是否生成详细的路径报告？',
                generate: '生成报告',
                cancel: '取消',
                save: '保存为TXT文件',
                startLocation: '起点位置：',
                endLocation: '终点位置：',
                segments: '路径段数：',
                success: '路径计算成功'
            },

            // 路径相关
            route: {
                title: '路径规划',
                startPoint: '起点',
                endPoint: '终点',
                startPlaceholder: '点击地图选择起点或输入建筑物名称',
                endPlaceholder: '点击地图选择终点或输入建筑物名称',
                routeType: '路径类型：',
                transportMode: '交通方式：',
                shortest: '最短路径',
                walking: '步行',
                cycling: '骑行',
                calculate: '计算路径',
                clear: '清除路径',
                calculating: '正在计算路径...',
                success: '路径计算成功',
                failed: '路径计算失败',
                distance: '总距离',
                time: '预计时间',
                export: '导出路径',
                share: '分享路径',
                notice: '路径提示',
                endPointSet: '终点已设置',
                hasBeenSetAsEndPoint: '已设置为终点',
                openRoutePlanning: '打开路径规划',
                setStartPoint: '设置起点',
                clickCalculate: '点击计算路径'
            },

            // 测距功能
            measure: {
                title: '测距工具',
                startPoint: '起点',
                endPoint: '终点',
                distance: '距离',
                clear: '清除测量',
                export: '导出结果',
                clickToSetStart: '点击地图设置起点',
                clickToSetEnd: '点击地图设置终点',
                measureComplete: '测量完成，点击地图重新测量',
                noData: '没有可导出的测量数据',
                exportSuccess: '测距结果已导出',
                exportFailed: '导出失败',
                straightLine: '直线距离',
                meters: '米',
                kilometers: '公里',
                help1: '点击地图上两个点进行测距',
                help2: '支持直线距离测量'
            },

            // 图层控制
            layers: {
                title: '图层控制',
                mapLayers: '地图图层',
                buildings: '建筑物分类',
                roads: '道路',
                waters: '水域',
                rivers: '河流',
                boundary: '校园边界',
                buildingControl: '建筑物总控制',
                teaching: '🏫 教学建筑',
                public: '🏛️ 公共建筑',
                dormitory: '🏠 宿舍建筑',
                sports: '🏟️ 体育建筑',
                admin: '🏢 行政建筑',
                dining: '🍽️ 食堂',
                service: '🏪 服务建筑',
                college: '🎓 学院建筑',
                parking: '🅿️ 公共停车场',
                unknown: '❓ 未知建筑'
            },
            
            // 天气
            weather: {
                title: '🌤️ 南通天气',
                loading: '正在获取天气信息...',
                error: '天气信息获取失败',
                retry: '重试',
                refresh: '刷新天气数据',
                humidity: '湿度',
                pressure: '气压',
                windSpeed: '风速',
                uvIndex: '紫外线',
                updateTime: '更新时间',
                justNow: '刚刚',
                nextUpdate: '下次更新：',
                minutesLater: '分钟后',
                autoUpdate: '自动更新',
                autoUpdateEnabled: '自动更新已启用',
                autoUpdateDisabled: '自动更新已禁用',
                tempChart: '今日温度变化'
            },
            
            // 建筑物信息
            building: {
                title: '建筑物信息',
                name: '名称：',
                area: '面积：',
                type: '类型：',
                setAsStart: '设为起点',
                setAsEnd: '设为终点',
                defaultType: '建筑物'
            },
            
            // 通用
            common: {
                close: '×',
                confirm: '确认',
                cancel: '取消',
                save: '保存',
                delete: '删除',
                edit: '编辑',
                loading: '加载中...',
                success: '成功',
                error: '错误',
                warning: '警告',
                info: '信息',
                unknown: '未知',
                meters: '米',
                minutes: '分钟',
                seconds: '秒',
                about: '约',
                segment: '段'
            },
            
            // 设置
            settings: {
                title: '⚙️ 系统设置',
                language: {
                    title: '🌐 语言设置',
                    label: '界面语言：',
                    chinese: '中文',
                    english: 'English'
                }
            },

            // 消息提示
            messages: {
                routeCalculated: '路径计算完成',
                routeCleared: '路径已清除',
                startPointSet: '起点已设置',
                endPointSet: '终点已设置',
                buildingNotFound: '未找到建筑物',
                routeExported: '路径已导出',
                routeShared: '路径已分享',
                reportSaved: '路径报告已保存为txt文件',
                languageChanged: '语言已切换',
                routePlanningReminder: '终点已设置！请打开路径规划面板，设置起点后点击"计算路径"按钮来生成路径报告。'
            },

            // 测试页面
            test: {
                title: '测距功能国际化测试',
                txtReport: 'TXT报告生成测试',
                generate: '生成测试报告',
                translation: '翻译文本测试'
            }
        },
        
        en: {
            // 语言切换
            language: {
                current: 'English',
                switch: 'Switch Language'
            },
            
            // 页面标题和基本信息
            page: {
                title: 'Smart Campus Map System',
                subtitle: 'Nantong University Campus Navigation System',
                loading: 'Loading map data...',
                loginPrompt: 'Please login to access map features'
            },
            
            // 登录相关
            login: {
                username: 'Username',
                password: 'Password',
                loginBtn: 'Login',
                loginSuccess: 'Login successful',
                loginFailed: 'Login failed'
            },
            
            // 功能按钮
            buttons: {
                routePlanning: '🌈 Route Planning',
                layerControl: '✨ Layer Control',
                weather: '☀️ Weather',
                systemInfo: '🌟 System Info',
                distanceMeasure: '📐 Distance Tool'
            },

            // 测距工具
            distance: {
                title: 'Distance Tool',
                panelTitle: '📏 Distance Measurement',
                resultTitle: '📏 Measurement Result',
                clickMapStart: 'Click map to set start point',
                clickMapEnd: 'Click map to set end point',
                measureComplete: 'Measurement complete, click map to measure again',
                startPoint: 'Start Point',
                endPoint: 'End Point',
                distance: 'Distance',
                straightLine: 'Straight Line Distance',
                startCoord: 'Start Coordinates',
                endCoord: 'End Coordinates',
                measureTime: 'Measurement Time',
                clearMeasure: 'Clear Measurement',
                exportResult: 'Export Result',
                newMeasure: 'New Measurement',
                exportSuccess: 'Distance result exported successfully',
                exportFailed: 'Export failed',
                noData: 'No measurement data to export',
                activated: 'Distance mode activated, click two points on the map to measure',
                helpText1: '📍 Click two points on the map to measure distance',
                helpText2: '📏 Supports straight line distance measurement',
                meters: 'meters',
                kilometers: 'kilometers',
                reportTitle: 'Smart Campus Distance Measurement Report',
                measureType: 'Measurement Type',
                system: 'Smart Campus Map System'
            },
            
            // 搜索
            search: {
                placeholder: '🔍 Search for buildings...',
                noResults: 'No buildings found',
                searching: 'Searching...',
                navigateHere: 'Navigate Here'
            },
            
            // 路径规划
            route: {
                title: 'Route Planning',
                startPoint: 'Start Point',
                endPoint: 'End Point',
                startPlaceholder: 'Click map to select start point or enter building name',
                endPlaceholder: 'Click map to select end point or enter building name',
                routeType: 'Route Type:',
                transportMode: 'Transport Mode:',
                shortest: 'Shortest Path',
                walking: 'Walking',
                cycling: 'Cycling',
                calculate: 'Calculate Route',
                clear: 'Clear Route',
                calculating: 'Calculating route...',
                success: 'Route calculated successfully',
                failed: 'Route calculation failed',
                distance: 'Total Distance',
                time: 'Estimated Time',
                export: 'Export Route',
                share: 'Share Route'
            },
            
            // 路径报告
            report: {
                title: 'Route Planning Report',
                confirmTitle: '🗺️ Route Planning Complete',
                confirmMessage: 'Route calculation completed. Generate detailed route report?',
                generate: 'Generate Report',
                cancel: 'Cancel',
                save: 'Save as TXT File',
                startLocation: 'Start Location:',
                endLocation: 'End Location:',
                segments: 'Route Segments:',
                success: 'Route calculated successfully'
            },

            // 路径相关
            route: {
                title: 'Route Planning',
                startPoint: 'Start Point',
                endPoint: 'End Point',
                startPlaceholder: 'Click map to select start point or enter building name',
                endPlaceholder: 'Click map to select end point or enter building name',
                routeType: 'Route Type:',
                transportMode: 'Transport Mode:',
                shortest: 'Shortest Path',
                walking: 'Walking',
                cycling: 'Cycling',
                calculate: 'Calculate Route',
                clear: 'Clear Route',
                calculating: 'Calculating route...',
                success: 'Route calculated successfully',
                failed: 'Route calculation failed',
                distance: 'Total Distance',
                time: 'Estimated Time',
                export: 'Export Route',
                share: 'Share Route',
                notice: 'Route Notice',
                endPointSet: 'End Point Set',
                hasBeenSetAsEndPoint: 'has been set as end point',
                openRoutePlanning: 'Open Route Planning',
                setStartPoint: 'Set start point',
                clickCalculate: 'Click Calculate Route'
            },

            // 测距功能
            measure: {
                title: 'Distance Measurement',
                startPoint: 'Start Point',
                endPoint: 'End Point',
                distance: 'Distance',
                clear: 'Clear Measurement',
                export: 'Export Result',
                clickToSetStart: 'Click map to set start point',
                clickToSetEnd: 'Click map to set end point',
                measureComplete: 'Measurement complete, click map to remeasure',
                noData: 'No measurement data to export',
                exportSuccess: 'Distance result exported successfully',
                exportFailed: 'Export failed',
                straightLine: 'Straight Line Distance',
                meters: 'meters',
                kilometers: 'kilometers',
                help1: 'Click two points on the map to measure distance',
                help2: 'Supports straight line distance measurement'
            },

            // 图层控制
            layers: {
                title: 'Layer Control',
                mapLayers: 'Map Layers',
                buildings: 'Building Categories',
                roads: 'Roads',
                waters: 'Waters',
                rivers: 'Rivers',
                boundary: 'Campus Boundary',
                buildingControl: 'Building Master Control',
                teaching: '🏫 Teaching Buildings',
                public: '🏛️ Public Buildings',
                dormitory: '🏠 Dormitory Buildings',
                sports: '🏟️ Sports Buildings',
                admin: '🏢 Administrative Buildings',
                dining: '🍽️ Dining Halls',
                service: '🏪 Service Buildings',
                college: '🎓 College Buildings',
                parking: '🅿️ Public Parking',
                unknown: '❓ Unknown Buildings'
            },
            
            // 天气
            weather: {
                title: '🌤️ Nantong Weather',
                loading: 'Getting weather information...',
                error: 'Failed to get weather information',
                retry: 'Retry',
                refresh: 'Refresh weather data',
                humidity: 'Humidity',
                pressure: 'Pressure',
                windSpeed: 'Wind Speed',
                uvIndex: 'UV Index',
                updateTime: 'Update Time',
                justNow: 'Just now',
                nextUpdate: 'Next update:',
                minutesLater: 'minutes later',
                autoUpdate: 'Auto Update',
                autoUpdateEnabled: 'Auto update enabled',
                autoUpdateDisabled: 'Auto update disabled',
                tempChart: 'Today\'s Temperature'
            },
            
            // 建筑物信息
            building: {
                title: 'Building Information',
                name: 'Name:',
                area: 'Area:',
                type: 'Type:',
                setAsStart: 'Set as Start',
                setAsEnd: 'Set as End',
                defaultType: 'Building'
            },
            
            // 通用
            common: {
                close: '×',
                confirm: 'Confirm',
                cancel: 'Cancel',
                save: 'Save',
                delete: 'Delete',
                edit: 'Edit',
                loading: 'Loading...',
                success: 'Success',
                error: 'Error',
                warning: 'Warning',
                info: 'Info',
                unknown: 'Unknown',
                meters: 'm',
                minutes: 'min',
                seconds: 's',
                about: 'About',
                segment: 'segment'
            },
            
            // 设置
            settings: {
                title: '⚙️ System Settings',
                language: {
                    title: '🌐 Language Settings',
                    label: 'Interface Language:',
                    chinese: '中文',
                    english: 'English'
                }
            },

            // 消息提示
            messages: {
                routeCalculated: 'Route calculated',
                routeCleared: 'Route cleared',
                startPointSet: 'Start point set',
                endPointSet: 'End point set',
                buildingNotFound: 'Building not found',
                routeExported: 'Route exported',
                routeShared: 'Route shared',
                reportSaved: 'Route report saved as txt file',
                languageChanged: 'Language switched',
                routePlanningReminder: 'End point set! Please open the Route Planning panel, set the start point, and click "Calculate Route" to generate a route report.'
            },

            // 测试页面
            test: {
                title: 'Distance Function Internationalization Test',
                txtReport: 'TXT Report Generation Test',
                generate: 'Generate Test Report',
                translation: 'Translation Text Test'
            }
        }
    },

    // 获取翻译文本
    t(key) {
        const keys = key.split('.');
        let value = this.translations[this.currentLanguage];
        
        for (const k of keys) {
            if (value && typeof value === 'object') {
                value = value[k];
            } else {
                return key; // 如果找不到翻译，返回原key
            }
        }
        
        return value || key;
    },

    // 切换语言
    switchLanguage() {
        this.currentLanguage = this.currentLanguage === 'zh' ? 'en' : 'zh';
        this.saveLanguagePreference();
        this.updatePageTexts();
        return this.currentLanguage;
    },

    // 设置语言
    setLanguage(lang) {
        if (this.languages[lang]) {
            this.currentLanguage = lang;
            this.saveLanguagePreference();
            this.updatePageTexts();
        }
    },

    // 保存语言偏好到localStorage
    saveLanguagePreference() {
        try {
            localStorage.setItem('campus-map-language', this.currentLanguage);
        } catch (error) {
            console.warn('无法保存语言偏好:', error);
        }
    },

    // 加载语言偏好
    loadLanguagePreference() {
        try {
            const saved = localStorage.getItem('campus-map-language');
            if (saved && this.languages[saved]) {
                this.currentLanguage = saved;
            }
        } catch (error) {
            console.warn('无法加载语言偏好:', error);
        }
    },

    // 更新页面所有文本
    updatePageTexts() {
        console.log('更新页面文本为:', this.currentLanguage);
        
        // 更新所有带有data-i18n属性的元素
        const elements = document.querySelectorAll('[data-i18n]');
        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            const text = this.t(key);
            
            if (element.tagName === 'INPUT' && (element.type === 'text' || element.type === 'password')) {
                element.placeholder = text;
            } else {
                element.textContent = text;
            }
        });

        // 更新页面标题
        document.title = this.t('page.title');
        
        // 触发自定义事件，通知其他模块语言已更改
        document.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: this.currentLanguage }
        }));
    },

    // 初始化国际化系统
    init() {
        console.log('初始化国际化系统');

        // 加载保存的语言偏好
        this.loadLanguagePreference();

        // 绑定设置按钮事件
        this.bindSettingsEvents();

        // 初始化页面文本
        this.updatePageTexts();

        console.log('国际化系统初始化完成，当前语言:', this.currentLanguage);
    },

    // 绑定设置相关事件
    bindSettingsEvents() {
        // 设置按钮点击事件
        const settingsBtn = document.getElementById('settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.showSettingsModal();
            });
        }

        // 设置模态框关闭事件
        const settingsClose = document.getElementById('settings-close');
        if (settingsClose) {
            settingsClose.addEventListener('click', () => {
                this.hideSettingsModal();
            });
        }

        // 语言选择按钮事件
        const langButtons = document.querySelectorAll('.language-option');
        langButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const lang = btn.getAttribute('data-lang');
                this.setLanguage(lang);
                this.updateLanguageButtons();

                // 显示切换成功消息
                if (window.showMessage) {
                    window.showMessage(this.t('messages.languageChanged'), 'success');
                }
            });
        });

        // 模态框背景点击关闭
        const settingsModal = document.getElementById('settings-modal');
        if (settingsModal) {
            settingsModal.addEventListener('click', (e) => {
                if (e.target === settingsModal || e.target.classList.contains('modal-overlay')) {
                    this.hideSettingsModal();
                }
            });
        }
    },

    // 显示设置模态框
    showSettingsModal() {
        const modal = document.getElementById('settings-modal');
        if (modal) {
            modal.style.display = 'flex';
            this.updateLanguageButtons();
        }
    },

    // 隐藏设置模态框
    hideSettingsModal() {
        const modal = document.getElementById('settings-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    },

    // 更新语言按钮状态
    updateLanguageButtons() {
        const langButtons = document.querySelectorAll('.language-option');
        langButtons.forEach(btn => {
            const lang = btn.getAttribute('data-lang');
            if (lang === this.currentLanguage) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });
    }
};

// 页面加载完成后初始化国际化系统
document.addEventListener('DOMContentLoaded', () => {
    I18N.init();
});

// 导出到全局作用域
window.I18N = I18N;
