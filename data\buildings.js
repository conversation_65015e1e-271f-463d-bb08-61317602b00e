/**
 * 校园建筑物数据 - 双语版本
 * 从SQLite数据库导出的建筑物信息，支持中英文双语
 * 格式：JavaScript数组，可转换为GeoJSON
 */

// 建筑物数据（双语版本）
window.buildingsData = [
  {
    "id": 1,
    "name": "教学楼1号",
    "type": "教学建筑",
    "description": "第一教学楼，主要用于基础课程教学",
    "name_en": "Teaching Building No.1",
    "type_en": "Teaching Building",
    "description_en": "First Teaching Building, mainly used for basic course instruction",
    "longitude": 120.9078,
    "latitude": 31.9752,
    "floor_count": 5,
    "area": 2278,
    "build_year": 2008,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 2,
    "name": "教学楼2号",
    "type": "教学建筑",
    "description": "第二教学楼，主要用于专业课程教学",
    "name_en": "Teaching Building No.2",
    "type_en": "Teaching Building",
    "description_en": "Second Teaching Building, mainly used for specialized course instruction",
    "longitude": 120.9082,
    "latitude": 31.975,
    "floor_count": 6,
    "area": 2966,
    "build_year": 2009,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },  {
    "id": 3,
    "name": "教学楼3号",
    "type": "教学建筑",
    "description": "第三教学楼，多媒体教学中心",
    "name_en": "Teaching Building No.3",
    "type_en": "Teaching Building",
    "description_en": "Third Teaching Building, multimedia teaching center",
    "longitude": 120.9075,
    "latitude": 31.9748,
    "floor_count": 7,
    "area": 3586,
    "build_year": 2010,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 4,
    "name": "教学楼4号、公共教学楼",
    "type": "教学建筑",
    "description": "第四教学楼，公共教学楼",
    "name_en": "Teaching Building No.4, Public Teaching Building",
    "type_en": "Teaching Building",
    "description_en": "Fourth Teaching Building, public teaching building",
    "longitude": 120.908,
    "latitude": 31.9755,
    "floor_count": 6,
    "area": 2902,
    "build_year": 2011,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 5,
    "name": "教学楼5号",
    "type": "教学建筑",
    "description": "第五教学楼，大型阶梯教室",
    "name_en": "Teaching Building No.5",
    "type_en": "Teaching Building",
    "description_en": "Fifth Teaching Building, large lecture halls",
    "longitude": 120.9085,
    "latitude": 31.9753,
    "floor_count": 8,
    "area": 5960,
    "build_year": 2012,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },  {
    "id": 6,
    "name": "教学楼6号",
    "type": "教学建筑",
    "description": "第六教学楼，实验教学楼",
    "name_en": "Teaching Building No.6",
    "type_en": "Teaching Building",
    "description_en": "Sixth Teaching Building, experimental teaching building",
    "longitude": 120.9088,
    "latitude": 31.9751,
    "floor_count": 5,
    "area": 2290,
    "build_year": 2013,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 7,
    "name": "教学楼7号",
    "type": "教学建筑",
    "description": "第七教学楼，语言教学中心",
    "name_en": "Teaching Building No.7",
    "type_en": "Teaching Building",
    "description_en": "Seventh Teaching Building, language teaching center",
    "longitude": 120.9072,
    "latitude": 31.9754,
    "floor_count": 6,
    "area": 2698,
    "build_year": 2014,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 8,
    "name": "教学楼8号",
    "type": "教学建筑",
    "description": "第八教学楼，计算机教学中心",
    "name_en": "Teaching Building No.8",
    "type_en": "Teaching Building",
    "description_en": "Eighth Teaching Building, computer teaching center",
    "longitude": 120.909,
    "latitude": 31.9749,
    "floor_count": 7,
    "area": 3405,
    "build_year": 2015,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 9,
    "name": "教学楼9号",
    "type": "教学建筑",
    "description": "第九教学楼，艺术教学楼",
    "name_en": "Teaching Building No.9",
    "type_en": "Teaching Building",
    "description_en": "Ninth Teaching Building, art teaching building",
    "longitude": 120.9076,
    "latitude": 31.9756,
    "floor_count": 8,
    "area": 3955,
    "build_year": 2016,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 10,
    "name": "科技实验楼",
    "type": "教学建筑",
    "description": "科技实验楼，科研实验中心",
    "name_en": "Science & Technology Laboratory Building",
    "type_en": "Teaching Building",
    "description_en": "Science & Technology Laboratory Building, research and experimental center",
    "longitude": 120.9092,
    "latitude": 31.9747,
    "floor_count": 6,
    "area": 2528,
    "build_year": 2017,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },  {
    "id": 11,
    "name": "图书馆",
    "type": "公共建筑",
    "description": "南通大学主图书馆，学习研究中心",
    "name_en": "Library",
    "type_en": "Public Building",
    "description_en": "Nantong University Main Library, learning and research center",
    "longitude": 120.9085,
    "latitude": 31.9745,
    "floor_count": 12,
    "area": 14844,
    "build_year": 2010,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 12,
    "name": "范曾艺术馆",
    "type": "公共建筑",
    "description": "范曾艺术馆，艺术展览中心",
    "name_en": "Fan Zeng Art Museum",
    "type_en": "Public Building",
    "description_en": "Fan Zeng Art Museum, art exhibition center",
    "longitude": 120.907,
    "latitude": 31.974,
    "floor_count": 4,
    "area": 3211,
    "build_year": 2018,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 13,
    "name": "地理科学学院、艺术学院",
    "type": "公共建筑",
    "description": "地理科学学院、艺术学院综合楼",
    "name_en": "School of Geography & Art",
    "type_en": "Public Building",
    "description_en": "School of Geography and Art comprehensive building",
    "longitude": 120.9095,
    "latitude": 31.9742,
    "floor_count": 6,
    "area": 8709,
    "build_year": 2019,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },  {
    "id": 14,
    "name": "学生公寓1号楼",
    "type": "宿舍建筑",
    "description": "学生公寓第一栋，本科生宿舍",
    "name_en": "Student Dormitory Building No.1",
    "type_en": "Dormitory Building",
    "description_en": "Student Dormitory Building No.1, undergraduate dormitory",
    "longitude": 120.91,
    "latitude": 31.976,
    "floor_count": 6,
    "area": 3216,
    "build_year": 2015,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 15,
    "name": "学生公寓2号楼",
    "type": "宿舍建筑",
    "description": "学生公寓第二栋，本科生宿舍",
    "name_en": "Student Dormitory Building No.2",
    "type_en": "Dormitory Building",
    "description_en": "Student Dormitory Building No.2, undergraduate dormitory",
    "longitude": 120.9102,
    "latitude": 31.9762,
    "floor_count": 6,
    "area": 3215,
    "build_year": 2015,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 16,
    "name": "学生公寓3号楼",
    "type": "宿舍建筑",
    "description": "学生公寓第三栋，本科生宿舍",
    "name_en": "Student Dormitory Building No.3",
    "type_en": "Dormitory Building",
    "description_en": "Student Dormitory Building No.3, undergraduate dormitory",
    "longitude": 120.9104,
    "latitude": 31.9764,
    "floor_count": 6,
    "area": 3215,
    "build_year": 2015,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },  {
    "id": 17,
    "name": "学生公寓32号楼",
    "type": "宿舍建筑",
    "description": "学生公寓32号楼，研究生宿舍",
    "name_en": "Student Dormitory Building No.32",
    "type_en": "Dormitory Building",
    "description_en": "Student Dormitory Building No.32, graduate dormitory",
    "longitude": 120.9098,
    "latitude": 31.9758,
    "floor_count": 6,
    "area": 2511,
    "build_year": 2020,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 18,
    "name": "学生公寓33号楼",
    "type": "宿舍建筑",
    "description": "学生公寓33号楼，研究生宿舍",
    "name_en": "Student Dormitory Building No.33",
    "type_en": "Dormitory Building",
    "description_en": "Student Dormitory Building No.33, graduate dormitory",
    "longitude": 120.9096,
    "latitude": 31.9756,
    "floor_count": 6,
    "area": 2467,
    "build_year": 2020,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 19,
    "name": "研究生公寓、留学生公寓",
    "type": "宿舍建筑",
    "description": "研究生公寓、留学生公寓",
    "name_en": "Graduate & International Student Dormitory",
    "type_en": "Dormitory Building",
    "description_en": "Graduate and international student dormitory",
    "longitude": 120.911,
    "latitude": 31.9765,
    "floor_count": 8,
    "area": 3174,
    "build_year": 2021,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 20,
    "name": "青年教师公寓",
    "type": "宿舍建筑",
    "description": "青年教师公寓，教师住宿",
    "name_en": "Young Faculty Apartment",
    "type_en": "Dormitory Building",
    "description_en": "Young faculty apartment, teacher accommodation",
    "longitude": 120.9108,
    "latitude": 31.9768,
    "floor_count": 6,
    "area": 2506,
    "build_year": 2019,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },  {
    "id": 21,
    "name": "一食堂",
    "type": "食堂",
    "description": "第一学生食堂，主要用餐场所",
    "name_en": "Dining Hall No.1",
    "type_en": "Dining Hall",
    "description_en": "First student dining hall, main dining facility",
    "longitude": 120.9092,
    "latitude": 31.9748,
    "floor_count": 3,
    "area": 4852,
    "build_year": 2009,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 22,
    "name": "二食堂",
    "type": "食堂",
    "description": "第二学生食堂，师生用餐场所",
    "name_en": "Dining Hall No.2",
    "type_en": "Dining Hall",
    "description_en": "Second student dining hall, dining facility for students and faculty",
    "longitude": 120.9088,
    "latitude": 31.9746,
    "floor_count": 3,
    "area": 7320,
    "build_year": 2010,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 23,
    "name": "三食堂",
    "type": "食堂",
    "description": "第三学生食堂，特色餐饮",
    "name_en": "Dining Hall No.3",
    "type_en": "Dining Hall",
    "description_en": "Third student dining hall, specialty dining",
    "longitude": 120.9094,
    "latitude": 31.975,
    "floor_count": 3,
    "area": 7223,
    "build_year": 2011,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },  {
    "id": 24,
    "name": "体育馆",
    "type": "体育建筑",
    "description": "综合性体育场馆，室内运动中心",
    "name_en": "Gymnasium",
    "type_en": "Sports Building",
    "description_en": "Comprehensive sports facility, indoor sports center",
    "longitude": 120.9065,
    "latitude": 31.9735,
    "floor_count": 3,
    "area": 13922,
    "build_year": 2012,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 25,
    "name": "体育中心、操场看台",
    "type": "体育建筑",
    "description": "体育中心，操场看台",
    "name_en": "Sports Center & Stadium Stands",
    "type_en": "Sports Building",
    "description_en": "Sports center and stadium stands",
    "longitude": 120.906,
    "latitude": 31.973,
    "floor_count": 2,
    "area": 3038,
    "build_year": 2013,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 26,
    "name": "机械工程学院",
    "type": "教学建筑",
    "description": "机械工程学院教学楼",
    "name_en": "School of Mechanical Engineering",
    "type_en": "Teaching Building",
    "description_en": "School of Mechanical Engineering building",
    "longitude": 120.9068,
    "latitude": 31.9758,
    "floor_count": 6,
    "area": 2932,
    "build_year": 2016,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 27,
    "name": "电气工程学院",
    "type": "教学建筑",
    "description": "电气工程学院教学楼",
    "name_en": "School of Electrical Engineering",
    "type_en": "Teaching Building",
    "description_en": "School of Electrical Engineering building",
    "longitude": 120.9072,
    "latitude": 31.976,
    "floor_count": 8,
    "area": 4365,
    "build_year": 2017,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },  {
    "id": 28,
    "name": "交通与土木工程学院",
    "type": "教学建筑",
    "description": "交通与土木工程学院",
    "name_en": "School of Transportation & Civil Engineering",
    "type_en": "Teaching Building",
    "description_en": "School of Transportation and Civil Engineering",
    "longitude": 120.9074,
    "latitude": 31.9762,
    "floor_count": 5,
    "area": 2070,
    "build_year": 2018,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 29,
    "name": "纺化楼",
    "type": "教学建筑",
    "description": "纺织化学工程学院",
    "name_en": "Textile & Chemical Engineering Building",
    "type_en": "Teaching Building",
    "description_en": "School of Textile and Chemical Engineering",
    "longitude": 120.9076,
    "latitude": 31.9764,
    "floor_count": 8,
    "area": 4671,
    "build_year": 2014,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 30,
    "name": "校医院",
    "type": "服务建筑",
    "description": "校医院，医疗服务中心",
    "name_en": "University Hospital",
    "type_en": "Service Building",
    "description_en": "University hospital, medical service center",
    "longitude": 120.9086,
    "latitude": 31.9741,
    "floor_count": 2,
    "area": 661,
    "build_year": 2008,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },  {
    "id": 31,
    "name": "工程训练中心",
    "type": "服务建筑",
    "description": "工程训练中心，实践教学基地",
    "name_en": "Engineering Training Center",
    "type_en": "Service Building",
    "description_en": "Engineering training center, practical teaching base",
    "longitude": 120.9058,
    "latitude": 31.9738,
    "floor_count": 4,
    "area": 8249,
    "build_year": 2015,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 32,
    "name": "校园服务中心",
    "type": "行政建筑",
    "description": "校园服务中心，综合服务大厅",
    "name_en": "Campus Service Center",
    "type_en": "Administrative Building",
    "description_en": "Campus service center, comprehensive service hall",
    "longitude": 120.9083,
    "latitude": 31.9743,
    "floor_count": 3,
    "area": 1404,
    "build_year": 2019,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 33,
    "name": "东操场",
    "type": "体育建筑",
    "description": "东操场，田径运动场",
    "name_en": "East Sports Field",
    "type_en": "Sports Building",
    "description_en": "East sports field, track and field stadium",
    "longitude": 120.9105,
    "latitude": 31.975,
    "floor_count": 1,
    "area": 20727,
    "build_year": 2008,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  },
  {
    "id": 34,
    "name": "西操场",
    "type": "体育建筑",
    "description": "西操场，足球场",
    "name_en": "West Sports Field",
    "type_en": "Sports Building",
    "description_en": "West sports field, football stadium",
    "longitude": 120.9055,
    "latitude": 31.9732,
    "floor_count": 1,
    "area": 21488,
    "build_year": 2008,
    "status": "active",
    "created_at": "2025-07-13 17:09:28"
  }
];
// 将数据转换为GeoJSON格式的函数（支持多语言）
window.convertToGeoJSON = function(buildingsArray, language = 'zh') {
    return {
        "type": "FeatureCollection",
        "features": buildingsArray.map(building => ({
            "type": "Feature",
            "geometry": {
                "type": "Point",
                "coordinates": [building.longitude, building.latitude]
            },
            "properties": {
                "id": building.id,
                "name": language === 'en' ? (building.name_en || building.name) : building.name,
                "type": language === 'en' ? (building.type_en || building.type) : building.type,
                "description": language === 'en' ? (building.description_en || building.description) : building.description,
                "floor_count": building.floor_count,
                "area": building.area,
                "build_year": building.build_year,
                "status": building.status,
                "created_at": building.created_at,
                // 保留原始双语数据
                "name_zh": building.name,
                "name_en": building.name_en,
                "type_zh": building.type,
                "type_en": building.type_en,
                "description_zh": building.description,
                "description_en": building.description_en
            }
        }))
    };
};

// 获取建筑物GeoJSON数据（支持多语言）
window.getBuildingsGeoJSON = function(language = 'zh') {
    return window.convertToGeoJSON(window.buildingsData, language);
};

// 根据类型筛选建筑物（支持多语言）
window.getBuildingsByType = function(type, language = 'zh') {
    return window.buildingsData.filter(building => {
        const buildingType = language === 'en' ? (building.type_en || building.type) : building.type;
        return buildingType === type;
    });
};

// 根据ID查找建筑物（返回多语言数据）
window.getBuildingById = function(id) {
    return window.buildingsData.find(building => building.id === id);
};

// 搜索建筑物（支持多语言搜索）
window.searchBuildings = function(keyword, language = 'zh') {
    if (!keyword) return window.buildingsData;
    const lowerKeyword = keyword.toLowerCase();
    
    return window.buildingsData.filter(building => {
        if (language === 'en') {
            // 英文模式：优先搜索英文字段，同时搜索中文字段作为备选
            return (building.name_en && building.name_en.toLowerCase().includes(lowerKeyword)) ||
                   (building.type_en && building.type_en.toLowerCase().includes(lowerKeyword)) ||
                   (building.description_en && building.description_en.toLowerCase().includes(lowerKeyword)) ||
                   (building.name && building.name.toLowerCase().includes(lowerKeyword)) ||
                   (building.type && building.type.toLowerCase().includes(lowerKeyword)) ||
                   (building.description && building.description.toLowerCase().includes(lowerKeyword));
        } else {
            // 中文模式：优先搜索中文字段，同时搜索英文字段作为备选
            return (building.name && building.name.toLowerCase().includes(lowerKeyword)) ||
                   (building.type && building.type.toLowerCase().includes(lowerKeyword)) ||
                   (building.description && building.description.toLowerCase().includes(lowerKeyword)) ||
                   (building.name_en && building.name_en.toLowerCase().includes(lowerKeyword)) ||
                   (building.type_en && building.type_en.toLowerCase().includes(lowerKeyword)) ||
                   (building.description_en && building.description_en.toLowerCase().includes(lowerKeyword));
        }
    });
};

// 获取建筑物类型统计（支持多语言）
window.getBuildingTypeStats = function(language = 'zh') {
    const stats = {};
    window.buildingsData.forEach(building => {
        const buildingType = language === 'en' ? (building.type_en || building.type) : building.type;
        stats[buildingType] = (stats[buildingType] || 0) + 1;
    });
    return stats;
};

// 获取建筑物的本地化信息
window.getLocalizedBuilding = function(building, language = 'zh') {
    if (!building) return null;
    
    return {
        ...building,
        name: language === 'en' ? (building.name_en || building.name) : building.name,
        type: language === 'en' ? (building.type_en || building.type) : building.type,
        description: language === 'en' ? (building.description_en || building.description) : building.description
    };
};

// 建筑物类型映射表
window.buildingTypeMapping = {
    // 中文到英文
    '教学建筑': 'Teaching Building',
    '宿舍建筑': 'Dormitory Building',
    '食堂': 'Dining Hall',
    '体育建筑': 'Sports Building',
    '服务建筑': 'Service Building',
    '行政建筑': 'Administrative Building',
    '公共建筑': 'Public Building',
    
    // 英文到中文
    'Teaching Building': '教学建筑',
    'Dormitory Building': '宿舍建筑',
    'Dining Hall': '食堂',
    'Sports Building': '体育建筑',
    'Service Building': '服务建筑',
    'Administrative Building': '行政建筑',
    'Public Building': '公共建筑'
};

console.log('✅ 双语建筑物数据已加载:', window.buildingsData.length, '个建筑物');
console.log('📊 中文建筑物类型统计:', window.getBuildingTypeStats('zh'));
console.log('📊 英文建筑物类型统计:', window.getBuildingTypeStats('en'));
console.log('🌐 多语言建筑物系统已激活');